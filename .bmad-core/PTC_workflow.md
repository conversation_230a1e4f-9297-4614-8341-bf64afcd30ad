# 🌱 Plant Tissue Culture Management Workflow

This workflow covers activities from **Culture Initiation → Acclimatization**, with clear differentiation between **Culture** and **Subculture**.

---

## 1. Culture Initiation (New Culture)
**Trigger:** New explant introduced.  

**Data to Capture:**
- Species / Variety  
- Explant type (leaf, shoot tip, node, etc.)  
- Source plant ID (mother plant reference)  
- Date of initiation  
- Medium composition (with hormone concentrations)  
- Vessel ID (QR/Barcode generated by app)  
- Initial conditions (light, temp, photoperiod)  

**System Behavior:**
- Assign **Culture ID** (root record in database)  
- Start tracking lineage tree  
- Reminders for contamination check & growth monitoring  

---

## 2. Culture Monitoring
**Regular Observations:**
- Date of observation  
- Contamination (Yes/No + notes/photo)  
- Survival status (# alive / # dead)  
- Growth stage (callus → shoot → root)  

**System Behavior:**
- Flag contaminated → mark for disposal  
- Update progress timeline for each culture  
- Generate alert if subculture is needed (time interval or manual input)  

---

## 3. Subculture Management (Propagation / Transfer)
**Trigger:** Culture needs multiplication or fresh medium.  

**Data to Capture:**
- Parent Culture ID → Child Subculture ID  
- Date of subculture  
- New medium composition  
- Number of explants transferred  
- Operator name/ID  
- Vessel ID(s) for each subculture  

**System Behavior:**
- Label clearly as **“Subculture”**  
- Maintain **lineage mapping**:
Culture A → Subculture A1, A2, A3

- Each subculture batch tracked separately but linked to parent  
- Auto-generate new QR/Barcode for subculture vessels  

---

## 4. Rooting & Pre-Acclimatization
**Data to Capture:**
- Date of transfer to rooting medium  
- Root development stage (scale/notes)  
- Number of plantlets ready for acclimatization  

**System Behavior:**
- Tag culture/subculture batch as “Ready for Acclimatization”  
- Generate reminders for transfer to greenhouse  

---

## 5. Acclimatization (Hardening)
**Data to Capture:**
- Date of transfer from lab to acclimatization area  
- Batch size (number of plantlets transferred)  
- Survival rate (week 1, week 2, etc.)  
- Acclimatization conditions (humidity, shading, substrate)  

**System Behavior:**
- Track batch survival curve  
- Mark final survival count → **“Successfully Acclimatized”**  
- Provide batch-level reports (initiation → acclimatization outcome)  

---

## 🔑 Differentiating Culture vs Subculture in App

1. **Record Type**
 - **Culture** → linked to **Explant Source** (mother plant).  
 - **Subculture** → linked to a **Parent Culture ID**.  

2. **ID System**
 - Example:  
   - Culture ID: `R001-C1` (Rose, first culture)  
   - Subculture IDs: `R001-C1-S1`, `R001-C1-S2`  

3. **Lineage Tree View**

Mother Plant → Culture C1 → Subculture S1 → Subculture S1.1 …

4. **Dashboard Filters**
- Toggle between **Cultures** (original initiations) and **Subcultures** (propagation steps).  

---
