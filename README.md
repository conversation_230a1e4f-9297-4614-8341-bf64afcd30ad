# CultureStack Android

A native Android application for tissue culture management, built with Jetpack Compose and following Material Design 3 guidelines.

## Project Status

✅ **Story 1.1 Complete** - Android App Setup and Basic Navigation
- Multi-module Android project structure
- Bottom navigation with 5 tabs (Timeline, Calendar, Add, Library, Settings)
- Material Design 3 theme with CultureStack branding
- Room database with SQLCipher encryption
- Hilt dependency injection
- Comprehensive permissions setup

## Architecture

This project follows a multi-module architecture with clean architecture principles:

```
CultureStack-Android/
├── app/                    # Main application module
├── core/                   # Core shared modules
│   ├── common/            # Common utilities and constants
│   ├── database/          # Room database and entities
│   ├── network/           # Network layer and APIs
│   └── ui/                # Shared UI components and theme
├── feature/               # Feature-specific modules
│   ├── cultures/          # Culture management
│   ├── recipes/           # Recipe library
│   ├── observations/      # Observation tracking
│   └── auth/              # Authentication
└── shared/                # Cross-cutting concerns
    ├── testing/           # Test utilities
    └── analytics/         # Analytics tracking
```

## Tech Stack

- **Language**: Kotlin 1.9.10+
- **UI Framework**: Jetpack Compose 1.5.0+
- **Architecture**: MVVM with Clean Architecture
- **Database**: Room 2.5.0+ with SQLCipher 4.5.0+
- **Dependency Injection**: Hilt 2.47+
- **Navigation**: Compose Navigation
- **Theme**: Material Design 3
- **Build System**: Gradle 8.0+

## Features Implemented

### ✅ Navigation Structure
- Bottom navigation with 5 tabs
- Compose navigation with proper state management
- Deep linking support preparation

### ✅ Material Design 3 Theme
- Custom color scheme based on green/earth tones
- Complete typography scale
- Consistent shape system
- Accessibility compliance

### ✅ Database Layer
- Room database with SQLCipher encryption
- Culture and Subculture entities
- Comprehensive DAOs with Flow-based reactive queries
- Migration strategy setup

### ✅ Permissions & Security
- Camera, storage, and network permissions
- Encrypted database storage
- Secure backup configuration
- File provider setup for photo sharing

## Getting Started

### Prerequisites
- Android Studio Electric Eel (2022.1.1) or newer
- JDK 11 or newer
- Android SDK API 24-34
- Git

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/terranoss/CultureStack.BMAD.git
   cd CultureStack.BMAD
   ```

2. **Initialize Gradle Wrapper** (if not present)
   ```bash
   gradle wrapper --gradle-version 8.0
   ```

3. **Build the project**
   ```bash
   ./gradlew build
   ```

4. **Run tests**
   ```bash
   ./gradlew test
   ```

5. **Install on device/emulator**
   ```bash
   ./gradlew installDebug
   ```

### Development Environment

- **Minimum Android Version**: API 24 (Android 7.0)
- **Target Android Version**: API 34 (Android 14)
- **Compile SDK**: 34

## Project Structure Details

### Core Modules

- **core/common**: Shared utilities, constants, and extensions
- **core/database**: Room database, entities, DAOs, and type converters
- **core/network**: HTTP client, API services, and network utilities
- **core/ui**: Shared UI components, theme, and design system

### Feature Modules

Each feature module follows clean architecture with:
- **data**: Repository implementations and data sources
- **domain**: Use cases, repository interfaces, and domain models
- **presentation**: UI components, ViewModels, and navigation

### Testing Strategy

- **Unit Tests**: 70% coverage focusing on ViewModels and business logic
- **Integration Tests**: 20% for database and component integration  
- **UI Tests**: 10% for critical navigation flows

## Current Screens

All screens currently show placeholder content with "Coming Soon" messages:

1. **Timeline**: Main culture timeline view with CultureStack branding
2. **Calendar**: Culture scheduling and event management
3. **Add**: Create new cultures, observations, and recipes
4. **Library**: Recipe and protocol library
5. **Settings**: App configuration and user preferences

## Next Steps

The foundation is complete and ready for feature development:

1. Implement culture creation and management
2. Add photo capture and observation tracking
3. Build recipe library functionality
4. Integrate Google Drive sync
5. Add authentication and user management

## Contributing

This project follows the BMAD™ Core development methodology. See the `docs/` directory for detailed architecture and development guidelines.

## License

Copyright © 2023 CultureStack. All rights reserved.
