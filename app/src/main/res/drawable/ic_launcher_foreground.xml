<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Simple flask/beaker icon representing tissue culture -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M40,30 L40,45 L30,70 L78,70 L68,45 L68,30 L60,30 L60,25 L48,25 L48,30 Z M45,35 L63,35 L63,45 L70,65 L38,65 L45,45 Z" />
    
    <!-- Small circle representing culture sample -->
    <circle
        android:fillColor="#81C784"
        android:cx="54"
        android:cy="55"
        android:radius="8" />
</vector>
