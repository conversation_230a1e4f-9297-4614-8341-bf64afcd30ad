<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 spacing scale -->
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xxl">48dp</dimen>
    
    <!-- Component dimensions -->
    <dimen name="card_elevation">2dp</dimen>
    <dimen name="card_elevation_pressed">4dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    
    <!-- Button dimensions -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="fab_size">56dp</dimen>
    
    <!-- Text field dimensions -->
    <dimen name="text_field_height">56dp</dimen>
    <dimen name="text_field_corner_radius">8dp</dimen>
    
    <!-- Icon dimensions -->
    <dimen name="icon_size_sm">16dp</dimen>
    <dimen name="icon_size_md">24dp</dimen>
    <dimen name="icon_size_lg">32dp</dimen>
    <dimen name="icon_size_xl">48dp</dimen>
    
    <!-- Navigation dimensions -->
    <dimen name="bottom_nav_height">80dp</dimen>
    <dimen name="top_app_bar_height">64dp</dimen>
    
    <!-- Minimum touch target size for accessibility -->
    <dimen name="min_touch_target">48dp</dimen>
    
    <!-- Screen margins -->
    <dimen name="screen_margin_horizontal">16dp</dimen>
    <dimen name="screen_margin_vertical">16dp</dimen>
</resources>
