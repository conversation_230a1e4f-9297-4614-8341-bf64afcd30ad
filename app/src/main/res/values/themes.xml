<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme for CultureStack -->
    <style name="Theme.CultureStack" parent="Theme.Material3.DayNight">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        
        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Window background -->
        <item name="android:windowBackground">@color/background</item>
    </style>
    
    <!-- Splash screen theme (for future use) -->
    <style name="Theme.CultureStack.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/primary</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="postSplashScreenTheme">@style/Theme.CultureStack</item>
    </style>
</resources>
