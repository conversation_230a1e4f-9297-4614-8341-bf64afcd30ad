plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.hilt)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "com.culturestack.core.database"
}

dependencies {
    // Core modules
    implementation(project(":core:common"))

    // Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlinx.serialization.json)

    // Android Core
    implementation(libs.android.core.ktx)

    // Database
    implementation(libs.bundles.room)
    implementation(libs.sqlcipher)
    kapt(libs.room.compiler)

    // Dependency Injection
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    // Testing
    testImplementation(libs.bundles.testing)
    testImplementation(libs.room.testing)
}

kapt {
    correctErrorTypes = true
}
