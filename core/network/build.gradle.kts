plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.hilt)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "com.culturestack.core.network"
}

dependencies {
    // Core modules
    implementation(project(":core:common"))

    // Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlinx.serialization.json)

    // Android Core
    implementation(libs.android.core.ktx)

    // Networking
    implementation(libs.bundles.networking)

    // Google Services
    implementation(libs.play.services.auth)
    implementation(libs.play.services.drive)

    // Dependency Injection
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    // Testing
    testImplementation(libs.bundles.testing)
}

kapt {
    correctErrorTypes = true
}
