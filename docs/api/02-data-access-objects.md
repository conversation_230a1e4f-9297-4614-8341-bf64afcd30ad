# Data Access Objects (DAOs)

## Overview
This document defines the database layer APIs using Room DAO interfaces, providing efficient data access patterns for CultureStack's SQLite database operations.

---

## **Culture Data Access Object**

```kotlin
@Dao
interface CultureDao {

    /**
     * Retrieves all active cultures ordered by last update
     * @return Flow of culture list for reactive UI updates
     */
    @Query("""
        SELECT * FROM cultures
        WHERE is_deleted = 0
        ORDER BY updated_at DESC
    """)
    fun getAllActiveCultures(): Flow<List<Culture>>

    /**
     * Retrieves cultures filtered by status
     * @param status Culture status to filter by
     * @return List of cultures matching status
     */
    @Query("""
        SELECT * FROM cultures
        WHERE status = :status AND is_deleted = 0
        ORDER BY updated_at DESC
    """)
    suspend fun getCulturesByStatus(status: CultureStatus): List<Culture>

    /**
     * Retrieves paginated cultures for large datasets
     * @param limit Number of items per page (default: 50)
     * @param offset Starting position for pagination
     * @return List of cultures for current page
     */
    @Query("""
        SELECT * FROM cultures
        WHERE is_deleted = 0
        ORDER BY updated_at DESC
        LIMIT :limit OFFSET :offset
    """)
    suspend fun getCulturesPage(limit: Int = 50, offset: Int = 0): List<Culture>

    /**
     * Searches cultures by species name
     * @param query Search query string
     * @return List of cultures matching search criteria
     */
    @Query("""
        SELECT * FROM cultures
        WHERE species LIKE '%' || :query || '%'
        AND is_deleted = 0
        ORDER BY updated_at DESC
    """)
    suspend fun searchCulturesBySpecies(query: String): List<Culture>

    /**
     * Gets optimized culture timeline data with aggregations
     * @param statuses List of statuses to include
     * @return Timeline items with precomputed counts
     */
    @Query("""
        SELECT c.id, c.culture_id, c.species, c.status, c.updated_at,
               COUNT(s.id) as subculture_count,
               MAX(o.observation_date) as last_observation
        FROM cultures c
        LEFT JOIN subcultures s ON c.id = s.parent_culture_id AND s.is_deleted = 0
        LEFT JOIN observations o ON c.id = o.culture_id AND o.is_deleted = 0
        WHERE c.is_deleted = 0 AND c.status IN (:statuses)
        GROUP BY c.id
        ORDER BY c.updated_at DESC
        LIMIT 50
    """)
    suspend fun getOptimizedCultureTimeline(
        statuses: List<String> = listOf("HEALTHY", "READY_FOR_TRANSFER", "IN_ROOTING")
    ): List<CultureTimelineItem>

    /**
     * Inserts new culture record
     * @param culture Culture entity to insert
     * @return Row ID of inserted culture
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCulture(culture: Culture): Long

    /**
     * Updates existing culture record
     * @param culture Modified culture entity
     */
    @Update
    suspend fun updateCulture(culture: Culture)

    /**
     * Performs soft delete of culture
     * @param id Culture ID to delete
     * @param deletedAt Timestamp of deletion
     */
    @Query("""
        UPDATE cultures
        SET is_deleted = 1, updated_at = :deletedAt
        WHERE id = :id
    """)
    suspend fun softDeleteCulture(id: String, deletedAt: Instant = Instant.now())

    /**
     * Gets culture with full lineage information
     * @param cultureId Root culture ID
     * @return Culture with all subcultures and observations
     */
    @Transaction
    @Query("""
        SELECT * FROM cultures
        WHERE id = :cultureId AND is_deleted = 0
    """)
    suspend fun getCultureWithLineage(cultureId: String): CultureWithLineage?

    /**
     * Updates culture status and timestamp atomically
     * @param id Culture ID
     * @param status New status
     * @param updatedAt Update timestamp
     */
    @Query("""
        UPDATE cultures
        SET status = :status, updated_at = :updatedAt, sync_version = sync_version + 1
        WHERE id = :id
    """)
    suspend fun updateCultureStatus(
        id: String,
        status: CultureStatus,
        updatedAt: Instant = Instant.now()
    )

    /**
     * Batch insert cultures for sync operations
     * @param cultures List of cultures to insert
     * @return List of row IDs
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCultures(cultures: List<Culture>): List<Long>

    /**
     * Gets cultures pending sync
     * @return Cultures with unsynchronized changes
     */
    @Query("""
        SELECT * FROM cultures
        WHERE last_synced_at IS NULL
        OR sync_version > 1
        ORDER BY created_at ASC
    """)
    suspend fun getCulturesPendingSync(): List<Culture>
}
```

## **Recipe Data Access Object**

```kotlin
@Dao
interface RecipeDao {

    /**
     * Retrieves all active recipes ordered by usage count
     * @return Flow of recipe list for reactive updates
     */
    @Query("""
        SELECT * FROM recipes
        WHERE is_deleted = 0
        ORDER BY usage_count DESC, name ASC
    """)
    fun getAllRecipes(): Flow<List<Recipe>>

    /**
     * Searches recipes by name, description, and tags
     * @param query Search query string
     * @return List of matching recipes
     */
    @Query("""
        SELECT r.* FROM recipes r
        JOIN recipe_fts fts ON r.id = fts.recipe_id
        WHERE recipe_fts MATCH :query
        AND r.is_deleted = 0
        ORDER BY r.usage_count DESC
    """)
    suspend fun searchRecipes(query: String): List<Recipe>

    /**
     * Gets recipes filtered by plant type
     * @param plantType Plant type to filter by
     * @return List of recipes suitable for plant type
     */
    @Query("""
        SELECT * FROM recipes
        WHERE plant_types LIKE '%' || :plantType || '%'
        AND is_deleted = 0
        ORDER BY usage_count DESC
    """)
    suspend fun getRecipesByPlantType(plantType: String): List<Recipe>

    /**
     * Gets recipes by difficulty level
     * @param level Difficulty level filter
     * @return List of recipes at specified difficulty
     */
    @Query("""
        SELECT * FROM recipes
        WHERE difficulty_level = :level
        AND is_deleted = 0
        ORDER BY usage_count DESC
    """)
    suspend fun getRecipesByDifficulty(level: DifficultyLevel): List<Recipe>

    /**
     * Gets most popular recipes
     * @param limit Number of recipes to return
     * @return Top recipes by usage count
     */
    @Query("""
        SELECT * FROM recipes
        WHERE is_deleted = 0
        ORDER BY usage_count DESC
        LIMIT :limit
    """)
    suspend fun getPopularRecipes(limit: Int = 10): List<Recipe>

    /**
     * Inserts new recipe
     * @param recipe Recipe entity to insert
     * @return Row ID of inserted recipe
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecipe(recipe: Recipe): Long

    /**
     * Updates recipe information
     * @param recipe Modified recipe entity
     */
    @Update
    suspend fun updateRecipe(recipe: Recipe)

    /**
     * Increments recipe usage count
     * @param id Recipe ID to increment
     */
    @Query("""
        UPDATE recipes
        SET usage_count = usage_count + 1, updated_at = :timestamp
        WHERE id = :id
    """)
    suspend fun incrementUsageCount(id: String, timestamp: Instant = Instant.now())

    /**
     * Gets recipe by ID with usage statistics
     * @param id Recipe ID
     * @return Recipe with usage information
     */
    @Query("""
        SELECT r.*,
               COUNT(c1.id) as culture_usage_count,
               COUNT(c2.id) as subculture_usage_count
        FROM recipes r
        LEFT JOIN cultures c1 ON r.id = c1.recipe_id AND c1.is_deleted = 0
        LEFT JOIN subcultures c2 ON r.id = c2.recipe_id AND c2.is_deleted = 0
        WHERE r.id = :id AND r.is_deleted = 0
        GROUP BY r.id
    """)
    suspend fun getRecipeWithStats(id: String): RecipeWithStats?

    /**
     * Soft delete recipe
     * @param id Recipe ID to delete
     */
    @Query("""
        UPDATE recipes
        SET is_deleted = 1, updated_at = :deletedAt
        WHERE id = :id
    """)
    suspend fun softDeleteRecipe(id: String, deletedAt: Instant = Instant.now())
}
```

## **Observation Data Access Object**

```kotlin
@Dao
interface ObservationDao {

    /**
     * Gets observations for specific culture/subculture
     * @param cultureId Culture or subculture ID
     * @return Flow of observations for reactive updates
     */
    @Query("""
        SELECT * FROM observations
        WHERE culture_id = :cultureId AND is_deleted = 0
        ORDER BY observation_date DESC, created_at DESC
    """)
    fun getObservationsForCulture(cultureId: String): Flow<List<Observation>>

    /**
     * Gets recent observations across all cultures
     * @param limit Number of recent observations
     * @return List of recent observations
     */
    @Query("""
        SELECT o.*, c.culture_id, c.species
        FROM observations o
        JOIN cultures c ON o.culture_id = c.id
        WHERE o.is_deleted = 0 AND c.is_deleted = 0
        ORDER BY o.observation_date DESC, o.created_at DESC
        LIMIT :limit
    """)
    suspend fun getRecentObservations(limit: Int = 20): List<ObservationWithCulture>

    /**
     * Gets observations with contamination detected
     * @return List of contaminated observations
     */
    @Query("""
        SELECT o.*, c.culture_id, c.species
        FROM observations o
        JOIN cultures c ON o.culture_id = c.id
        WHERE o.contamination_status = 1
        AND o.is_deleted = 0 AND c.is_deleted = 0
        ORDER BY o.observation_date DESC
    """)
    suspend fun getContaminationObservations(): List<ObservationWithCulture>

    /**
     * Inserts new observation
     * @param observation Observation entity to insert
     * @return Row ID of inserted observation
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertObservation(observation: Observation): Long

    /**
     * Updates existing observation
     * @param observation Modified observation entity
     */
    @Update
    suspend fun updateObservation(observation: Observation)

    /**
     * Gets observation statistics for culture
     * @param cultureId Culture ID
     * @return Aggregated observation statistics
     */
    @Query("""
        SELECT
            COUNT(*) as total_observations,
            SUM(CASE WHEN contamination_status = 1 THEN 1 ELSE 0 END) as contamination_count,
            AVG(CASE
                WHEN survival_status = 'EXCELLENT' THEN 4
                WHEN survival_status = 'GOOD' THEN 3
                WHEN survival_status = 'FAIR' THEN 2
                WHEN survival_status = 'POOR' THEN 1
                ELSE 0 END
            ) as avg_survival_rating,
            MAX(observation_date) as last_observation_date
        FROM observations
        WHERE culture_id = :cultureId AND is_deleted = 0
    """)
    suspend fun getObservationStats(cultureId: String): ObservationStats?

    /**
     * Gets observations in date range
     * @param startDate Range start date
     * @param endDate Range end date
     * @return Observations within date range
     */
    @Query("""
        SELECT * FROM observations
        WHERE observation_date BETWEEN :startDate AND :endDate
        AND is_deleted = 0
        ORDER BY observation_date DESC
    """)
    suspend fun getObservationsInRange(
        startDate: LocalDate,
        endDate: LocalDate
    ): List<Observation>
}
```

## **Query Optimization Strategies**

### **Indexing Strategy**
Essential indexes for performance:
```sql
-- Culture table indexes
CREATE INDEX idx_cultures_status ON cultures(status, is_deleted, updated_at);
CREATE INDEX idx_cultures_species ON cultures(species, is_deleted);
CREATE INDEX idx_cultures_sync ON cultures(last_synced_at, sync_version);

-- Recipe table indexes
CREATE INDEX idx_recipes_usage ON recipes(usage_count DESC, is_deleted);
CREATE INDEX idx_recipes_plant_type ON recipes(plant_types, is_deleted);

-- Observation table indexes
CREATE INDEX idx_observations_culture_date ON observations(culture_id, observation_date DESC, is_deleted);
CREATE INDEX idx_observations_contamination ON observations(contamination_status, is_deleted);
```

### **Pagination Patterns**
Implement efficient pagination for large result sets:
```kotlin
data class PaginationRequest(
    val page: Int = 0,
    val size: Int = 50
) {
    val offset: Int get() = page * size
    val limit: Int get() = size
}
```

### **Batch Operations**
Use batch operations for bulk inserts/updates:
```kotlin
@Insert(onConflict = OnConflictStrategy.REPLACE)
suspend fun insertCultures(cultures: List<Culture>): List<Long>

@Update
suspend fun updateCultures(cultures: List<Culture>)
```

## **Performance Monitoring**

### **Query Performance Metrics**
Track query execution times:
```kotlin
class QueryPerformanceInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val startTime = System.currentTimeMillis()
        val response = chain.proceed(chain.request())
        val endTime = System.currentTimeMillis()

        logQueryPerformance(chain.request().url.toString(), endTime - startTime)
        return response
    }
}
```

### **Database Size Monitoring**
Monitor database growth and cleanup:
```kotlin
@Query("SELECT COUNT(*) FROM cultures WHERE is_deleted = 0")
suspend fun getActiveCultureCount(): Int

@Query("SELECT COUNT(*) FROM cultures WHERE is_deleted = 1")
suspend fun getDeletedCultureCount(): Int
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After performance testing and optimization