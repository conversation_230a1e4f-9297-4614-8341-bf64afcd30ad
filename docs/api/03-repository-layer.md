# Repository Layer APIs

## Overview
This document defines the repository pattern implementations that abstract data access and provide business-focused APIs with validation, caching, and error handling.

---

## **Culture Repository Interface**

```kotlin
interface CultureRepository {

    /**
     * Creates new culture record
     * @param request Culture creation request with validation
     * @return Result containing created culture or error
     */
    suspend fun createCulture(request: CreateCultureRequest): Result<Culture>

    /**
     * Gets culture by unique identifier
     * @param id Culture ID
     * @return Result containing culture or null if not found
     */
    suspend fun getCultureById(id: String): Result<Culture?>

    /**
     * Gets all active cultures as flow for UI reactivity
     * @return Flow of culture lists
     */
    fun getActiveCultures(): Flow<List<Culture>>

    /**
     * Gets paginated cultures for performance
     * @param page Page number (0-based)
     * @param pageSize Items per page
     * @return Result containing culture page data
     */
    suspend fun getCulturesPaginated(
        page: Int = 0,
        pageSize: Int = 50
    ): Result<PaginatedResult<Culture>>

    /**
     * Searches cultures with multiple criteria
     * @param query Search criteria object
     * @return Result containing matching cultures
     */
    suspend fun searchCultures(query: CultureSearchQuery): Result<List<Culture>>

    /**
     * Updates culture status with business logic validation
     * @param id Culture ID
     * @param status New status
     * @param notes Optional status change notes
     * @return Result indicating success or validation errors
     */
    suspend fun updateCultureStatus(
        id: String,
        status: CultureStatus,
        notes: String? = null
    ): Result<Unit>

    /**
     * Creates subculture from parent culture
     * @param request Subculture creation request
     * @return Result containing created subculture
     */
    suspend fun createSubculture(request: CreateSubcultureRequest): Result<Subculture>

    /**
     * Gets complete culture lineage tree
     * @param rootCultureId Root culture ID
     * @return Result containing lineage tree structure
     */
    suspend fun getCultureLineage(rootCultureId: String): Result<CultureLineage>

    /**
     * Performs soft delete of culture and related data
     * @param id Culture ID to delete
     * @param cascade Whether to delete subcultures
     * @return Result indicating success or constraints
     */
    suspend fun deleteCulture(id: String, cascade: Boolean = false): Result<Unit>

    /**
     * Gets cultures requiring user attention
     * @return Result containing cultures needing action
     */
    suspend fun getCulturesRequiringAttention(): Result<List<CultureAlert>>

    /**
     * Exports culture data in specified format
     * @param cultureIds List of culture IDs to export
     * @param format Export format (CSV, JSON, PDF)
     * @return Result containing export file information
     */
    suspend fun exportCultures(
        cultureIds: List<String>,
        format: ExportFormat
    ): Result<ExportResult>
}
```

## **Culture Repository Implementation**

```kotlin
@Singleton
class CultureRepositoryImpl @Inject constructor(
    private val cultureDao: CultureDao,
    private val subcultureDao: SubcultureDao,
    private val observationDao: ObservationDao,
    private val validationService: ValidationService,
    private val eventBus: EventBus
) : CultureRepository {

    override suspend fun createCulture(request: CreateCultureRequest): Result<Culture> =
        withContext(Dispatchers.IO) {
            try {
                // Validate request
                val validationResult = validationService.validateCultureCreation(request)
                if (!validationResult.isValid) {
                    return@withContext Result.failure(
                        ValidationException(validationResult.errors)
                    )
                }

                // Generate unique culture ID
                val cultureId = generateUniqueCultureId()

                // Create culture entity
                val culture = Culture(
                    id = UUID.randomUUID().toString(),
                    cultureId = cultureId,
                    species = request.species,
                    explantType = request.explantType,
                    sourcePlantId = request.sourcePlantId,
                    initiationDate = request.initiationDate,
                    mediumComposition = request.mediumComposition,
                    recipeId = request.recipeId,
                    initialConditions = request.initialConditions,
                    status = CultureStatus.HEALTHY,
                    deviceId = DeviceInfoProvider.getDeviceId()
                )

                // Insert into database
                val rowId = cultureDao.insertCulture(culture)

                // Increment recipe usage if recipe was used
                request.recipeId?.let { recipeId ->
                    recipeDao.incrementUsageCount(recipeId)
                }

                // Publish creation event
                eventBus.publish(CultureCreatedEvent(culture))

                Result.success(culture)

            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override suspend fun getCultureById(id: String): Result<Culture?> =
        withContext(Dispatchers.IO) {
            try {
                val culture = cultureDao.getCultureById(id)
                Result.success(culture)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

    override fun getActiveCultures(): Flow<List<Culture>> =
        cultureDao.getAllActiveCultures()

    override suspend fun getCulturesPaginated(
        page: Int,
        pageSize: Int
    ): Result<PaginatedResult<Culture>> = withContext(Dispatchers.IO) {
        try {
            val offset = page * pageSize
            val cultures = cultureDao.getCulturesPage(pageSize, offset)
            val totalCount = cultureDao.getTotalCultureCount()
            val hasMore = (offset + pageSize) < totalCount

            val result = PaginatedResult(
                items = cultures,
                page = page,
                pageSize = pageSize,
                totalCount = totalCount,
                hasMore = hasMore
            )

            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateCultureStatus(
        id: String,
        status: CultureStatus,
        notes: String?
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // Get current culture for validation
            val culture = cultureDao.getCultureById(id)
                ?: return@withContext Result.failure(
                    NotFoundException("Culture not found: $id")
                )

            // Validate status transition
            val transitionValid = validationService.validateStatusTransition(
                culture.status, status
            )
            if (!transitionValid) {
                return@withContext Result.failure(
                    ValidationException(listOf("Invalid status transition"))
                )
            }

            // Update status
            cultureDao.updateCultureStatus(id, status)

            // Create status change observation if notes provided
            notes?.let { statusNotes ->
                val observation = Observation(
                    id = UUID.randomUUID().toString(),
                    cultureId = id,
                    observationDate = LocalDate.now(),
                    notes = "Status changed to $status: $statusNotes",
                    contaminationStatus = false,
                    survivalStatus = mapStatusToSurvival(status),
                    growthStage = mapStatusToGrowthStage(status)
                )
                observationDao.insertObservation(observation)
            }

            // Publish status change event
            eventBus.publish(
                CultureStatusChangedEvent(
                    cultureId = id,
                    oldStatus = culture.status,
                    newStatus = status,
                    reason = notes
                )
            )

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun generateUniqueCultureId(): String {
        val prefix = "CUL"
        val timestamp = System.currentTimeMillis().toString().takeLast(6)
        val random = (1000..9999).random()
        return "$prefix$timestamp$random"
    }

    private fun mapStatusToSurvival(status: CultureStatus): SurvivalStatus =
        when (status) {
            CultureStatus.HEALTHY -> SurvivalStatus.EXCELLENT
            CultureStatus.READY_FOR_TRANSFER -> SurvivalStatus.GOOD
            CultureStatus.IN_ROOTING -> SurvivalStatus.GOOD
            CultureStatus.CONTAMINATED -> SurvivalStatus.POOR
            CultureStatus.DISPOSED -> SurvivalStatus.POOR
        }
}
```

## **Validation Service**

```kotlin
interface ValidationService {
    fun validateCultureCreation(request: CreateCultureRequest): ValidationResult
    fun validateStatusTransition(from: CultureStatus, to: CultureStatus): Boolean
    fun validateObservationData(request: CreateObservationRequest): ValidationResult
}

@Singleton
class ValidationServiceImpl @Inject constructor() : ValidationService {

    override fun validateCultureCreation(request: CreateCultureRequest): ValidationResult {
        val errors = mutableListOf<String>()

        // Species validation
        if (request.species.isBlank()) {
            errors.add("Species name is required")
        } else if (request.species.length > 100) {
            errors.add("Species name cannot exceed 100 characters")
        }

        // Date validation
        if (request.initiationDate.isAfter(LocalDate.now())) {
            errors.add("Initiation date cannot be in the future")
        } else if (request.initiationDate.isBefore(LocalDate.now().minusYears(1))) {
            errors.add("Initiation date cannot be more than 1 year ago")
        }

        // Explant type validation
        val validExplantTypes = listOf("Shoot tip", "Node", "Leaf", "Root", "Callus", "Other")
        if (request.explantType !in validExplantTypes) {
            errors.add("Invalid explant type. Valid options: ${validExplantTypes.joinToString()}")
        }

        // Medium composition validation
        if (request.mediumComposition.isBlank()) {
            errors.add("Medium composition is required")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    override fun validateStatusTransition(from: CultureStatus, to: CultureStatus): Boolean {
        val validTransitions = mapOf(
            CultureStatus.HEALTHY to listOf(
                CultureStatus.READY_FOR_TRANSFER,
                CultureStatus.CONTAMINATED,
                CultureStatus.DISPOSED
            ),
            CultureStatus.READY_FOR_TRANSFER to listOf(
                CultureStatus.IN_ROOTING,
                CultureStatus.CONTAMINATED,
                CultureStatus.DISPOSED
            ),
            CultureStatus.IN_ROOTING to listOf(
                CultureStatus.HEALTHY,
                CultureStatus.CONTAMINATED,
                CultureStatus.DISPOSED
            ),
            CultureStatus.CONTAMINATED to listOf(
                CultureStatus.DISPOSED
            ),
            CultureStatus.DISPOSED to emptyList()
        )

        return validTransitions[from]?.contains(to) == true
    }
}

data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
```

## **Caching Strategy**

```kotlin
@Singleton
class CacheManager @Inject constructor() {

    private val cultureCache = LruCache<String, Culture>(maxSize = 100)
    private val recipeCache = LruCache<String, Recipe>(maxSize = 50)

    fun getCulture(id: String): Culture? = cultureCache.get(id)

    fun putCulture(culture: Culture) {
        cultureCache.put(culture.id, culture)
    }

    fun invalidateCulture(id: String) {
        cultureCache.remove(id)
    }

    fun getRecipe(id: String): Recipe? = recipeCache.get(id)

    fun putRecipe(recipe: Recipe) {
        recipeCache.put(recipe.id, recipe)
    }

    fun clear() {
        cultureCache.evictAll()
        recipeCache.evictAll()
    }
}
```

## **Data Models**

```kotlin
data class PaginatedResult<T>(
    val items: List<T>,
    val page: Int,
    val pageSize: Int,
    val totalCount: Int,
    val hasMore: Boolean
) {
    val totalPages: Int = (totalCount + pageSize - 1) / pageSize
}

data class CultureSearchQuery(
    val species: String? = null,
    val status: CultureStatus? = null,
    val dateRange: DateRange? = null,
    val tags: List<String> = emptyList(),
    val sortBy: SortBy = SortBy.UPDATED_AT,
    val sortOrder: SortOrder = SortOrder.DESC
)

data class CultureAlert(
    val cultureId: String,
    val alertType: AlertType,
    val severity: AlertSeverity,
    val message: String,
    val actionRequired: String?,
    val dueDate: LocalDate?
)

enum class AlertType {
    OVERDUE_OBSERVATION,
    OVERDUE_TRANSFER,
    CONTAMINATION_RISK,
    LOW_SURVIVAL_RATE
}

enum class AlertSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

data class ExportResult(
    val filePath: String,
    val format: ExportFormat,
    val recordCount: Int,
    val fileSize: Long,
    val exportedAt: Instant
)

enum class ExportFormat {
    CSV, JSON, PDF
}
```

## **Repository Testing Strategy**

```kotlin
@Test
class CultureRepositoryTest {

    @Mock private lateinit var cultureDao: CultureDao
    @Mock private lateinit var validationService: ValidationService
    @Mock private lateinit var eventBus: EventBus

    private lateinit var repository: CultureRepositoryImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        repository = CultureRepositoryImpl(
            cultureDao, subcultureDao, observationDao, validationService, eventBus
        )
    }

    @Test
    fun `createCulture with valid request returns success`() = runTest {
        // Arrange
        val request = CreateCultureRequest(
            species = "Rosa damascena",
            explantType = "Shoot tip",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS + 2,4-D",
            initialConditions = "25°C, 16h photoperiod"
        )
        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(true, emptyList()))
        whenever(cultureDao.insertCulture(any())).thenReturn(1L)

        // Act
        val result = repository.createCulture(request)

        // Assert
        assertTrue(result.isSuccess)
        verify(eventBus).publish(any<CultureCreatedEvent>())
    }

    @Test
    fun `createCulture with invalid request returns failure`() = runTest {
        // Arrange
        val request = CreateCultureRequest(
            species = "",
            explantType = "Invalid",
            initiationDate = LocalDate.now().plusDays(1),
            mediumComposition = "",
            initialConditions = ""
        )
        val validationErrors = listOf("Species name is required", "Invalid explant type")
        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(false, validationErrors))

        // Act
        val result = repository.createCulture(request)

        // Assert
        assertTrue(result.isFailure)
        assertTrue(result.exceptionOrNull() is ValidationException)
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After repository implementation and testing