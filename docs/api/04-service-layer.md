# Service Layer APIs

## Overview
This document defines the business logic orchestration layer that coordinates repository operations, enforces business rules, and manages complex workflows.

---

## **Culture Service Interface**

```kotlin
interface CultureService {

    /**
     * Orchestrates complete culture creation workflow
     * @param request Culture creation request
     * @return Result containing created culture with workflow status
     */
    suspend fun createCultureWorkflow(request: CreateCultureRequest): Result<CultureWorkflowResult>

    /**
     * Handles culture status transitions with business rules
     * @param cultureId Culture ID
     * @param transition Status transition request
     * @return Result containing transition outcome
     */
    suspend fun transitionCultureStatus(
        cultureId: String,
        transition: StatusTransitionRequest
    ): Result<StatusTransitionResult>

    /**
     * Creates batch subcultures with progress tracking
     * @param request Batch subculture request
     * @return Result containing batch operation handle
     */
    suspend fun createBatchSubcultures(
        request: BatchSubcultureRequest
    ): Result<BatchOperationHandle>

    /**
     * Analyzes culture performance and suggests improvements
     * @param cultureId Culture ID
     * @return Result containing performance analysis
     */
    suspend fun analyzeCulturePerformance(cultureId: String): Result<CultureAnalysis>

    /**
     * Generates culture recommendations based on history
     * @param userId User ID for personalized recommendations
     * @param limit Number of recommendations
     * @return Result containing recommendation list
     */
    suspend fun getCultureRecommendations(
        userId: String,
        limit: Int = 10
    ): Result<List<CultureRecommendation>>

    /**
     * Validates culture data integrity
     * @param cultureId Culture ID
     * @return Result containing validation report
     */
    suspend fun validateCultureIntegrity(cultureId: String): Result<IntegrityReport>
}
```

## **Culture Service Implementation**

```kotlin
@Singleton
class CultureServiceImpl @Inject constructor(
    private val cultureRepository: CultureRepository,
    private val observationRepository: ObservationRepository,
    private val recipeRepository: RecipeRepository,
    private val notificationService: NotificationService,
    private val analyticsService: AnalyticsService,
    private val validationService: ValidationService
) : CultureService {

    override suspend fun createCultureWorkflow(
        request: CreateCultureRequest
    ): Result<CultureWorkflowResult> = withContext(Dispatchers.IO) {

        val workflowSteps = mutableListOf<WorkflowStep>()

        try {
            // Step 1: Validate request
            workflowSteps.add(WorkflowStep("validation", "pending"))
            val validationResult = validationService.validateCultureCreation(request)
            if (!validationResult.isValid) {
                workflowSteps.last().status = "failed"
                return@withContext Result.failure(
                    ValidationException(validationResult.errors)
                )
            }
            workflowSteps.last().status = "completed"

            // Step 2: Create culture record
            workflowSteps.add(WorkflowStep("creation", "pending"))
            val cultureResult = cultureRepository.createCulture(request)
            if (cultureResult.isFailure) {
                workflowSteps.last().status = "failed"
                return@withContext Result.failure(cultureResult.exceptionOrNull()!!)
            }
            val culture = cultureResult.getOrThrow()
            workflowSteps.last().status = "completed"

            // Step 3: Setup initial observation
            if (request.initialObservation != null) {
                workflowSteps.add(WorkflowStep("initial_observation", "pending"))
                val observationRequest = CreateObservationRequest(
                    cultureId = culture.id,
                    observationDate = culture.initiationDate,
                    notes = request.initialObservation,
                    contaminationStatus = false,
                    survivalStatus = SurvivalStatus.GOOD,
                    growthStage = GrowthStage.INITIATION
                )

                val observationResult = observationRepository.createObservation(observationRequest)
                if (observationResult.isSuccess) {
                    workflowSteps.last().status = "completed"
                } else {
                    workflowSteps.last().status = "failed"
                    // Log warning but don't fail entire workflow
                }
            }

            // Step 4: Schedule initial reminders
            workflowSteps.add(WorkflowStep("scheduling", "pending"))
            scheduleInitialReminders(culture)
            workflowSteps.last().status = "completed"

            // Step 5: Track analytics
            workflowSteps.add(WorkflowStep("analytics", "pending"))
            analyticsService.trackCultureCreation(culture)
            workflowSteps.last().status = "completed"

            Result.success(
                CultureWorkflowResult(
                    culture = culture,
                    workflowSteps = workflowSteps,
                    isComplete = true,
                    nextRecommendedActions = generateNextActions(culture)
                )
            )

        } catch (e: Exception) {
            workflowSteps.lastOrNull()?.status = "failed"
            Result.failure(e)
        }
    }

    override suspend fun transitionCultureStatus(
        cultureId: String,
        transition: StatusTransitionRequest
    ): Result<StatusTransitionResult> = withContext(Dispatchers.IO) {
        try {
            // Get current culture
            val cultureResult = cultureRepository.getCultureById(cultureId)
            if (cultureResult.isFailure) {
                return@withContext Result.failure(cultureResult.exceptionOrNull()!!)
            }
            val culture = cultureResult.getOrThrow()
                ?: return@withContext Result.failure(
                    NotFoundException("Culture not found: $cultureId")
                )

            // Validate transition
            val isValidTransition = validationService.validateStatusTransition(
                culture.status, transition.newStatus
            )
            if (!isValidTransition) {
                return@withContext Result.failure(
                    ValidationException(listOf("Invalid status transition"))
                )
            }

            // Execute business rules for specific transitions
            when (transition.newStatus) {
                CultureStatus.CONTAMINATED -> {
                    handleContaminationTransition(culture, transition)
                }
                CultureStatus.READY_FOR_TRANSFER -> {
                    handleReadyForTransferTransition(culture, transition)
                }
                CultureStatus.DISPOSED -> {
                    handleDisposalTransition(culture, transition)
                }
                else -> { /* No special handling */ }
            }

            // Update status
            val updateResult = cultureRepository.updateCultureStatus(
                cultureId, transition.newStatus, transition.notes
            )
            if (updateResult.isFailure) {
                return@withContext Result.failure(updateResult.exceptionOrNull()!!)
            }

            Result.success(
                StatusTransitionResult(
                    cultureId = cultureId,
                    oldStatus = culture.status,
                    newStatus = transition.newStatus,
                    timestamp = Instant.now(),
                    notes = transition.notes,
                    triggeredActions = generateTriggeredActions(culture, transition.newStatus)
                )
            )

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createBatchSubcultures(
        request: BatchSubcultureRequest
    ): Result<BatchOperationHandle> = withContext(Dispatchers.IO) {
        try {
            // Validate parent culture exists and is suitable for subculturing
            val parentResult = cultureRepository.getCultureById(request.parentCultureId)
            if (parentResult.isFailure) {
                return@withContext Result.failure(parentResult.exceptionOrNull()!!)
            }
            val parent = parentResult.getOrThrow()
                ?: return@withContext Result.failure(
                    NotFoundException("Parent culture not found")
                )

            // Check if parent is ready for subculturing
            if (parent.status != CultureStatus.READY_FOR_TRANSFER) {
                return@withContext Result.failure(
                    ValidationException(listOf("Parent culture not ready for transfer"))
                )
            }

            // Create batch operation handle
            val batchId = UUID.randomUUID().toString()
            val handle = BatchOperationHandle(
                batchId = batchId,
                operationType = BatchOperationType.SUBCULTURE_CREATION,
                totalItems = request.count,
                status = BatchStatus.IN_PROGRESS,
                startTime = Instant.now()
            )

            // Start async batch processing
            launchBatchSubcultureCreation(batchId, request)

            Result.success(handle)

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun scheduleInitialReminders(culture: Culture) {
        // Schedule 7-day transfer reminder
        notificationService.scheduleReminder(
            ReminderId.generate(),
            culture.id,
            ReminderType.TRANSFER_CHECK,
            culture.initiationDate.plusDays(7)
        )

        // Schedule 14-day growth assessment
        notificationService.scheduleReminder(
            ReminderId.generate(),
            culture.id,
            ReminderType.GROWTH_ASSESSMENT,
            culture.initiationDate.plusDays(14)
        )
    }

    private fun generateNextActions(culture: Culture): List<RecommendedAction> {
        return listOf(
            RecommendedAction(
                type = ActionType.SCHEDULE_OBSERVATION,
                title = "Schedule first observation",
                description = "Monitor culture progress in 3-5 days",
                dueDate = LocalDate.now().plusDays(4),
                priority = Priority.MEDIUM
            ),
            RecommendedAction(
                type = ActionType.PREPARE_SUBCULTURE,
                title = "Prepare for first subculture",
                description = "Ready medium and tools for subculturing in 2-3 weeks",
                dueDate = LocalDate.now().plusWeeks(2),
                priority = Priority.LOW
            )
        )
    }

    private suspend fun handleContaminationTransition(
        culture: Culture,
        transition: StatusTransitionRequest
    ) {
        // Create contamination observation
        val contaminationObservation = CreateObservationRequest(
            cultureId = culture.id,
            observationDate = LocalDate.now(),
            notes = "Contamination detected: ${transition.notes}",
            contaminationStatus = true,
            survivalStatus = SurvivalStatus.POOR,
            growthStage = GrowthStage.DECLINING
        )
        observationRepository.createObservation(contaminationObservation)

        // Send contamination alert
        notificationService.sendContaminationAlert(culture)

        // Update analytics
        analyticsService.trackContamination(culture.id, transition.notes ?: "")
    }

    private suspend fun launchBatchSubcultureCreation(
        batchId: String,
        request: BatchSubcultureRequest
    ) {
        // This would typically be launched in a background coroutine
        // with progress tracking and error handling
        coroutineScope.launch(Dispatchers.IO) {
            var successCount = 0
            val failures = mutableListOf<BatchFailure>()

            repeat(request.count) { index ->
                try {
                    val subcultureRequest = CreateSubcultureRequest(
                        parentCultureId = request.parentCultureId,
                        subcultureDate = request.subcultureDate,
                        mediumComposition = request.mediumComposition,
                        recipeId = request.recipeId,
                        explantCount = request.explantCountPerSubculture,
                        notes = "${request.notes} (${index + 1}/${request.count})"
                    )

                    val result = cultureRepository.createSubculture(subcultureRequest)
                    if (result.isSuccess) {
                        successCount++
                    } else {
                        failures.add(
                            BatchFailure(
                                index = index,
                                error = result.exceptionOrNull()?.message ?: "Unknown error"
                            )
                        )
                    }
                } catch (e: Exception) {
                    failures.add(BatchFailure(index = index, error = e.message ?: "Unknown error"))
                }

                // Update progress
                updateBatchProgress(batchId, index + 1, request.count, successCount, failures.size)
            }

            // Mark batch as completed
            completeBatchOperation(batchId, successCount, failures)
        }
    }
}
```

## **Business Rules Engine**

```kotlin
interface BusinessRulesEngine {
    fun evaluateRule(rule: BusinessRule, context: RuleContext): RuleResult
    fun evaluateRules(rules: List<BusinessRule>, context: RuleContext): List<RuleResult>
}

@Singleton
class BusinessRulesEngineImpl @Inject constructor() : BusinessRulesEngine {

    override fun evaluateRule(rule: BusinessRule, context: RuleContext): RuleResult {
        return when (rule.type) {
            RuleType.CONTAMINATION_DETECTION -> evaluateContaminationRule(rule, context)
            RuleType.TRANSFER_READINESS -> evaluateTransferReadinessRule(rule, context)
            RuleType.DISPOSAL_CRITERIA -> evaluateDisposalRule(rule, context)
            else -> RuleResult.skipped("Rule type not implemented: ${rule.type}")
        }
    }

    private fun evaluateContaminationRule(rule: BusinessRule, context: RuleContext): RuleResult {
        val culture = context.culture ?: return RuleResult.error("Culture not provided")
        val observations = context.observations ?: emptyList()

        val recentContaminations = observations
            .filter { it.observationDate.isAfter(LocalDate.now().minusWeeks(2)) }
            .count { it.contaminationStatus }

        return when {
            recentContaminations >= 3 -> RuleResult.triggered(
                "High contamination risk: $recentContaminations contaminated observations in 2 weeks"
            )
            recentContaminations >= 1 -> RuleResult.warning(
                "Moderate contamination risk: $recentContaminations contaminated observations"
            )
            else -> RuleResult.passed("No contamination risk detected")
        }
    }
}

data class BusinessRule(
    val id: String,
    val type: RuleType,
    val name: String,
    val description: String,
    val parameters: Map<String, Any> = emptyMap()
)

enum class RuleType {
    CONTAMINATION_DETECTION,
    TRANSFER_READINESS,
    DISPOSAL_CRITERIA,
    OBSERVATION_FREQUENCY
}

data class RuleContext(
    val culture: Culture? = null,
    val observations: List<Observation>? = null,
    val subcultures: List<Subculture>? = null,
    val recipe: Recipe? = null,
    val currentDate: LocalDate = LocalDate.now()
)

sealed class RuleResult {
    abstract val message: String

    data class Passed(override val message: String) : RuleResult()
    data class Warning(override val message: String) : RuleResult()
    data class Triggered(override val message: String) : RuleResult()
    data class Error(override val message: String) : RuleResult()
    data class Skipped(override val message: String) : RuleResult()

    companion object {
        fun passed(message: String) = Passed(message)
        fun warning(message: String) = Warning(message)
        fun triggered(message: String) = Triggered(message)
        fun error(message: String) = Error(message)
        fun skipped(message: String) = Skipped(message)
    }
}
```

## **Service Data Models**

```kotlin
data class CultureWorkflowResult(
    val culture: Culture,
    val workflowSteps: List<WorkflowStep>,
    val isComplete: Boolean,
    val nextRecommendedActions: List<RecommendedAction>
)

data class WorkflowStep(
    val stepName: String,
    var status: String, // "pending", "completed", "failed"
    val startTime: Instant = Instant.now(),
    var endTime: Instant? = null,
    var errorMessage: String? = null
)

data class StatusTransitionRequest(
    val newStatus: CultureStatus,
    val notes: String? = null,
    val scheduledDate: LocalDate? = null,
    val reasonCode: String? = null
)

data class StatusTransitionResult(
    val cultureId: String,
    val oldStatus: CultureStatus,
    val newStatus: CultureStatus,
    val timestamp: Instant,
    val notes: String?,
    val triggeredActions: List<TriggeredAction>
)

data class RecommendedAction(
    val type: ActionType,
    val title: String,
    val description: String,
    val dueDate: LocalDate,
    val priority: Priority
)

enum class ActionType {
    SCHEDULE_OBSERVATION,
    PREPARE_SUBCULTURE,
    CHECK_CONTAMINATION,
    ADJUST_CONDITIONS,
    DISPOSE_CULTURE
}

enum class Priority {
    LOW, MEDIUM, HIGH, CRITICAL
}

data class BatchOperationHandle(
    val batchId: String,
    val operationType: BatchOperationType,
    val totalItems: Int,
    var completedItems: Int = 0,
    var successCount: Int = 0,
    var failureCount: Int = 0,
    val status: BatchStatus,
    val startTime: Instant,
    var endTime: Instant? = null,
    var progress: Double = 0.0
)

enum class BatchOperationType {
    SUBCULTURE_CREATION,
    STATUS_UPDATE,
    BULK_OBSERVATION
}

enum class BatchStatus {
    PENDING, IN_PROGRESS, COMPLETED, FAILED, CANCELLED
}
```

## **Service Testing Strategy**

```kotlin
@Test
class CultureServiceTest {

    @Mock private lateinit var cultureRepository: CultureRepository
    @Mock private lateinit var observationRepository: ObservationRepository
    @Mock private lateinit var validationService: ValidationService
    @Mock private lateinit var notificationService: NotificationService

    private lateinit var cultureService: CultureServiceImpl

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        cultureService = CultureServiceImpl(
            cultureRepository, observationRepository, recipeRepository,
            notificationService, analyticsService, validationService
        )
    }

    @Test
    fun `createCultureWorkflow completes all steps successfully`() = runTest {
        // Arrange
        val request = CreateCultureRequest(
            species = "Rosa damascena",
            explantType = "Shoot tip",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS + 2,4-D",
            initialConditions = "25°C, 16h photoperiod",
            initialObservation = "Initial culture looks healthy"
        )

        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(true, emptyList()))
        whenever(cultureRepository.createCulture(request))
            .thenReturn(Result.success(mockCulture))
        whenever(observationRepository.createObservation(any()))
            .thenReturn(Result.success(mockObservation))

        // Act
        val result = cultureService.createCultureWorkflow(request)

        // Assert
        assertTrue(result.isSuccess)
        val workflowResult = result.getOrThrow()
        assertTrue(workflowResult.isComplete)
        assertEquals(5, workflowResult.workflowSteps.size)
        assertTrue(workflowResult.workflowSteps.all { it.status == "completed" })
        assertFalse(workflowResult.nextRecommendedActions.isEmpty())

        verify(notificationService, times(2)).scheduleReminder(any(), any(), any(), any())
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After business logic implementation and workflow testing