# Event & Communication APIs

## Overview
This document defines the event-driven communication system that enables loose coupling between components through domain events and asynchronous messaging.

---

## **Event Bus Interface**

```kotlin
interface EventBus {

    /**
     * Publishes event to all subscribers
     * @param event Event to publish
     */
    suspend fun publish(event: DomainEvent)

    /**
     * Publishes event without waiting for subscribers
     * @param event Event to publish asynchronously
     */
    suspend fun publishAsync(event: DomainEvent)

    /**
     * Subscribes to events of specific type
     * @param eventType Event class type
     * @param handler Event handler function
     * @return Subscription handle for unsubscribing
     */
    fun <T : DomainEvent> subscribe(
        eventType: Class<T>,
        handler: suspend (T) -> Unit
    ): EventSubscription

    /**
     * Subscribes to multiple event types
     * @param eventTypes Set of event types
     * @param handler Generic event handler
     * @return Subscription handle
     */
    fun subscribeToMultiple(
        eventTypes: Set<Class<out DomainEvent>>,
        handler: suspend (DomainEvent) -> Unit
    ): EventSubscription

    /**
     * Unsubscribes from events
     * @param subscription Subscription to cancel
     */
    fun unsubscribe(subscription: EventSubscription)
}
```

## **Event Bus Implementation**

```kotlin
@Singleton
class EventBusImpl @Inject constructor(
    private val coroutineScope: CoroutineScope
) : EventBus {

    private val subscribers = ConcurrentHashMap<Class<out DomainEvent>, MutableSet<EventHandler>>()
    private val eventChannel = Channel<DomainEvent>(Channel.UNLIMITED)

    init {
        // Start event processing coroutine
        coroutineScope.launch {
            for (event in eventChannel) {
                processEvent(event)
            }
        }
    }

    override suspend fun publish(event: DomainEvent) {
        // Synchronous publishing - wait for all handlers to complete
        processEvent(event)
    }

    override suspend fun publishAsync(event: DomainEvent) {
        // Asynchronous publishing - queue event for processing
        eventChannel.trySend(event)
    }

    override fun <T : DomainEvent> subscribe(
        eventType: Class<T>,
        handler: suspend (T) -> Unit
    ): EventSubscription {
        val eventHandler = EventHandler(
            id = UUID.randomUUID().toString(),
            handler = { event ->
                @Suppress("UNCHECKED_CAST")
                handler(event as T)
            }
        )

        subscribers.getOrPut(eventType) { ConcurrentHashMap.newKeySet() }
            .add(eventHandler)

        return EventSubscriptionImpl(eventType, eventHandler.id) {
            unsubscribeHandler(eventType, eventHandler.id)
        }
    }

    override fun subscribeToMultiple(
        eventTypes: Set<Class<out DomainEvent>>,
        handler: suspend (DomainEvent) -> Unit
    ): EventSubscription {
        val subscriptionId = UUID.randomUUID().toString()
        val eventHandler = EventHandler(subscriptionId, handler)

        eventTypes.forEach { eventType ->
            subscribers.getOrPut(eventType) { ConcurrentHashMap.newKeySet() }
                .add(eventHandler)
        }

        return EventSubscriptionImpl(eventTypes, subscriptionId) {
            eventTypes.forEach { eventType ->
                unsubscribeHandler(eventType, subscriptionId)
            }
        }
    }

    override fun unsubscribe(subscription: EventSubscription) {
        subscription.unsubscribe()
    }

    private suspend fun processEvent(event: DomainEvent) {
        val handlers = subscribers[event::class.java] ?: emptySet()

        handlers.forEach { handler ->
            coroutineScope.launch {
                try {
                    handler.handler(event)
                } catch (e: Exception) {
                    // Log error but don't let one handler failure affect others
                    Log.e("EventBus", "Error processing event ${event::class.simpleName}", e)
                }
            }
        }
    }

    private fun unsubscribeHandler(eventType: Class<out DomainEvent>, handlerId: String) {
        subscribers[eventType]?.removeIf { it.id == handlerId }
    }
}

private data class EventHandler(
    val id: String,
    val handler: suspend (DomainEvent) -> Unit
)

private class EventSubscriptionImpl(
    private val eventType: Any, // Class<out DomainEvent> or Set<Class<out DomainEvent>>
    private val handlerId: String,
    private val unsubscribeAction: () -> Unit
) : EventSubscription {

    private var isActive = true

    override fun unsubscribe() {
        if (isActive) {
            unsubscribeAction()
            isActive = false
        }
    }

    override fun isActive(): Boolean = isActive
}

interface EventSubscription {
    fun unsubscribe()
    fun isActive(): Boolean
}
```

## **Domain Events**

```kotlin
/**
 * Base class for all domain events
 */
sealed class DomainEvent {
    abstract val timestamp: Instant
    abstract val eventId: String

    data class CultureCreated(
        val culture: Culture,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class CultureStatusChanged(
        val cultureId: String,
        val oldStatus: CultureStatus,
        val newStatus: CultureStatus,
        val reason: String? = null,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class ObservationAdded(
        val observation: Observation,
        val cultureId: String,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class ContaminationDetected(
        val cultureId: String,
        val observationId: String,
        val severity: ContaminationSeverity,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class SyncCompleted(
        val entityType: String,
        val entityId: String,
        val operation: SyncOperation,
        val success: Boolean,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class ConflictDetected(
        val entityType: String,
        val entityId: String,
        val conflictType: ConflictType,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class BatchOperationCompleted(
        val batchId: String,
        val operationType: BatchOperationType,
        val totalItems: Int,
        val successCount: Int,
        val failureCount: Int,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class ReminderScheduled(
        val reminderId: String,
        val cultureId: String,
        val reminderType: ReminderType,
        val scheduledFor: LocalDateTime,
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()

    data class UserActivityLogged(
        val userId: String,
        val activity: String,
        val entityType: String,
        val entityId: String,
        val metadata: Map<String, String> = emptyMap(),
        override val timestamp: Instant = Instant.now(),
        override val eventId: String = UUID.randomUUID().toString()
    ) : DomainEvent()
}

enum class ContaminationSeverity {
    LOW, MODERATE, HIGH, CRITICAL
}

enum class ConflictType {
    VERSION_MISMATCH, DATA_INCONSISTENCY, SIMULTANEOUS_EDIT
}

enum class ReminderType {
    OBSERVATION_DUE, TRANSFER_CHECK, GROWTH_ASSESSMENT, CONTAMINATION_CHECK
}
```

## **Event Handlers**

```kotlin
/**
 * Handles culture-related events
 */
@Component
class CultureEventHandler @Inject constructor(
    private val notificationService: NotificationService,
    private val analyticsService: AnalyticsService,
    private val eventBus: EventBus
) {

    @PostConstruct
    fun initialize() {
        eventBus.subscribe(DomainEvent.CultureCreated::class.java) { event ->
            handleCultureCreated(event)
        }

        eventBus.subscribe(DomainEvent.CultureStatusChanged::class.java) { event ->
            handleCultureStatusChanged(event)
        }

        eventBus.subscribe(DomainEvent.ContaminationDetected::class.java) { event ->
            handleContaminationDetected(event)
        }
    }

    private suspend fun handleCultureCreated(event: DomainEvent.CultureCreated) {
        // Track culture creation analytics
        analyticsService.trackEvent("culture_created", mapOf(
            "culture_id" to event.culture.id,
            "species" to event.culture.species,
            "explant_type" to event.culture.explantType
        ))

        // Send welcome notification for first-time users
        if (analyticsService.isFirstCulture(event.culture.deviceId)) {
            notificationService.sendWelcomeNotification(event.culture)
        }

        Log.i("CultureEvents", "Culture created: ${event.culture.cultureId}")
    }

    private suspend fun handleCultureStatusChanged(event: DomainEvent.CultureStatusChanged) {
        // Track status changes
        analyticsService.trackEvent("culture_status_changed", mapOf(
            "culture_id" to event.cultureId,
            "old_status" to event.oldStatus.name,
            "new_status" to event.newStatus.name,
            "reason" to (event.reason ?: "")
        ))

        // Handle specific status changes
        when (event.newStatus) {
            CultureStatus.READY_FOR_TRANSFER -> {
                notificationService.sendTransferReadyNotification(event.cultureId)
            }
            CultureStatus.CONTAMINATED -> {
                notificationService.sendContaminationAlert(event.cultureId)
            }
            else -> { /* No specific action */ }
        }

        Log.i("CultureEvents", "Culture status changed: ${event.cultureId} -> ${event.newStatus}")
    }

    private suspend fun handleContaminationDetected(event: DomainEvent.ContaminationDetected) {
        // Send immediate contamination alert
        notificationService.sendUrgentContaminationAlert(
            event.cultureId, event.severity
        )

        // Track contamination for analytics
        analyticsService.trackContaminationEvent(event)

        // Auto-suggest disposal for critical contamination
        if (event.severity == ContaminationSeverity.CRITICAL) {
            notificationService.suggestDisposal(event.cultureId)
        }

        Log.w("CultureEvents", "Contamination detected: ${event.cultureId} (${event.severity})")
    }
}

/**
 * Handles sync-related events
 */
@Component
class SyncEventHandler @Inject constructor(
    private val syncStatusService: SyncStatusService,
    private val eventBus: EventBus
) {

    @PostConstruct
    fun initialize() {
        eventBus.subscribe(DomainEvent.SyncCompleted::class.java) { event ->
            handleSyncCompleted(event)
        }

        eventBus.subscribe(DomainEvent.ConflictDetected::class.java) { event ->
            handleConflictDetected(event)
        }
    }

    private suspend fun handleSyncCompleted(event: DomainEvent.SyncCompleted) {
        syncStatusService.updateSyncStatus(
            event.entityType, event.entityId, event.success
        )

        if (event.success) {
            Log.d("SyncEvents", "Sync completed: ${event.entityType}/${event.entityId}")
        } else {
            Log.w("SyncEvents", "Sync failed: ${event.entityType}/${event.entityId}")
        }
    }

    private suspend fun handleConflictDetected(event: DomainEvent.ConflictDetected) {
        syncStatusService.recordConflict(event)

        Log.w("SyncEvents", "Sync conflict: ${event.entityType}/${event.entityId} (${event.conflictType})")
    }
}
```

## **Event-Driven Architecture Patterns**

### **Command-Query Separation**
Separate commands (state changes) from queries (data retrieval):

```kotlin
// Commands trigger events
suspend fun createCulture(request: CreateCultureRequest): Result<Culture> {
    val culture = // ... create culture
    eventBus.publish(DomainEvent.CultureCreated(culture))
    return Result.success(culture)
}

// Queries don't trigger events
fun getCultures(): Flow<List<Culture>> = cultureDao.getAllActiveCultures()
```

### **Eventual Consistency**
Handle eventually consistent updates through events:

```kotlin
class CacheInvalidationHandler @Inject constructor(
    private val cacheManager: CacheManager,
    private val eventBus: EventBus
) {

    @PostConstruct
    fun initialize() {
        eventBus.subscribe(DomainEvent.CultureStatusChanged::class.java) { event ->
            // Invalidate cache when culture changes
            cacheManager.invalidateCulture(event.cultureId)
        }
    }
}
```

### **Saga Pattern for Complex Workflows**
Coordinate multi-step processes through events:

```kotlin
class SubcultureCreationSaga @Inject constructor(
    private val eventBus: EventBus
) {

    @PostConstruct
    fun initialize() {
        eventBus.subscribe(DomainEvent.CultureStatusChanged::class.java) { event ->
            if (event.newStatus == CultureStatus.READY_FOR_TRANSFER) {
                startSubculturePreparationWorkflow(event.cultureId)
            }
        }
    }

    private suspend fun startSubculturePreparationWorkflow(cultureId: String) {
        // Step 1: Schedule reminder
        eventBus.publish(DomainEvent.ReminderScheduled(
            reminderId = UUID.randomUUID().toString(),
            cultureId = cultureId,
            reminderType = ReminderType.TRANSFER_CHECK,
            scheduledFor = LocalDateTime.now().plusDays(3)
        ))

        // Step 2: Prepare materials notification
        // ... additional workflow steps
    }
}
```

## **Event Store for Audit Trail**

```kotlin
@Entity(tableName = "event_store")
data class StoredEvent(
    @PrimaryKey val eventId: String,
    val eventType: String,
    val aggregateId: String,
    val aggregateType: String,
    val eventData: String, // JSON serialized event
    val timestamp: Instant,
    val version: Int
)

@Dao
interface EventStoreDao {
    @Insert
    suspend fun storeEvent(event: StoredEvent)

    @Query("SELECT * FROM event_store WHERE aggregate_id = :aggregateId ORDER BY version ASC")
    suspend fun getEventsForAggregate(aggregateId: String): List<StoredEvent>

    @Query("SELECT * FROM event_store WHERE event_type = :eventType ORDER BY timestamp DESC")
    suspend fun getEventsByType(eventType: String): List<StoredEvent>
}

@Component
class EventStore @Inject constructor(
    private val eventStoreDao: EventStoreDao,
    private val eventBus: EventBus,
    private val jsonSerializer: JsonSerializer
) {

    @PostConstruct
    fun initialize() {
        // Store all events for audit trail
        eventBus.subscribeToMultiple(
            setOf(
                DomainEvent.CultureCreated::class.java,
                DomainEvent.CultureStatusChanged::class.java,
                DomainEvent.ObservationAdded::class.java,
                DomainEvent.ContaminationDetected::class.java
            )
        ) { event ->
            storeEvent(event)
        }
    }

    private suspend fun storeEvent(event: DomainEvent) {
        val storedEvent = StoredEvent(
            eventId = event.eventId,
            eventType = event::class.simpleName ?: "Unknown",
            aggregateId = extractAggregateId(event),
            aggregateType = extractAggregateType(event),
            eventData = jsonSerializer.serialize(event),
            timestamp = event.timestamp,
            version = 1 // In real implementation, would increment based on aggregate
        )

        eventStoreDao.storeEvent(storedEvent)
    }

    private fun extractAggregateId(event: DomainEvent): String = when (event) {
        is DomainEvent.CultureCreated -> event.culture.id
        is DomainEvent.CultureStatusChanged -> event.cultureId
        is DomainEvent.ObservationAdded -> event.cultureId
        is DomainEvent.ContaminationDetected -> event.cultureId
        else -> ""
    }
}
```

## **Performance Considerations**

### **Event Processing Optimization**
- Use bounded channels to prevent memory issues
- Implement backpressure handling for high-volume events
- Consider event batching for analytics events

### **Error Handling and Resilience**
- Isolate handler failures to prevent cascade effects
- Implement retry logic for critical events
- Use dead letter queues for failed events

### **Testing Event-Driven Systems**
```kotlin
@Test
class EventBusTest {

    @Test
    fun `event is delivered to subscribed handlers`() = runTest {
        val eventBus = EventBusImpl(this)
        val receivedEvents = mutableListOf<DomainEvent.CultureCreated>()

        eventBus.subscribe(DomainEvent.CultureCreated::class.java) { event ->
            receivedEvents.add(event)
        }

        val testEvent = DomainEvent.CultureCreated(mockCulture)
        eventBus.publish(testEvent)

        assertEquals(1, receivedEvents.size)
        assertEquals(testEvent.eventId, receivedEvents[0].eventId)
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After event-driven architecture implementation and performance testing