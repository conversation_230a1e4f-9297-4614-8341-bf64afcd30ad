# Sync & Integration APIs

## Overview
This document defines the synchronization and external integration APIs that handle cloud sync, conflict resolution, and offline-first data management.

---

## **Sync Service Interface**

```kotlin
interface SyncService {

    /**
     * Performs intelligent synchronization with conflict resolution
     * @param syncScope Scope of synchronization (all, cultures, recipes, etc.)
     * @return Result containing sync summary
     */
    suspend fun performIntelligentSync(syncScope: SyncScope = SyncScope.ALL): Result<SyncSummary>

    /**
     * Queues entity for synchronization
     * @param entityType Type of entity to sync
     * @param entityId Entity identifier
     * @param operation Sync operation type
     * @param priority Sync priority level
     * @return Result indicating queue success
     */
    suspend fun queueForSync(
        entityType: String,
        entityId: String,
        operation: SyncOperation,
        priority: SyncPriority = SyncPriority.NORMAL
    ): Result<Unit>

    /**
     * Resolves sync conflict with user choice
     * @param conflictId Conflict identifier
     * @param resolution User's resolution choice
     * @return Result containing resolved entity
     */
    suspend fun resolveConflict(
        conflictId: String,
        resolution: ConflictResolution
    ): Result<Any>

    /**
     * Gets current sync status for UI display
     * @return Current synchronization status
     */
    suspend fun getSyncStatus(): SyncStatus

    /**
     * Forces manual sync for specific entity
     * @param entityType Entity type
     * @param entityId Entity ID
     * @return Result of manual sync operation
     */
    suspend fun forceSyncEntity(entityType: String, entityId: String): Result<SyncResult>

    /**
     * Enables offline-only mode
     * @return Result indicating mode change success
     */
    suspend fun enableOfflineMode(): Result<Unit>

    /**
     * Gets entities pending synchronization
     * @param entityType Optional entity type filter
     * @return List of entities awaiting sync
     */
    suspend fun getPendingSyncEntities(entityType: String? = null): List<PendingSyncEntity>
}
```

## **Sync Service Implementation**

```kotlin
@Singleton
class SyncServiceImpl @Inject constructor(
    private val driveApiService: DriveApiService,
    private val syncQueueDao: SyncQueueDao,
    private val conflictResolver: ConflictResolver,
    private val networkMonitor: NetworkMonitor,
    private val authService: AuthService
) : SyncService {

    private val syncMutex = Mutex()

    override suspend fun performIntelligentSync(syncScope: SyncScope): Result<SyncSummary> =
        withContext(Dispatchers.IO) {
            syncMutex.withLock {
                try {
                    val summary = SyncSummary()

                    // Check authentication status
                    if (!authService.isAuthenticated()) {
                        return@withLock Result.failure(
                            SyncException("User not authenticated for cloud sync")
                        )
                    }

                    // Check network connectivity
                    if (!networkMonitor.isConnected()) {
                        return@withLock Result.failure(
                            SyncException("No network connectivity")
                        )
                    }

                    // Get pending sync operations
                    val pendingOperations = syncQueueDao.getPendingOperations(syncScope)

                    for (operation in pendingOperations) {
                        try {
                            when (operation.operation) {
                                SyncOperation.CREATE -> syncCreateOperation(operation)
                                SyncOperation.UPDATE -> syncUpdateOperation(operation)
                                SyncOperation.DELETE -> syncDeleteOperation(operation)
                            }

                            // Mark operation as completed
                            syncQueueDao.markCompleted(operation.id)
                            summary.successCount++

                        } catch (conflictException: SyncConflictException) {
                            // Handle conflict
                            val conflict = createConflictRecord(operation, conflictException)
                            conflictResolver.queueConflictForResolution(conflict)
                            summary.conflictCount++

                        } catch (e: Exception) {
                            // Handle failure
                            syncQueueDao.markFailed(operation.id, e.message)
                            summary.failureCount++
                        }
                    }

                    // Perform bidirectional sync (download changes from cloud)
                    val downloadResult = syncDownstreamChanges(syncScope)
                    summary.downloadCount = downloadResult.changeCount

                    Result.success(summary)

                } catch (e: Exception) {
                    Result.failure(e)
                }
            }
        }

    override suspend fun queueForSync(
        entityType: String,
        entityId: String,
        operation: SyncOperation,
        priority: SyncPriority
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val syncQueueItem = SyncQueueItem(
                id = UUID.randomUUID().toString(),
                entityType = entityType,
                entityId = entityId,
                operation = operation,
                priority = priority,
                createdAt = Instant.now(),
                status = SyncQueueStatus.PENDING
            )

            syncQueueDao.insert(syncQueueItem)
            Result.success(Unit)

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun resolveConflict(
        conflictId: String,
        resolution: ConflictResolution
    ): Result<Any> = withContext(Dispatchers.IO) {
        try {
            val conflict = conflictResolver.getConflict(conflictId)
                ?: return@withContext Result.failure(
                    NotFoundException("Conflict not found: $conflictId")
                )

            val resolvedEntity = when (resolution.strategy) {
                ConflictResolutionStrategy.KEEP_LOCAL -> {
                    applyLocalVersion(conflict)
                }
                ConflictResolutionStrategy.KEEP_REMOTE -> {
                    applyRemoteVersion(conflict)
                }
                ConflictResolutionStrategy.MERGE -> {
                    mergeVersions(conflict, resolution.mergeStrategy)
                }
                ConflictResolutionStrategy.MANUAL -> {
                    applyManualResolution(conflict, resolution.manualData)
                }
            }

            // Mark conflict as resolved
            conflictResolver.markResolved(conflictId, resolvedEntity)

            Result.success(resolvedEntity)

        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun syncCreateOperation(operation: PendingSyncOperation) {
        when (operation.entityType) {
            "Culture" -> {
                val culture = cultureRepository.getCultureById(operation.entityId).getOrThrow()
                driveApiService.uploadCultureData(culture)
            }
            "Observation" -> {
                val observation = observationRepository.getObservationById(operation.entityId).getOrThrow()
                driveApiService.uploadObservationData(observation)
            }
            "Recipe" -> {
                val recipe = recipeRepository.getRecipeById(operation.entityId).getOrThrow()
                driveApiService.uploadRecipeData(recipe)
            }
            else -> throw IllegalArgumentException("Unsupported entity type: ${operation.entityType}")
        }
    }

    private suspend fun syncUpdateOperation(operation: PendingSyncOperation) {
        // Similar to create but with update semantics
        when (operation.entityType) {
            "Culture" -> {
                val culture = cultureRepository.getCultureById(operation.entityId).getOrThrow()
                driveApiService.updateCultureData(culture)
            }
            // ... other entity types
        }
    }

    private suspend fun syncDeleteOperation(operation: PendingSyncOperation) {
        // Handle soft deletes by uploading tombstone records
        driveApiService.markEntityDeleted(operation.entityType, operation.entityId)
    }

    private suspend fun syncDownstreamChanges(syncScope: SyncScope): DownloadResult {
        val changes = driveApiService.getChangesSince(getLastSyncTimestamp())
        var changeCount = 0

        for (change in changes) {
            when (change.entityType) {
                "Culture" -> {
                    val cloudCulture = driveApiService.downloadCultureData(change.entityId)
                    val localCulture = cultureRepository.getCultureById(change.entityId).getOrNull()

                    if (localCulture == null) {
                        // New culture from another device
                        cultureRepository.createCulture(cloudCulture.toCreateRequest())
                        changeCount++
                    } else {
                        // Check for conflicts
                        if (hasConflict(localCulture, cloudCulture)) {
                            throw SyncConflictException("Version mismatch", localCulture, cloudCulture)
                        }

                        // Apply changes
                        cultureRepository.updateFromSync(cloudCulture)
                        changeCount++
                    }
                }
                // Handle other entity types...
            }
        }

        updateLastSyncTimestamp()
        return DownloadResult(changeCount)
    }
}
```

## **Conflict Resolution System**

```kotlin
interface ConflictResolver {
    suspend fun queueConflictForResolution(conflict: SyncConflict)
    suspend fun getConflict(conflictId: String): SyncConflict?
    suspend fun getPendingConflicts(): List<SyncConflict>
    suspend fun markResolved(conflictId: String, resolvedEntity: Any)
    suspend fun autoResolveConflicts(): Int
}

@Singleton
class ConflictResolverImpl @Inject constructor(
    private val conflictDao: SyncConflictDao,
    private val eventBus: EventBus
) : ConflictResolver {

    override suspend fun queueConflictForResolution(conflict: SyncConflict) {
        conflictDao.insert(conflict)
        eventBus.publish(
            DomainEvent.ConflictDetected(
                entityType = conflict.entityType,
                entityId = conflict.entityId,
                conflictType = conflict.conflictType
            )
        )
    }

    override suspend fun autoResolveConflicts(): Int {
        val conflicts = conflictDao.getAutoResolvableConflicts()
        var resolvedCount = 0

        for (conflict in conflicts) {
            try {
                val resolution = determineAutoResolution(conflict)
                if (resolution != null) {
                    val resolvedEntity = applyResolution(conflict, resolution)
                    markResolved(conflict.id, resolvedEntity)
                    resolvedCount++
                }
            } catch (e: Exception) {
                // Log error but continue with other conflicts
                Log.e("ConflictResolver", "Auto-resolution failed for ${conflict.id}", e)
            }
        }

        return resolvedCount
    }

    private fun determineAutoResolution(conflict: SyncConflict): ConflictResolution? {
        return when {
            // Always prefer server for user data conflicts
            conflict.entityType == "User" -> ConflictResolution(
                strategy = ConflictResolutionStrategy.KEEP_REMOTE
            )

            // For cultures, prefer the version with more recent observations
            conflict.entityType == "Culture" -> {
                val localLastObservation = extractLastObservationDate(conflict.localVersion)
                val remoteLastObservation = extractLastObservationDate(conflict.remoteVersion)

                when {
                    localLastObservation > remoteLastObservation -> ConflictResolution(
                        strategy = ConflictResolutionStrategy.KEEP_LOCAL
                    )
                    remoteLastObservation > localLastObservation -> ConflictResolution(
                        strategy = ConflictResolutionStrategy.KEEP_REMOTE
                    )
                    else -> null // Requires manual resolution
                }
            }

            else -> null // No auto-resolution strategy
        }
    }
}
```

## **Google Drive Integration**

```kotlin
interface DriveApiService {
    suspend fun uploadCultureData(culture: Culture): DriveUploadResult
    suspend fun downloadCultureData(cultureId: String): Culture
    suspend fun updateCultureData(culture: Culture): DriveUpdateResult
    suspend fun markEntityDeleted(entityType: String, entityId: String)
    suspend fun getChangesSince(timestamp: Instant): List<DriveChange>
    suspend fun uploadPhoto(photo: Photo): DriveUploadResult
    suspend fun downloadPhoto(photoId: String): ByteArray
}

@Singleton
class DriveApiServiceImpl @Inject constructor(
    private val driveClient: Drive,
    private val authService: AuthService,
    private val compressionService: CompressionService
) : DriveApiService {

    companion object {
        private const val CULTURESTACK_FOLDER = "CultureStack"
        private const val CULTURES_FOLDER = "Cultures"
        private const val PHOTOS_FOLDER = "Photos"
        private const val RECIPES_FOLDER = "Recipes"
    }

    override suspend fun uploadCultureData(culture: Culture): DriveUploadResult =
        withContext(Dispatchers.IO) {
            try {
                // Ensure folder structure exists
                val culturesFolderId = ensureFolderExists(CULTURES_FOLDER)

                // Convert culture to JSON
                val cultureJson = JsonSerializer.serialize(culture)
                val compressedData = compressionService.compress(cultureJson)

                // Create file metadata
                val fileMetadata = File().apply {
                    name = "${culture.cultureId}.json"
                    parents = listOf(culturesFolderId)
                    mimeType = "application/json"
                }

                // Upload file
                val mediaContent = ByteArrayContent("application/json", compressedData)
                val file = driveClient.files()
                    .create(fileMetadata, mediaContent)
                    .setFields("id,name,modifiedTime,version")
                    .execute()

                DriveUploadResult.success(file.id, file.name, file.size ?: 0L)

            } catch (e: Exception) {
                DriveUploadResult.failure(e.message ?: "Upload failed")
            }
        }

    override suspend fun downloadCultureData(cultureId: String): Culture =
        withContext(Dispatchers.IO) {
            try {
                // Find culture file
                val query = "name='${cultureId}.json' and parents in '${getCulturesFolderId()}'"
                val files = driveClient.files().list()
                    .setQ(query)
                    .setFields("files(id,name,modifiedTime)")
                    .execute()

                val file = files.files.firstOrNull()
                    ?: throw NotFoundException("Culture file not found: $cultureId")

                // Download file content
                val outputStream = ByteArrayOutputStream()
                driveClient.files().get(file.id).executeMediaAndDownloadTo(outputStream)

                // Decompress and deserialize
                val compressedData = outputStream.toByteArray()
                val jsonData = compressionService.decompress(compressedData)
                JsonSerializer.deserialize<Culture>(jsonData)

            } catch (e: Exception) {
                throw SyncException("Failed to download culture: $cultureId", e)
            }
        }

    override suspend fun getChangesSince(timestamp: Instant): List<DriveChange> =
        withContext(Dispatchers.IO) {
            try {
                val cultureStackFolderId = getCultureStackFolderId()
                val query = "modifiedTime > '${timestamp.toString()}' and " +
                           "'$cultureStackFolderId' in parents"

                val files = driveClient.files().list()
                    .setQ(query)
                    .setFields("files(id,name,modifiedTime,parents,trashed)")
                    .setOrderBy("modifiedTime")
                    .execute()

                files.files.map { file ->
                    DriveChange(
                        fileId = file.id,
                        fileName = file.name,
                        entityType = determineEntityType(file.name),
                        entityId = extractEntityId(file.name),
                        modifiedTime = Instant.parse(file.modifiedTime.toString()),
                        isDeleted = file.trashed == true
                    )
                }

            } catch (e: Exception) {
                throw SyncException("Failed to get changes since $timestamp", e)
            }
        }

    override suspend fun uploadPhoto(photo: Photo): DriveUploadResult =
        withContext(Dispatchers.IO) {
            try {
                val photosFolderId = ensureFolderExists(PHOTOS_FOLDER)

                // Compress photo if needed
                val compressedPhoto = compressionService.compressImage(
                    photo.imageData,
                    maxWidth = 1920,
                    maxHeight = 1080,
                    quality = 85
                )

                val fileMetadata = File().apply {
                    name = "${photo.id}.jpg"
                    parents = listOf(photosFolderId)
                    mimeType = "image/jpeg"
                    description = "Culture photo: ${photo.cultureId}"
                }

                val mediaContent = ByteArrayContent("image/jpeg", compressedPhoto)
                val file = driveClient.files()
                    .create(fileMetadata, mediaContent)
                    .setFields("id,name,size")
                    .execute()

                DriveUploadResult.success(file.id, file.name, file.size ?: 0L)

            } catch (e: Exception) {
                DriveUploadResult.failure(e.message ?: "Photo upload failed")
            }
        }

    private suspend fun ensureFolderExists(folderName: String): String {
        // Check if folder exists
        val query = "name='$folderName' and mimeType='application/vnd.google-apps.folder'"
        val folders = driveClient.folders().list()
            .setQ(query)
            .setFields("folders(id,name)")
            .execute()

        return if (folders.folders.isNotEmpty()) {
            folders.folders.first().id
        } else {
            // Create folder
            val folderMetadata = File().apply {
                name = folderName
                mimeType = "application/vnd.google-apps.folder"
            }

            driveClient.files()
                .create(folderMetadata)
                .setFields("id")
                .execute()
                .id
        }
    }
}
```

## **Network Monitoring**

```kotlin
interface NetworkMonitor {
    val isConnected: Boolean
    val connectionType: ConnectionType
    fun observeConnectivity(): Flow<NetworkState>
    suspend fun waitForConnection(): Boolean
}

@Singleton
class NetworkMonitorImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : NetworkMonitor {

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE)
            as ConnectivityManager

    private val _networkState = MutableStateFlow(getCurrentNetworkState())
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            _networkState.value = NetworkState.Connected(getConnectionType())
        }

        override fun onLost(network: Network) {
            _networkState.value = NetworkState.Disconnected
        }
    }

    init {
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        connectivityManager.registerNetworkCallback(request, networkCallback)
    }

    override val isConnected: Boolean
        get() = _networkState.value is NetworkState.Connected

    override val connectionType: ConnectionType
        get() = when (val state = _networkState.value) {
            is NetworkState.Connected -> state.type
            is NetworkState.Disconnected -> ConnectionType.NONE
        }

    override fun observeConnectivity(): Flow<NetworkState> = _networkState.asStateFlow()

    override suspend fun waitForConnection(): Boolean {
        return if (isConnected) {
            true
        } else {
            _networkState
                .first { it is NetworkState.Connected }
                .let { true }
        }
    }

    private fun getCurrentNetworkState(): NetworkState {
        val activeNetwork = connectivityManager.activeNetwork
        return if (activeNetwork != null) {
            NetworkState.Connected(getConnectionType())
        } else {
            NetworkState.Disconnected
        }
    }

    private fun getConnectionType(): ConnectionType {
        val capabilities = connectivityManager.getNetworkCapabilities(
            connectivityManager.activeNetwork
        )

        return when {
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true ->
                ConnectionType.WIFI
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true ->
                ConnectionType.CELLULAR
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true ->
                ConnectionType.ETHERNET
            else -> ConnectionType.NONE
        }
    }
}

sealed class NetworkState {
    object Disconnected : NetworkState()
    data class Connected(val type: ConnectionType) : NetworkState()
}

enum class ConnectionType {
    NONE, WIFI, CELLULAR, ETHERNET
}
```

## **Sync Data Models**

```kotlin
data class SyncSummary(
    var successCount: Int = 0,
    var failureCount: Int = 0,
    var conflictCount: Int = 0,
    var downloadCount: Int = 0,
    val startTime: Instant = Instant.now(),
    var endTime: Instant? = null,
    val errors: MutableList<SyncError> = mutableListOf()
) {
    val isComplete: Boolean get() = endTime != null
    val totalOperations: Int get() = successCount + failureCount + conflictCount
    val duration: Duration? get() = endTime?.let { Duration.between(startTime, it) }
}

data class SyncStatus(
    val isEnabled: Boolean,
    val lastSyncTime: Instant?,
    val pendingOperations: Int,
    val conflictsCount: Int,
    val syncState: SyncState
)

enum class SyncState {
    IDLE, SYNCING, PAUSED, ERROR, OFFLINE_MODE
}

enum class SyncScope {
    ALL, CULTURES, RECIPES, OBSERVATIONS, PHOTOS
}

enum class SyncOperation {
    CREATE, UPDATE, DELETE
}

enum class SyncPriority {
    LOW, NORMAL, HIGH, URGENT
}

data class ConflictResolution(
    val strategy: ConflictResolutionStrategy,
    val mergeStrategy: MergeStrategy? = null,
    val manualData: Any? = null
)

enum class ConflictResolutionStrategy {
    KEEP_LOCAL, KEEP_REMOTE, MERGE, MANUAL
}

enum class MergeStrategy {
    LATEST_WINS, FIELD_BY_FIELD, UNION
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After sync implementation and conflict resolution testing