# Data Transfer Objects (DTOs)

## Overview
This document defines the API contract specifications through Data Transfer Objects that handle request validation, response formatting, and data transformation between application layers.

---

## **Request DTOs**

### **Culture Request Objects**

```kotlin
/**
 * Request object for creating new culture
 */
data class CreateCultureRequest(
    val species: String,
    val explantType: String,
    val sourcePlantId: String? = null,
    val initiationDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val initialConditions: String,
    val initialObservation: String? = null,
    val tags: List<String> = emptyList()
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        if (species.isBlank()) errors.add("Species is required")
        if (species.length > 100) errors.add("Species name cannot exceed 100 characters")

        if (explantType.isBlank()) errors.add("Explant type is required")
        if (explantType !in VALID_EXPLANT_TYPES) {
            errors.add("Invalid explant type. Valid options: ${VALID_EXPLANT_TYPES.joinToString()}")
        }

        if (initiationDate.isAfter(LocalDate.now())) {
            errors.add("Initiation date cannot be in the future")
        }
        if (initiationDate.isBefore(LocalDate.now().minusYears(1))) {
            errors.add("Initiation date cannot be more than 1 year ago")
        }

        if (mediumComposition.isBlank()) errors.add("Medium composition is required")
        if (mediumComposition.length > 500) errors.add("Medium composition cannot exceed 500 characters")

        if (initialConditions.isBlank()) errors.add("Initial conditions are required")
        if (initialConditions.length > 1000) errors.add("Initial conditions cannot exceed 1000 characters")

        // Validate tags
        if (tags.size > 10) errors.add("Maximum 10 tags allowed")
        tags.forEach { tag ->
            if (tag.length > 50) errors.add("Tag '$tag' cannot exceed 50 characters")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    companion object {
        val VALID_EXPLANT_TYPES = listOf(
            "Shoot tip", "Node", "Leaf", "Root", "Callus", "Embryo", "Seed", "Other"
        )
    }
}

/**
 * Request object for updating culture information
 */
data class UpdateCultureRequest(
    val species: String? = null,
    val mediumComposition: String? = null,
    val initialConditions: String? = null,
    val tags: List<String>? = null,
    val notes: String? = null
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        species?.let {
            if (it.isBlank()) errors.add("Species cannot be blank")
            if (it.length > 100) errors.add("Species name cannot exceed 100 characters")
        }

        mediumComposition?.let {
            if (it.isBlank()) errors.add("Medium composition cannot be blank")
            if (it.length > 500) errors.add("Medium composition cannot exceed 500 characters")
        }

        initialConditions?.let {
            if (it.isBlank()) errors.add("Initial conditions cannot be blank")
            if (it.length > 1000) errors.add("Initial conditions cannot exceed 1000 characters")
        }

        tags?.let { tagList ->
            if (tagList.size > 10) errors.add("Maximum 10 tags allowed")
            tagList.forEach { tag ->
                if (tag.length > 50) errors.add("Tag '$tag' cannot exceed 50 characters")
            }
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}

/**
 * Request object for batch subculture creation
 */
data class BatchSubcultureRequest(
    val parentCultureId: String,
    val count: Int,
    val subcultureDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val explantCountPerSubculture: Int = 1,
    val notes: String? = null
) {
    init {
        require(count > 0) { "Count must be positive" }
        require(count <= 50) { "Maximum 50 subcultures per batch" }
        require(explantCountPerSubculture > 0) { "Explant count must be positive" }
        require(explantCountPerSubculture <= 10) { "Maximum 10 explants per subculture" }
    }

    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        if (parentCultureId.isBlank()) errors.add("Parent culture ID is required")

        if (subcultureDate.isAfter(LocalDate.now())) {
            errors.add("Subculture date cannot be in the future")
        }

        if (mediumComposition.isBlank()) errors.add("Medium composition is required")
        if (mediumComposition.length > 500) errors.add("Medium composition cannot exceed 500 characters")

        notes?.let {
            if (it.length > 1000) errors.add("Notes cannot exceed 1000 characters")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}
```

### **Observation Request Objects**

```kotlin
/**
 * Request object for creating observations
 */
data class CreateObservationRequest(
    val cultureId: String,
    val observationDate: LocalDate,
    val contaminationStatus: Boolean,
    val survivalStatus: SurvivalStatus,
    val growthStage: GrowthStage,
    val notes: String? = null,
    val photoFilenames: List<String> = emptyList(),
    val environmentalConditions: EnvironmentalConditions? = null
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        if (cultureId.isBlank()) errors.add("Culture ID is required")

        if (observationDate.isAfter(LocalDate.now())) {
            errors.add("Observation date cannot be in the future")
        }

        notes?.let {
            if (it.length > 2000) errors.add("Notes cannot exceed 2000 characters")
        }

        if (photoFilenames.size > 10) errors.add("Maximum 10 photos per observation")
        photoFilenames.forEach { filename ->
            if (!filename.matches(Regex("^[\\w\\-. ]+\\.(jpg|jpeg|png|webp)$", RegexOption.IGNORE_CASE))) {
                errors.add("Invalid photo filename: $filename")
            }
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}

/**
 * Environmental conditions for observations
 */
data class EnvironmentalConditions(
    val temperature: Double? = null,
    val humidity: Double? = null,
    val lightIntensity: Double? = null,
    val photoperiod: Double? = null,
    val co2Level: Double? = null
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        temperature?.let {
            if (it < -10 || it > 60) errors.add("Temperature must be between -10°C and 60°C")
        }

        humidity?.let {
            if (it < 0 || it > 100) errors.add("Humidity must be between 0% and 100%")
        }

        lightIntensity?.let {
            if (it < 0 || it > 500) errors.add("Light intensity must be between 0 and 500 µmol/m²/s")
        }

        photoperiod?.let {
            if (it < 0 || it > 24) errors.add("Photoperiod must be between 0 and 24 hours")
        }

        co2Level?.let {
            if (it < 0 || it > 10000) errors.add("CO2 level must be between 0 and 10000 ppm")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}

enum class SurvivalStatus {
    EXCELLENT, GOOD, FAIR, POOR, DEAD
}

enum class GrowthStage {
    INITIATION, ESTABLISHMENT, MULTIPLICATION, ROOTING, ACCLIMATIZATION, DECLINING
}
```

### **Recipe Request Objects**

```kotlin
/**
 * Request object for creating recipes
 */
data class CreateRecipeRequest(
    val name: String,
    val description: String,
    val category: RecipeCategory,
    val difficultyLevel: DifficultyLevel,
    val plantTypes: List<String>,
    val components: List<RecipeComponent>,
    val instructions: List<String>,
    val notes: String? = null,
    val tags: List<String> = emptyList()
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        if (name.isBlank()) errors.add("Recipe name is required")
        if (name.length > 100) errors.add("Recipe name cannot exceed 100 characters")

        if (description.isBlank()) errors.add("Recipe description is required")
        if (description.length > 1000) errors.add("Recipe description cannot exceed 1000 characters")

        if (plantTypes.isEmpty()) errors.add("At least one plant type must be specified")
        if (plantTypes.size > 20) errors.add("Maximum 20 plant types allowed")

        if (components.isEmpty()) errors.add("At least one component must be specified")
        if (components.size > 50) errors.add("Maximum 50 components allowed")

        if (instructions.isEmpty()) errors.add("At least one instruction must be provided")
        if (instructions.size > 20) errors.add("Maximum 20 instruction steps allowed")

        // Validate components
        components.forEachIndexed { index, component ->
            val componentValidation = component.validate()
            if (!componentValidation.isValid) {
                errors.add("Component ${index + 1}: ${componentValidation.errors.joinToString()}")
            }
        }

        notes?.let {
            if (it.length > 2000) errors.add("Notes cannot exceed 2000 characters")
        }

        if (tags.size > 10) errors.add("Maximum 10 tags allowed")

        return ValidationResult(errors.isEmpty(), errors)
    }
}

/**
 * Recipe component with concentration and units
 */
data class RecipeComponent(
    val name: String,
    val concentration: Double,
    val unit: ConcentrationUnit,
    val isOptional: Boolean = false,
    val notes: String? = null
) {
    fun validate(): ValidationResult {
        val errors = mutableListOf<String>()

        if (name.isBlank()) errors.add("Component name is required")
        if (name.length > 100) errors.add("Component name cannot exceed 100 characters")

        if (concentration < 0) errors.add("Concentration cannot be negative")
        if (concentration > 1000000) errors.add("Concentration seems unreasonably high")

        notes?.let {
            if (it.length > 500) errors.add("Component notes cannot exceed 500 characters")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}

enum class RecipeCategory {
    INITIATION, MULTIPLICATION, ROOTING, MAINTENANCE, SPECIALIZED
}

enum class DifficultyLevel {
    BEGINNER, INTERMEDIATE, ADVANCED, EXPERT
}

enum class ConcentrationUnit {
    MG_L("mg/L"),
    G_L("g/L"),
    MICROMOLAR("µM"),
    MILLIMOLAR("mM"),
    PERCENT("%"),
    PPM("ppm");

    constructor(display: String) {
        this.display = display
    }

    val display: String
}
```

## **Response DTOs**

### **Culture Response Objects**

```kotlin
/**
 * Response object containing culture with computed fields
 */
data class CultureResponse(
    val culture: Culture,
    val subcultureCount: Int,
    val observationCount: Int,
    val lastObservationDate: LocalDate?,
    val daysActive: Long,
    val successPrediction: Double?,
    val nextRecommendedAction: String?,
    val healthScore: Int, // 0-100
    val alerts: List<CultureAlert> = emptyList(),
    val lineageInfo: LineageInfo? = null
) {
    val isOverdue: Boolean
        get() = lastObservationDate?.let {
            LocalDate.now().isAfter(it.plusDays(7))
        } ?: true

    val statusColor: String
        get() = when {
            alerts.any { it.severity == AlertSeverity.CRITICAL } -> "#FF0000"
            alerts.any { it.severity == AlertSeverity.HIGH } -> "#FF8800"
            culture.status == CultureStatus.CONTAMINATED -> "#CC0000"
            culture.status == CultureStatus.DISPOSED -> "#666666"
            healthScore >= 80 -> "#00AA00"
            healthScore >= 60 -> "#AAAA00"
            else -> "#AA0000"
        }
}

/**
 * Response object for culture timeline with aggregated data
 */
data class CultureTimelineResponse(
    val cultures: List<CultureTimelineItem>,
    val totalCount: Int,
    val hasMore: Boolean,
    val nextPageToken: String?,
    val summary: TimelineSummary
)

data class CultureTimelineItem(
    val id: String,
    val cultureId: String,
    val species: String,
    val status: CultureStatus,
    val updatedAt: Instant,
    val subcultureCount: Int,
    val lastObservation: LocalDate?,
    val alertLevel: AlertLevel,
    val daysWithoutObservation: Int?,
    val healthScore: Int,
    val thumbnailUrl: String? = null
)

data class TimelineSummary(
    val totalCultures: Int,
    val healthyCultures: Int,
    val contaminatedCultures: Int,
    val overdueObservations: Int,
    val averageHealthScore: Double
)

enum class AlertLevel {
    NONE, LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * Detailed culture lineage information
 */
data class LineageInfo(
    val parentCultureId: String?,
    val generation: Int,
    val totalSubcultures: Int,
    val successfulSubcultures: Int,
    val lineageTree: LineageNode
)

data class LineageNode(
    val cultureId: String,
    val species: String,
    val status: CultureStatus,
    val createdAt: LocalDate,
    val children: List<LineageNode> = emptyList()
)
```

### **Observation Response Objects**

```kotlin
/**
 * Response object for observation with metadata
 */
data class ObservationResponse(
    val observation: Observation,
    val photos: List<PhotoMetadata> = emptyList(),
    val analysisResults: ObservationAnalysis? = null,
    val recommendations: List<String> = emptyList()
)

data class PhotoMetadata(
    val id: String,
    val filename: String,
    val thumbnailUrl: String,
    val fullSizeUrl: String,
    val fileSize: Long,
    val dimensions: PhotoDimensions,
    val capturedAt: Instant
)

data class PhotoDimensions(
    val width: Int,
    val height: Int
) {
    val aspectRatio: Double get() = width.toDouble() / height.toDouble()
    val megapixels: Double get() = (width * height) / 1_000_000.0
}

/**
 * AI-powered observation analysis
 */
data class ObservationAnalysis(
    val contaminationRisk: ContaminationRisk,
    val growthAssessment: GrowthAssessment,
    val recommendations: List<ActionRecommendation>,
    val confidenceScore: Double // 0.0 to 1.0
)

data class ContaminationRisk(
    val level: RiskLevel,
    val probability: Double,
    val indicators: List<String>,
    val recommendedActions: List<String>
)

data class GrowthAssessment(
    val overallHealth: HealthRating,
    val growthRate: GrowthRate,
    val developmentStage: DevelopmentStage,
    val issues: List<GrowthIssue>
)

enum class RiskLevel {
    VERY_LOW, LOW, MODERATE, HIGH, VERY_HIGH
}

enum class HealthRating {
    POOR, FAIR, GOOD, EXCELLENT
}

enum class GrowthRate {
    STAGNANT, SLOW, NORMAL, FAST, VERY_FAST
}
```

### **Recipe Response Objects**

```kotlin
/**
 * Response object for recipe with usage statistics
 */
data class RecipeResponse(
    val recipe: Recipe,
    val usageStats: RecipeUsageStats,
    val ratings: RecipeRatings,
    val compatibility: List<PlantCompatibility> = emptyList()
)

data class RecipeUsageStats(
    val totalUsage: Int,
    val successRate: Double,
    val averageGrowthRate: Double,
    val popularityRank: Int,
    val recentUsage: List<UsageTrend>
)

data class RecipeRatings(
    val averageRating: Double,
    val totalRatings: Int,
    val ratingDistribution: Map<Int, Int>, // Rating (1-5) to count
    val userRating: Int? = null // Current user's rating
)

data class PlantCompatibility(
    val plantType: String,
    val compatibilityScore: Double,
    val successRate: Double,
    val sampleSize: Int
)

data class UsageTrend(
    val period: String, // "2024-01", "2024-02", etc.
    val usageCount: Int,
    val successRate: Double
)
```

## **Sync Response Objects**

```kotlin
/**
 * Response object for sync operations
 */
data class SyncSummary(
    var successCount: Int = 0,
    var failureCount: Int = 0,
    var conflictCount: Int = 0,
    var downloadCount: Int = 0,
    val startTime: Instant = Instant.now(),
    var endTime: Instant? = null,
    val errors: MutableList<SyncError> = mutableListOf()
) {
    val isComplete: Boolean get() = endTime != null
    val totalOperations: Int get() = successCount + failureCount + conflictCount
    val duration: Duration? get() = endTime?.let { Duration.between(startTime, it) }
    val successRate: Double get() = if (totalOperations > 0) successCount.toDouble() / totalOperations else 0.0
}

data class SyncError(
    val entityType: String,
    val entityId: String,
    val operation: SyncOperation,
    val errorMessage: String,
    val timestamp: Instant = Instant.now(),
    val isRetryable: Boolean = true
)

data class ConflictDetails(
    val conflictId: String,
    val entityType: String,
    val entityId: String,
    val conflictType: ConflictType,
    val localVersion: Any,
    val remoteVersion: Any,
    val detectedAt: Instant,
    val suggestedResolution: ConflictResolutionStrategy?
)
```

## **Pagination and Search DTOs**

```kotlin
/**
 * Generic paginated result wrapper
 */
data class PaginatedResult<T>(
    val items: List<T>,
    val page: Int,
    val pageSize: Int,
    val totalCount: Int,
    val hasMore: Boolean
) {
    val totalPages: Int = (totalCount + pageSize - 1) / pageSize
    val isFirstPage: Boolean = page == 0
    val isLastPage: Boolean = !hasMore
}

/**
 * Search request with filters and sorting
 */
data class SearchRequest(
    val query: String? = null,
    val filters: Map<String, String> = emptyMap(),
    val sortBy: String = "updated_at",
    val sortOrder: SortOrder = SortOrder.DESC,
    val page: Int = 0,
    val pageSize: Int = 50
) {
    init {
        require(page >= 0) { "Page must be non-negative" }
        require(pageSize in 1..100) { "Page size must be between 1 and 100" }
    }
}

enum class SortOrder {
    ASC, DESC
}

/**
 * Culture-specific search query
 */
data class CultureSearchQuery(
    val species: String? = null,
    val status: CultureStatus? = null,
    val dateRange: DateRange? = null,
    val tags: List<String> = emptyList(),
    val sortBy: SortBy = SortBy.UPDATED_AT,
    val sortOrder: SortOrder = SortOrder.DESC
)

data class DateRange(
    val startDate: LocalDate,
    val endDate: LocalDate
) {
    init {
        require(!startDate.isAfter(endDate)) { "Start date must be before or equal to end date" }
        require(!startDate.isBefore(LocalDate.now().minusYears(10))) { "Start date too far in the past" }
        require(!endDate.isAfter(LocalDate.now())) { "End date cannot be in the future" }
    }
}

enum class SortBy {
    UPDATED_AT, CREATED_AT, SPECIES, STATUS, HEALTH_SCORE
}
```

## **Validation Framework**

```kotlin
/**
 * Validation result with detailed error information
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String>,
    val warnings: List<String> = emptyList()
) {
    fun throwIfInvalid() {
        if (!isValid) {
            throw ValidationException(errors)
        }
    }
}

/**
 * Field-specific validation error
 */
data class FieldError(
    val field: String,
    val message: String,
    val rejectedValue: Any?,
    val code: String
)

/**
 * Comprehensive validation result with field-level errors
 */
data class DetailedValidationResult(
    val isValid: Boolean,
    val fieldErrors: List<FieldError> = emptyList(),
    val globalErrors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)

/**
 * Base interface for validatable DTOs
 */
interface Validatable {
    fun validate(): ValidationResult
    fun validateDetailed(): DetailedValidationResult = DetailedValidationResult(
        isValid = validate().isValid,
        globalErrors = validate().errors,
        warnings = validate().warnings
    )
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After DTO implementation and API contract validation