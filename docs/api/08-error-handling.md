# Error Handling & Exception Management

## Overview
This document defines the comprehensive error handling strategies, custom exception hierarchy, and resilience patterns used throughout CultureStack's API layers.

---

## **Custom Exception Hierarchy**

```kotlin
/**
 * Base exception for CultureStack business logic errors
 */
sealed class CultureStackException(
    message: String,
    cause: Throwable? = null
) : Exception(message, cause) {
    abstract val errorCode: String
    abstract val userMessage: String
    open val retryable: Boolean = false
    open val severity: ErrorSeverity = ErrorSeverity.ERROR
}

/**
 * Validation errors for user input
 */
class ValidationException(
    val errors: List<String>
) : CultureStackException(
    message = "Validation failed: ${errors.joinToString(", ")}",
    cause = null
) {
    override val errorCode: String = "VALIDATION_ERROR"
    override val userMessage: String = "Please check your input: ${errors.joinToString("; ")}"
    override val severity: ErrorSeverity = ErrorSeverity.WARNING
}

/**
 * Database constraint violations
 */
class ConstraintViolationException(
    val constraint: String,
    message: String
) : CultureStackException(message, null) {
    override val errorCode: String = "CONSTRAINT_VIOLATION"
    override val userMessage: String = when (constraint) {
        "unique_culture_id" -> "A culture with this ID already exists"
        "foreign_key_culture" -> "Referenced culture does not exist"
        else -> "Database constraint violation"
    }
}

/**
 * Resource not found errors
 */
class NotFoundException(
    val resourceType: String,
    val resourceId: String
) : CultureStackException("$resourceType not found: $resourceId", null) {
    override val errorCode: String = "RESOURCE_NOT_FOUND"
    override val userMessage: String = "${resourceType.capitalize()} not found"
}

/**
 * Sync-related errors
 */
class SyncException(
    message: String,
    cause: Throwable? = null
) : CultureStackException(message, cause) {
    override val errorCode: String = "SYNC_ERROR"
    override val userMessage: String = "Synchronization failed. Please try again later."
    override val retryable: Boolean = true
}

class SyncConflictException(
    message: String,
    val localEntity: Any,
    val remoteEntity: Any
) : SyncException(message) {
    override val errorCode: String = "SYNC_CONFLICT"
    override val userMessage: String = "Data conflict detected. Please resolve before continuing."
    override val retryable: Boolean = false
}

/**
 * Authentication and authorization errors
 */
class AuthenticationException(
    message: String
) : CultureStackException(message, null) {
    override val errorCode: String = "AUTHENTICATION_ERROR"
    override val userMessage: String = "Please sign in to continue"
}

class AuthorizationException(
    message: String
) : CultureStackException(message, null) {
    override val errorCode: String = "AUTHORIZATION_ERROR"
    override val userMessage: String = "You don't have permission to perform this action"
}

/**
 * External service errors
 */
class ExternalServiceException(
    val service: String,
    message: String,
    cause: Throwable? = null
) : CultureStackException("$service error: $message", cause) {
    override val errorCode: String = "EXTERNAL_SERVICE_ERROR"
    override val userMessage: String = when (service) {
        "GoogleDrive" -> "Cloud storage is temporarily unavailable"
        "Firebase" -> "Authentication service is temporarily unavailable"
        else -> "External service temporarily unavailable"
    }
    override val retryable: Boolean = true
}

/**
 * Business rule violation errors
 */
class BusinessRuleException(
    val ruleId: String,
    message: String
) : CultureStackException(message, null) {
    override val errorCode: String = "BUSINESS_RULE_VIOLATION"
    override val userMessage: String = when (ruleId) {
        "max_subcultures_exceeded" -> "Maximum number of subcultures reached for this culture"
        "invalid_status_transition" -> "Cannot change culture status in current state"
        "contaminated_culture_operation" -> "Cannot perform this operation on contaminated cultures"
        else -> "Business rule violation: $message"
    }
}

/**
 * Rate limiting and quota errors
 */
class RateLimitException(
    val retryAfter: Duration?
) : CultureStackException("Rate limit exceeded", null) {
    override val errorCode: String = "RATE_LIMIT_EXCEEDED"
    override val userMessage: String = retryAfter?.let {
        "Too many requests. Please wait ${it.toMinutes()} minutes before trying again."
    } ?: "Too many requests. Please try again later."
    override val retryable: Boolean = true
}

enum class ErrorSeverity {
    INFO, WARNING, ERROR, CRITICAL
}
```

## **Error Response Models**

```kotlin
/**
 * Standardized error response for API
 */
data class ErrorResponse(
    val error: ErrorDetail,
    val timestamp: Instant = Instant.now(),
    val path: String? = null,
    val requestId: String = UUID.randomUUID().toString()
)

data class ErrorDetail(
    val code: String,
    val message: String,
    val userMessage: String,
    val severity: ErrorSeverity,
    val retryable: Boolean = false,
    val details: Map<String, Any> = emptyMap(),
    val fieldErrors: List<FieldError> = emptyList()
)

data class FieldError(
    val field: String,
    val message: String,
    val rejectedValue: Any?,
    val code: String
)

/**
 * Error response builder
 */
object ErrorResponseBuilder {

    fun fromException(exception: CultureStackException, path: String? = null): ErrorResponse {
        return ErrorResponse(
            error = ErrorDetail(
                code = exception.errorCode,
                message = exception.message ?: "Unknown error",
                userMessage = exception.userMessage,
                severity = exception.severity,
                retryable = exception.retryable,
                details = extractDetails(exception)
            ),
            path = path
        )
    }

    fun fromValidationException(exception: ValidationException): ErrorResponse {
        val fieldErrors = exception.errors.mapIndexed { index, error ->
            FieldError(
                field = "validation[$index]",
                message = error,
                rejectedValue = null,
                code = "VALIDATION_FAILED"
            )
        }

        return ErrorResponse(
            error = ErrorDetail(
                code = exception.errorCode,
                message = exception.message ?: "Validation failed",
                userMessage = exception.userMessage,
                severity = exception.severity,
                fieldErrors = fieldErrors
            )
        )
    }

    private fun extractDetails(exception: CultureStackException): Map<String, Any> {
        return when (exception) {
            is SyncConflictException -> mapOf(
                "conflictType" to "data_conflict",
                "hasLocalChanges" to true,
                "hasRemoteChanges" to true
            )
            is RateLimitException -> mapOf(
                "retryAfter" to (exception.retryAfter?.toSeconds() ?: 60)
            )
            is ExternalServiceException -> mapOf(
                "service" to exception.service,
                "retryable" to exception.retryable
            )
            else -> emptyMap()
        }
    }
}
```

## **Result Pattern Implementation**

```kotlin
/**
 * Result wrapper for operations that can fail
 */
sealed class Result<out T> {
    data class Success<T>(val value: T) : Result<T>()
    data class Failure(val exception: Throwable) : Result<Nothing>()

    val isSuccess: Boolean get() = this is Success
    val isFailure: Boolean get() = this is Failure

    fun getOrNull(): T? = when (this) {
        is Success -> value
        is Failure -> null
    }

    fun getOrThrow(): T = when (this) {
        is Success -> value
        is Failure -> throw exception
    }

    fun getOrElse(defaultValue: T): T = when (this) {
        is Success -> value
        is Failure -> defaultValue
    }

    fun exceptionOrNull(): Throwable? = when (this) {
        is Success -> null
        is Failure -> exception
    }

    inline fun <R> map(transform: (T) -> R): Result<R> = when (this) {
        is Success -> Success(transform(value))
        is Failure -> this
    }

    inline fun <R> flatMap(transform: (T) -> Result<R>): Result<R> = when (this) {
        is Success -> transform(value)
        is Failure -> this
    }

    inline fun onSuccess(action: (T) -> Unit): Result<T> {
        if (this is Success) action(value)
        return this
    }

    inline fun onFailure(action: (Throwable) -> Unit): Result<T> {
        if (this is Failure) action(exception)
        return this
    }

    companion object {
        fun <T> success(value: T): Result<T> = Success(value)
        fun failure(exception: Throwable): Result<Nothing> = Failure(exception)

        inline fun <T> runCatching(block: () -> T): Result<T> = try {
            Success(block())
        } catch (e: Exception) {
            Failure(e)
        }
    }
}

/**
 * Extension functions for Result
 */
inline fun <T> Result<T>.fold(
    onSuccess: (T) -> Unit,
    onFailure: (Throwable) -> Unit
) {
    when (this) {
        is Result.Success -> onSuccess(value)
        is Result.Failure -> onFailure(exception)
    }
}

suspend inline fun <T> Result<T>.suspendOnSuccess(action: suspend (T) -> Unit): Result<T> {
    if (this is Result.Success) action(value)
    return this
}

suspend inline fun <T> Result<T>.suspendOnFailure(action: suspend (Throwable) -> Unit): Result<T> {
    if (this is Result.Failure) action(exception)
    return this
}
```

## **Resilience Patterns**

### **Circuit Breaker Pattern**

```kotlin
class CircuitBreaker(
    private val failureThreshold: Int = 5,
    private val recoveryTimeout: Duration = Duration.ofMinutes(1),
    private val monitoringPeriod: Duration = Duration.ofSeconds(10)
) {
    private var state: CircuitState = CircuitState.CLOSED
    private var failureCount = 0
    private var lastFailureTime: Instant? = null
    private var lastStateChange: Instant = Instant.now()

    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        when (state) {
            CircuitState.OPEN -> {
                if (shouldAttemptReset()) {
                    state = CircuitState.HALF_OPEN
                    return executeOperation(operation)
                }
                return Result.failure(
                    CircuitBreakerException("Circuit breaker is OPEN - service unavailable")
                )
            }
            CircuitState.HALF_OPEN -> {
                return executeOperation(operation)
            }
            CircuitState.CLOSED -> {
                return executeOperation(operation)
            }
        }
    }

    private suspend fun <T> executeOperation(operation: suspend () -> T): Result<T> {
        return try {
            val result = operation()
            onSuccess()
            Result.success(result)
        } catch (e: Exception) {
            onFailure()
            Result.failure(e)
        }
    }

    private fun onSuccess() {
        failureCount = 0
        if (state == CircuitState.HALF_OPEN) {
            state = CircuitState.CLOSED
            lastStateChange = Instant.now()
        }
    }

    private fun onFailure() {
        failureCount++
        lastFailureTime = Instant.now()

        if (failureCount >= failureThreshold) {
            state = CircuitState.OPEN
            lastStateChange = Instant.now()
        }
    }

    private fun shouldAttemptReset(): Boolean {
        return lastFailureTime?.let {
            Duration.between(it, Instant.now()) >= recoveryTimeout
        } ?: false
    }
}

enum class CircuitState {
    CLOSED, OPEN, HALF_OPEN
}

class CircuitBreakerException(message: String) : Exception(message)
```

### **Retry Policy**

```kotlin
class RetryPolicy(
    private val maxAttempts: Int = 3,
    private val baseDelay: Duration = Duration.ofSeconds(1),
    private val maxDelay: Duration = Duration.ofMinutes(1),
    private val backoffMultiplier: Double = 2.0,
    private val retryableExceptions: Set<Class<out Exception>> = setOf(
        SyncException::class.java,
        ExternalServiceException::class.java,
        RateLimitException::class.java
    )
) {

    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        var lastException: Exception? = null
        var delay = baseDelay

        repeat(maxAttempts) { attempt ->
            try {
                return Result.success(operation())
            } catch (e: Exception) {
                lastException = e

                if (!isRetryable(e) || attempt == maxAttempts - 1) {
                    return Result.failure(e)
                }

                // Wait before retry
                delay(delay.toMillis())

                // Calculate next delay with exponential backoff
                delay = Duration.ofMillis(
                    minOf(
                        (delay.toMillis() * backoffMultiplier).toLong(),
                        maxDelay.toMillis()
                    )
                )
            }
        }

        return Result.failure(
            lastException ?: Exception("All retry attempts failed")
        )
    }

    private fun isRetryable(exception: Exception): Boolean {
        if (exception is CultureStackException) {
            return exception.retryable
        }
        return retryableExceptions.any { it.isInstance(exception) }
    }
}
```

### **Fallback Mechanism**

```kotlin
class FallbackHandler {

    suspend fun <T> withFallback(
        primary: suspend () -> T,
        fallback: suspend () -> T,
        condition: (Throwable) -> Boolean = { true }
    ): Result<T> {
        return try {
            Result.success(primary())
        } catch (e: Exception) {
            if (condition(e)) {
                try {
                    Result.success(fallback())
                } catch (fallbackException: Exception) {
                    Result.failure(FallbackException("Both primary and fallback operations failed", e, fallbackException))
                }
            } else {
                Result.failure(e)
            }
        }
    }
}

class FallbackException(
    message: String,
    val primaryException: Throwable,
    val fallbackException: Throwable
) : Exception(message)
```

## **Global Error Handler**

```kotlin
@Component
class GlobalErrorHandler @Inject constructor(
    private val logger: Logger,
    private val analyticsService: AnalyticsService,
    private val notificationService: NotificationService
) {

    suspend fun handleError(
        error: Throwable,
        context: ErrorContext
    ): ErrorResponse {

        // Log the error
        logError(error, context)

        // Track error for analytics
        trackError(error, context)

        // Send critical error notifications
        handleCriticalErrors(error, context)

        // Convert to standardized response
        return when (error) {
            is CultureStackException -> ErrorResponseBuilder.fromException(error, context.path)
            is ValidationException -> ErrorResponseBuilder.fromValidationException(error)
            else -> createGenericErrorResponse(error, context)
        }
    }

    private fun logError(error: Throwable, context: ErrorContext) {
        when {
            error is CultureStackException && error.severity == ErrorSeverity.WARNING -> {
                logger.warn("Warning: ${error.message}", error)
            }
            error is CultureStackException && error.severity == ErrorSeverity.CRITICAL -> {
                logger.error("Critical error: ${error.message}", error)
            }
            else -> {
                logger.error("Error in ${context.operation}: ${error.message}", error)
            }
        }
    }

    private suspend fun trackError(error: Throwable, context: ErrorContext) {
        val errorData = mapOf(
            "error_type" to error::class.simpleName,
            "error_code" to (error as? CultureStackException)?.errorCode,
            "operation" to context.operation,
            "user_id" to context.userId,
            "severity" to (error as? CultureStackException)?.severity?.name
        ).filterValues { it != null }

        analyticsService.trackError(errorData)
    }

    private suspend fun handleCriticalErrors(error: Throwable, context: ErrorContext) {
        if (error is CultureStackException && error.severity == ErrorSeverity.CRITICAL) {
            // Send alert to administrators
            notificationService.sendAdminAlert(
                "Critical Error in CultureStack",
                "Error: ${error.message}\nContext: ${context.operation}\nUser: ${context.userId}"
            )
        }
    }

    private fun createGenericErrorResponse(error: Throwable, context: ErrorContext): ErrorResponse {
        return ErrorResponse(
            error = ErrorDetail(
                code = "INTERNAL_ERROR",
                message = "An unexpected error occurred",
                userMessage = "Something went wrong. Please try again later.",
                severity = ErrorSeverity.ERROR,
                retryable = true
            ),
            path = context.path
        )
    }
}

data class ErrorContext(
    val operation: String,
    val userId: String?,
    val path: String? = null,
    val metadata: Map<String, Any> = emptyMap()
)
```

## **Error Recovery Strategies**

```kotlin
class ErrorRecoveryService @Inject constructor(
    private val cacheManager: CacheManager,
    private val syncService: SyncService,
    private val backupService: BackupService
) {

    suspend fun recoverFromDatabaseError(error: Exception): Result<Unit> {
        return when (error) {
            is SQLiteException -> {
                if (error.message?.contains("disk I/O error") == true) {
                    // Attempt to recover from I/O error
                    recoverFromDiskError()
                } else {
                    Result.failure(error)
                }
            }
            is SQLiteDatabaseCorruptException -> {
                // Attempt database recovery
                recoverFromCorruption()
            }
            else -> Result.failure(error)
        }
    }

    private suspend fun recoverFromDiskError(): Result<Unit> {
        return try {
            // Clear cache to free up space
            cacheManager.clearAll()

            // Compact database
            backupService.compactDatabase()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private suspend fun recoverFromCorruption(): Result<Unit> {
        return try {
            // Restore from backup if available
            if (backupService.hasValidBackup()) {
                backupService.restoreFromBackup()
                Result.success(Unit)
            } else {
                // Reset to clean state and trigger full sync
                backupService.resetDatabase()
                syncService.performFullSync()
                Result.success(Unit)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

## **Error Testing Strategies**

```kotlin
@Test
class ErrorHandlingTest {

    @Test
    fun `validation exception contains detailed field errors`() {
        val request = CreateCultureRequest(
            species = "", // Invalid
            explantType = "Invalid Type", // Invalid
            initiationDate = LocalDate.now().plusDays(1), // Invalid
            mediumComposition = "",
            initialConditions = ""
        )

        val validationResult = request.validate()

        assertFalse(validationResult.isValid)
        assertTrue(validationResult.errors.size >= 3)
        assertTrue(validationResult.errors.any { it.contains("species") })
        assertTrue(validationResult.errors.any { it.contains("explant") })
        assertTrue(validationResult.errors.any { it.contains("date") })
    }

    @Test
    fun `circuit breaker opens after threshold failures`() = runTest {
        val circuitBreaker = CircuitBreaker(failureThreshold = 2)
        var callCount = 0

        // Simulate failing operation
        val failingOperation: suspend () -> String = {
            callCount++
            throw Exception("Service unavailable")
        }

        // First failure
        val result1 = circuitBreaker.execute(failingOperation)
        assertTrue(result1.isFailure)

        // Second failure - should trip circuit breaker
        val result2 = circuitBreaker.execute(failingOperation)
        assertTrue(result2.isFailure)

        // Third call should be rejected by circuit breaker
        val result3 = circuitBreaker.execute(failingOperation)
        assertTrue(result3.isFailure)
        assertTrue(result3.exceptionOrNull() is CircuitBreakerException)

        // Should not have called the operation a third time
        assertEquals(2, callCount)
    }

    @Test
    fun `retry policy respects max attempts`() = runTest {
        val retryPolicy = RetryPolicy(maxAttempts = 3)
        var attempts = 0

        val failingOperation: suspend () -> String = {
            attempts++
            throw SyncException("Temporary failure")
        }

        val result = retryPolicy.execute(failingOperation)

        assertTrue(result.isFailure)
        assertEquals(3, attempts)
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After error handling implementation and resilience testing