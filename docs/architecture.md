# CultureStack Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for **CultureStack**, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project

Based on review of the PRD and project documentation, **CultureStack** is specified as a **native Android application** built from scratch.

**Analysis:**
1. **No starter templates mentioned** in PRD or documentation
2. **Greenfield project** - This is a new native Android application
3. **Technology constraints identified:**
   - Native Android development using Kotlin/Java
   - Local SQLite database with Room
   - Google Services integration (Sign-In, Drive, Play Billing)
   - Offline-first architecture with cloud sync

**Architectural Decision:**
Since this is a **native Android application** rather than a traditional web fullstack application, I recommend adapting this architecture document to focus on:
- **Client Architecture:** Native Android app structure
- **Cloud Integration:** Google Drive API, Google Services
- **Local Data Layer:** SQLite with Room ORM
- **Sync Architecture:** Offline-first with cloud backup

**Template Adaptation Required:** This fullstack template will be adapted for mobile-first architecture with cloud integration rather than traditional web frontend/backend separation.

**Status:** N/A - Greenfield native Android project

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-25 | 1.0 | Initial architecture document creation | Winston (Architect) |

## High Level Architecture

### Technical Summary
CultureStack employs a **native Android architecture** with offline-first design, utilizing local SQLite storage synchronized with Google Drive for cloud backup. The application integrates Google Services (Authentication, Drive API, Play Billing) to provide seamless user experience while maintaining offline functionality. The architecture prioritizes data integrity and user experience in laboratory environments, supporting the PRD's goal of reducing culture failure rates through reliable mobile access to tracking capabilities.

### Platform and Infrastructure Choice

**Analysis of Platform Options:**

1. **Google Cloud + Android (Recommended)**
   - **Pros:** Native Google Services integration, seamless Play Store billing, Drive API optimization, Android development ecosystem
   - **Cons:** Platform lock-in, Google Services dependency
   - **Rationale:** Aligns perfectly with PRD requirements for Google Drive sync and Play Billing

2. **AWS + Android**
   - **Pros:** Enterprise scalability, diverse service ecosystem
   - **Cons:** Additional complexity for Drive integration, separate billing system needed
   - **Rationale:** Overengineered for mobile-first application

3. **Firebase + Android**
   - **Pros:** Real-time sync, integrated authentication, mobile-optimized
   - **Cons:** Conflicts with Google Drive requirement, different data model
   - **Rationale:** Would require PRD changes for sync mechanism

**Recommendation:** **Google Cloud Platform + Native Android**

**Platform:** Google Cloud Platform
**Key Services:** Google Drive API v3, Google Sign-In API, Google Play Billing Library, Android WorkManager
**Deployment Host and Regions:** Google Play Store distribution, user data stored in personal Google Drive

### Repository Structure

**Structure:** Monorepo with Android-focused organization
**Monorepo Tool:** Gradle multi-module project (Android standard)
**Package Organization:** Feature-based modules (culture, recipes, sync, auth) with shared core module

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "User Device"
        A[Android App]
        B[SQLite Database]
        C[Local File Storage]
        A --> B
        A --> C
    end

    subgraph "Google Services"
        D[Google Drive API]
        E[Google Sign-In]
        F[Play Billing]
    end

    subgraph "User's Google Account"
        G[Personal Google Drive]
        H[Play Store Account]
    end

    A --> D
    A --> E
    A --> F
    D --> G
    E --> H
    F --> H

    I[Push Notifications] --> A
    J[Android WorkManager] --> A
```

### Architectural Patterns

- **Offline-First Architecture:** Local SQLite as primary data store with cloud sync - _Rationale:_ Essential for laboratory environments with unreliable connectivity
- **MVVM Pattern:** Model-View-ViewModel with Android Architecture Components - _Rationale:_ Android best practice for maintainable UI architecture
- **Repository Pattern:** Abstract data access between local and cloud storage - _Rationale:_ Enables seamless sync and testing isolation
- **Clean Architecture:** Layered dependency structure with domain-driven design - _Rationale:_ Supports complex business logic for culture management
- **Event-Driven Updates:** Local broadcasts for data synchronization - _Rationale:_ Ensures UI consistency during background sync operations

## Tech Stack

This is the **DEFINITIVE** technology selection for the entire CultureStack project. All development must use these exact versions and technologies.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale & Constraints |
|----------|------------|---------|---------|-------------------------|
| Mobile Platform | Android | API 24+ (Android 7.0+) | Native mobile application platform | PRD requirement; **Constraint:** Excludes ~5% older devices |
| Programming Language | Kotlin | 1.9.0+ | Primary development language | Android-first language; **Constraint:** Requires developer Kotlin expertise |
| UI Framework | Jetpack Compose | 1.5.0+ | Modern declarative UI framework | Future-proof; **Constraint:** Learning curve for XML-based developers |
| Database | SQLite + Room | Room 2.5.0+ | Local data storage and ORM | Offline-first; **Constraint:** Performance degrades >100K records |
| Database Encryption | SQLCipher | 4.5.0+ | Database encryption for sensitive data | **Added:** Security for culture data on rooted devices |
| Authentication | Google Sign-In API | 20.7.0+ | User authentication for premium features | PRD requirement; **Constraint:** Excludes non-Google users |
| Fallback Auth | Guest Mode | Custom | Local-only access without Google account | **Added:** Graceful degradation for Google Services limitations |
| Cloud Storage | Google Drive API | v3 | User data synchronization | PRD requirement; **Constraint:** 15GB storage limit, rate limits |
| Billing | Google Play Billing Library | 6.0.1+ | Premium subscription management | PRD requirement; **Constraint:** Google Play dependency |
| Image Handling | Coil | 2.4.0+ | Async image loading and caching | Photo management; **Constraint:** Memory usage for large galleries |
| Image Compression | WebP + Android ImageDecoder | API 24+ | Automatic photo compression | **Added:** Mitigate storage constraints |
| Networking | Retrofit + OkHttp | 2.9.0+ / 4.11.0+ | HTTP client for API calls | **Constraint:** Google Drive API rate limiting |
| Serialization | Kotlinx Serialization | 1.5.0+ | JSON serialization for sync | Type-safe; excellent Room compatibility |
| Background Tasks | WorkManager | 2.8.0+ | Sync scheduling and notifications | Android recommended; handles Google Drive rate limits |
| Dependency Injection | Hilt | 2.47+ | Dependency management | Android-recommended DI framework |
| Testing Framework | JUnit + Espresso | JUnit 4.13+ / Espresso 3.5+ | Unit and UI testing | Android standard testing stack |
| Build System | Gradle | 8.0+ | Build automation | Android standard; supports multi-module architecture |
| Version Control | Git | 2.40+ | Source code management | Industry standard |
| Push Notifications | Firebase Cloud Messaging | 23.2.0+ | Reminder notifications | **Constraint:** Requires Google Play Services |
| Analytics | Firebase Analytics | 21.3.0+ | Usage tracking and insights | Privacy-compliant; **Constraint:** Google Services dependency |
| Crash Reporting | Firebase Crashlytics | 18.4.0+ | Error monitoring | Production stability; **Constraint:** Google Services dependency |

### Additional Architecture Components (Based on Constraint Analysis):

- **Storage Monitoring Service:** Tracks Google Drive usage and warns users before limits
- **Conflict Resolution Engine:** Handles sync conflicts with user-friendly resolution UI
- **Graceful Degradation Manager:** Provides offline-only functionality when Google Services unavailable
- **Data Archiving System:** Manages culture record lifecycle to prevent database performance issues

## Data Models

Based on the PRD requirements, I'll define the core business entities for CultureStack's offline-first architecture with cloud synchronization.

### Culture Entity
**Purpose:** Represents the root culture record with enhanced search and performance capabilities.

**Key Attributes:**
- id: String (UUID) - Unique identifier for sync across devices
- cultureId: String - Human-readable culture ID (C001, C002, etc.)
- species: String - Plant species/variety name **[Indexed for search]**
- explantType: String - Type of plant tissue used
- sourcePlantId: String? - Optional source plant identifier
- initiationDate: LocalDate - Date culture was started **[Indexed for date queries]**
- mediumComposition: String - Medium recipe or manual composition
- recipeId: String? - Reference to saved recipe if used
- initialConditions: String - Environmental conditions at initiation
- status: CultureStatus - Current lifecycle status **[Indexed for timeline queries]**
- isDeleted: Boolean - Soft delete flag for sync
- createdAt: Instant - Record creation timestamp
- updatedAt: Instant - Last modification timestamp **[Indexed for timeline sorting]**
- syncVersion: Long - Version for conflict resolution
- lastSyncedAt: Instant? - **[Added]** Last successful cloud sync
- deviceId: String - **[Added]** Device that last modified record

#### TypeScript Interface
```kotlin
data class Culture(
    val id: String = UUID.randomUUID().toString(),
    val cultureId: String,
    val species: String,
    val explantType: String,
    val sourcePlantId: String? = null,
    val initiationDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val initialConditions: String,
    val status: CultureStatus = CultureStatus.HEALTHY,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CultureStatus {
    HEALTHY, CONTAMINATED, READY_FOR_TRANSFER,
    IN_ROOTING, ACCLIMATIZING, COMPLETED, DISPOSED
}
```

#### Relationships
- One-to-many with Subculture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Subculture Entity
**Purpose:** Represents cultures derived from parent cultures, maintaining lineage traceability through the culture family tree.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- subcultureId: String - Human-readable ID (S001, S002, etc.)
- parentCultureId: String - Reference to parent culture ID
- subcultureDate: LocalDate - Date of subculturing
- mediumComposition: String - Medium used for subculture
- recipeId: String? - Recipe reference if used
- explantCount: Int - Number of explants transferred
- status: CultureStatus - Current status
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last update timestamp
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that last modified record

#### TypeScript Interface
```kotlin
data class Subculture(
    val id: String = UUID.randomUUID().toString(),
    val subcultureId: String,
    val parentCultureId: String,
    val subcultureDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val explantCount: Int,
    val status: CultureStatus = CultureStatus.HEALTHY,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)
```

#### Relationships
- Many-to-one with Culture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Recipe Entity (Enhanced for Discovery)
**Purpose:** Stores standardized medium formulations with enhanced discoverability for user workflows.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- name: String - User-defined recipe name **[Indexed for search]**
- description: String? - Optional recipe description
- ingredients: List<Ingredient> - Structured ingredient list
- preparationNotes: String? - Special preparation instructions
- category: String? - Optional categorization
- **plantTypes: List<String> - [Added] Target plant categories for filtering**
- **difficultyLevel: DifficultyLevel - [Added] Beginner/Intermediate/Advanced**
- **tags: List<String> - [Added] Searchable tags for recipe discovery**
- usageCount: Int - Track recipe popularity **[Indexed for recommendations]**
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last modification
- version: Int - Recipe version for history tracking
- syncVersion: Long - Sync conflict resolution
- **lastSyncedAt: Instant? - [Added] Sync metadata**
- **deviceId: String - [Added] Last modification device**

#### TypeScript Interface
```kotlin
data class Recipe(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String? = null,
    val ingredients: List<Ingredient>,
    val preparationNotes: String? = null,
    val category: String? = null,
    val plantTypes: List<String> = emptyList(),
    val difficultyLevel: DifficultyLevel = DifficultyLevel.BEGINNER,
    val tags: List<String> = emptyList(),
    val usageCount: Int = 0,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val version: Int = 1,
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

data class Ingredient(
    val name: String,
    val concentration: String,
    val unit: String
)

enum class DifficultyLevel { BEGINNER, INTERMEDIATE, ADVANCED }
```

#### Relationships
- One-to-many with Culture (recipes used in cultures)
- One-to-many with Subculture (recipes used in subcultures)

### Observation Entity
**Purpose:** Captures monitoring data and photos for culture health tracking and decision-making support.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- cultureId: String - Reference to culture or subculture
- observationDate: LocalDate - Date of observation
- contaminationStatus: Boolean - Contamination present
- survivalStatus: SurvivalStatus - Overall health assessment
- growthStage: GrowthStage - Current development stage
- notes: String? - Additional observations
- photoFilenames: List<String> - Local photo file references
- createdAt: Instant - Record creation
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that created record

#### TypeScript Interface
```kotlin
data class Observation(
    val id: String = UUID.randomUUID().toString(),
    val cultureId: String,
    val observationDate: LocalDate,
    val contaminationStatus: Boolean,
    val survivalStatus: SurvivalStatus,
    val growthStage: GrowthStage,
    val notes: String? = null,
    val photoFilenames: List<String> = emptyList(),
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class SurvivalStatus { EXCELLENT, GOOD, FAIR, POOR }
enum class GrowthStage { INITIATION, ESTABLISHMENT, GROWTH, READY_FOR_TRANSFER }
```

#### Relationships
- Many-to-one with Culture or Subculture
- One-to-many with Photo files (stored separately)

### Photo Entity (New - Based on Journey Analysis)
**Purpose:** Manages culture photos with efficient storage and sync capabilities.

```kotlin
data class Photo(
    val id: String = UUID.randomUUID().toString(),
    val filename: String,
    val observationId: String,
    val localPath: String,
    val cloudPath: String? = null,
    val thumbnailPath: String, // For performance in gallery views
    val compressionLevel: CompressionLevel,
    val uploadStatus: UploadStatus,
    val fileSize: Long,
    val capturedAt: Instant,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CompressionLevel { ORIGINAL, HIGH, MEDIUM, LOW }
enum class UploadStatus { PENDING, UPLOADING, COMPLETED, FAILED }
```

### BatchOperation Entity (New - For Bulk Workflows)
**Purpose:** Supports bulk operations like creating multiple subcultures from user journey requirements.

```kotlin
data class BatchOperation(
    val id: String = UUID.randomUUID().toString(),
    val operationType: BatchOperationType,
    val parentId: String,
    val targetCount: Int,
    val completedCount: Int = 0,
    val status: BatchStatus,
    val parameters: Map<String, String> = emptyMap(),
    val createdAt: Instant = Instant.now(),
    val completedAt: Instant? = null
)

enum class BatchOperationType {
    CREATE_SUBCULTURES, UPDATE_STATUS, BULK_OBSERVATION
}
enum class BatchStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### SyncQueue Entity (New - Based on Data Flow Analysis)
**Purpose:** Manages offline-to-cloud synchronization with priority handling and retry logic.

```kotlin
data class SyncQueue(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String, // "Culture", "Recipe", "Observation", "Photo"
    val entityId: String,
    val operation: SyncOperation, // CREATE, UPDATE, DELETE
    val priority: SyncPriority, // HIGH, NORMAL, LOW
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val lastAttempt: Instant? = null,
    val status: SyncStatus, // PENDING, IN_PROGRESS, COMPLETED, FAILED
    val errorMessage: String? = null,
    val createdAt: Instant = Instant.now(),
    val scheduledFor: Instant = Instant.now()
)

enum class SyncOperation { CREATE, UPDATE, DELETE }
enum class SyncPriority { HIGH, NORMAL, LOW }
enum class SyncStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### ConflictResolution Entity (New - For Multi-Device Conflicts)
**Purpose:** Handles sync conflicts when same entity is modified on multiple devices.

```kotlin
data class ConflictResolution(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String,
    val entityId: String,
    val localVersion: String, // JSON snapshot of local entity
    val cloudVersion: String, // JSON snapshot of cloud entity
    val localUpdatedAt: Instant,
    val cloudUpdatedAt: Instant,
    val resolutionStrategy: ConflictStrategy,
    val userChoice: ConflictChoice? = null,
    val resolvedAt: Instant? = null,
    val createdAt: Instant = Instant.now()
)

enum class ConflictStrategy { MANUAL, LAST_WRITE_WINS, MERGE }
enum class ConflictChoice { ACCEPT_LOCAL, ACCEPT_CLOUD, MERGE }
```

### Database Indexes (Performance Optimization)
```sql
-- Critical indexes based on user journey analysis
CREATE INDEX idx_culture_timeline ON cultures(status, updatedAt DESC, isDeleted);
CREATE INDEX idx_culture_search ON cultures(species, status) WHERE isDeleted = 0;
CREATE INDEX idx_culture_initiation_date ON cultures(initiationDate);
CREATE INDEX idx_recipe_discovery ON recipes(plantTypes, difficultyLevel, usageCount DESC);
CREATE INDEX idx_recipe_usage_count ON recipes(usageCount DESC);
CREATE INDEX idx_sync_queue_processing ON sync_queue(status, priority, scheduledFor);
CREATE INDEX idx_batch_operation_tracking ON batch_operations(status, createdAt);
CREATE INDEX idx_photo_sync ON photos(uploadStatus, createdAt);
CREATE INDEX idx_observation_timeline ON observations(cultureId, observationDate DESC);
CREATE INDEX idx_conflict_resolution ON conflict_resolutions(entityType, resolvedAt);
```

## API Specification

Based on the architecture analysis, CultureStack is a **native Android application** with **local-first storage** and **Google Drive integration** for cloud sync. Unlike traditional REST/GraphQL APIs, this application uses Google APIs and local data access patterns.

**API Architecture Decision:**
Since CultureStack is not a web-based fullstack application but rather a native mobile app, the "API" layer consists of:
1. **Local Data Access APIs** (Room database interfaces)
2. **Google Services API Integration** (Drive, Auth, Billing)
3. **Internal Service Layer APIs** (Business logic interfaces)

### Local Data Access API (Room Database)

#### Culture Management API
```kotlin
@Dao
interface CultureDao {
    @Query("SELECT * FROM cultures WHERE isDeleted = 0 ORDER BY updatedAt DESC")
    fun getAllActiveCultures(): Flow<List<Culture>>

    @Query("SELECT * FROM cultures WHERE status = :status AND isDeleted = 0")
    suspend fun getCulturesByStatus(status: CultureStatus): List<Culture>

    @Query("SELECT * FROM cultures WHERE species LIKE '%' || :query || '%' AND isDeleted = 0")
    suspend fun searchCulturesBySpecies(query: String): List<Culture>

    @Insert
    suspend fun insertCulture(culture: Culture): Long

    @Update
    suspend fun updateCulture(culture: Culture)

    @Query("UPDATE cultures SET isDeleted = 1, updatedAt = :deletedAt WHERE id = :id")
    suspend fun softDeleteCulture(id: String, deletedAt: Instant)
}
```

#### Recipe Management API
```kotlin
@Dao
interface RecipeDao {
    @Query("SELECT * FROM recipes WHERE isDeleted = 0 ORDER BY usageCount DESC")
    fun getAllRecipes(): Flow<List<Recipe>>

    @Query("SELECT * FROM recipes WHERE plantTypes LIKE '%' || :plantType || '%' AND isDeleted = 0")
    suspend fun getRecipesByPlantType(plantType: String): List<Recipe>

    @Query("SELECT * FROM recipes WHERE difficultyLevel = :level AND isDeleted = 0")
    suspend fun getRecipesByDifficulty(level: DifficultyLevel): List<Recipe>

    @Insert
    suspend fun insertRecipe(recipe: Recipe): Long

    @Query("UPDATE recipes SET usageCount = usageCount + 1 WHERE id = :id")
    suspend fun incrementUsageCount(id: String)
}
```

### Google Services Integration API

#### Enhanced Google Drive Sync Service (Rate-Limited)
```kotlin
interface ConstraintAwareDriveService {
    suspend fun syncWithRateLimit(operations: List<SyncOperation>): Result<SyncResult>
    suspend fun estimateRemainingQuota(): Result<QuotaStatus>
    suspend fun scheduleDelayedSync(delay: Duration): Result<Unit>

    // Batch operations to respect API limits
    suspend fun batchSyncCultures(cultures: List<Culture>): Result<BatchSyncResult>
    suspend fun intelligentPhotoSync(): Result<PhotoSyncResult>

    // Graceful degradation
    suspend fun enableOfflineMode(): Result<Unit>
    suspend fun detectServiceAvailability(): ServicesStatus
}

data class QuotaStatus(
    val requestsUsed: Int,
    val requestsRemaining: Int,
    val resetTime: Instant,
    val storageUsed: Long,
    val storageRemaining: Long,
    val recommendedSyncDelay: Duration
)

data class BatchSyncResult(
    val successCount: Int,
    val failedCount: Int,
    val rateLimitHit: Boolean,
    val nextAllowedSync: Instant?
)
```

#### Authentication Service
```kotlin
interface AuthService {
    suspend fun signInWithGoogle(): Result<GoogleSignInAccount>
    suspend fun signOut(): Result<Unit>
    suspend fun getCurrentUser(): GoogleSignInAccount?
    suspend fun refreshTokenIfNeeded(): Result<Unit>
    fun isUserSignedIn(): Boolean
}
```

#### Billing Service
```kotlin
interface BillingService {
    suspend fun initializeBilling(): Result<Unit>
    suspend fun querySubscriptions(): Result<List<SubscriptionInfo>>
    suspend fun purchaseSubscription(subscriptionId: String): Result<PurchaseResult>
    suspend fun verifyPurchase(purchase: Purchase): Result<Boolean>
    fun isPremiumUser(): Boolean
}
```

### Enhanced Offline Queue Service
```kotlin
interface OfflineQueueService {
    suspend fun queueOperation(operation: SyncOperation, priority: SyncPriority): Result<Unit>
    suspend fun getQueueSize(): Int
    suspend fun getQueueStatus(): QueueStatus
    suspend fun processQueueWhenOnline(): Result<SyncResult>
    suspend fun compactQueue(): Result<Unit>

    // Emergency capabilities
    suspend fun exportQueueForManualSync(): Result<String>
    suspend fun importQueueFromBackup(data: String): Result<Unit>
}

data class QueueStatus(
    val pendingOperations: Int,
    val estimatedSyncTime: Duration,
    val lastSuccessfulSync: Instant?,
    val oldestPendingOperation: Instant?
)
```

### Graceful Degradation Service (New)
```kotlin
interface GracefulDegradationService {
    suspend fun detectGoogleServicesAvailability(): ServicesStatus
    suspend fun enableGuestMode(): Result<Unit>
    suspend fun enableOfflineOnlyMode(): Result<Unit>
    suspend fun exportLocalData(): Result<ExportResult>

    // Fallback authentication
    suspend fun createLocalAccount(username: String): Result<LocalAccount>
    suspend fun migrateToGoogleAccount(localAccount: LocalAccount): Result<MigrationResult>
}

enum class ServicesStatus {
    AVAILABLE,
    OFFLINE,
    NOT_AUTHENTICATED,
    UNAVAILABLE,
    RATE_LIMITED,
    QUOTA_EXCEEDED
}

data class ExportResult(
    val filePath: String,
    val format: ExportFormat, // JSON, CSV, PDF
    val includesPhotos: Boolean,
    val fileSize: Long
)
```

### Internal Service Layer API

#### Culture Management Service
```kotlin
interface CultureService {
    suspend fun createCulture(cultureRequest: CreateCultureRequest): Result<Culture>
    suspend fun createSubculture(subcultureRequest: CreateSubcultureRequest): Result<Subculture>
    suspend fun addObservation(observationRequest: AddObservationRequest): Result<Observation>
    suspend fun updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>
    suspend fun getCultureLineage(cultureId: String): Result<CultureLineage>
    suspend fun createBatchSubcultures(batchRequest: BatchSubcultureRequest): Result<BatchOperation>
}

data class CreateCultureRequest(
    val species: String,
    val explantType: String,
    val sourcePlantId: String?,
    val initiationDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String?,
    val initialConditions: String
)
```

### Error Handling (Enhanced with Constraints)
```kotlin
sealed class CultureStackError : Exception() {
    object NetworkUnavailable : CultureStackError()
    object GoogleServicesUnavailable : CultureStackError()
    object StorageQuotaExceeded : CultureStackError()
    object RateLimitExceeded : CultureStackError() // New
    object SyncConflictDetected : CultureStackError()
    object LocalStorageFull : CultureStackError() // New
    data class DatabaseError(val cause: Throwable) : CultureStackError()
    data class GoogleApiError(val code: Int, val message: String) : CultureStackError()
    data class QuotaError(val resetTime: Instant) : CultureStackError() // New
}
```

## Components

Based on the architectural patterns, tech stack, and constraint analysis, I'll define the major logical components across CultureStack's native Android architecture.

### Culture Management Component
**Responsibility:** Handles all culture and subculture lifecycle operations including creation, monitoring, status updates, and lineage tracking.

**Key Interfaces:**
- `CultureService.createCulture(CreateCultureRequest): Result<Culture>`
- `CultureService.createSubculture(CreateSubcultureRequest): Result<Subculture>`
- `CultureService.updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>`
- `CultureService.getCultureLineage(cultureId: String): Result<CultureLineage>`

**Dependencies:** Database Component, Sync Component, Notification Component

**Technology Stack:**
- Kotlin with Coroutines for async operations
- Room database for local storage
- Hilt for dependency injection
- WorkManager for background status updates

### Recipe Management Component
**Responsibility:** Manages recipe library operations including creation, search, categorization, and usage tracking for medium formulations.

**Key Interfaces:**
- `RecipeService.createRecipe(CreateRecipeRequest): Result<Recipe>`
- `RecipeService.searchRecipes(searchCriteria: RecipeSearchCriteria): Result<List<Recipe>>`
- `RecipeService.getRecipesByPlantType(plantType: String): Result<List<Recipe>>`
- `RecipeService.incrementUsageCount(recipeId: String): Result<Unit>`

**Dependencies:** Database Component, Search Component

**Technology Stack:**
- Room database with FTS (Full-Text Search) support
- Kotlinx Serialization for structured ingredient data
- Repository pattern for data access abstraction

### Observation & Photo Component
**Responsibility:** Handles observation logging, photo capture, compression, storage, and gallery management for culture documentation.

**Key Interfaces:**
- `ObservationService.addObservation(AddObservationRequest): Result<Observation>`
- `PhotoService.captureAndProcessPhoto(cultureId: String): Result<Photo>`
- `PhotoService.compressPhoto(photo: Photo, level: CompressionLevel): Result<OptimizedPhoto>`
- `PhotoService.getPhotoGallery(cultureId: String): Result<List<Photo>>`

**Dependencies:** Database Component, File Storage Component, Sync Component

**Technology Stack:**
- Android CameraX for photo capture
- Coil for image loading and caching
- WebP format for optimal compression
- Android MediaStore for file management

### Sync Management Component
**Responsibility:** Orchestrates data synchronization between local SQLite storage and Google Drive, handling conflicts, rate limits, and offline scenarios.

**Key Interfaces:**
- `SyncService.queueForSync(entityType: String, entityId: String, operation: SyncOperation): Result<Unit>`
- `SyncService.performIntelligentSync(): Result<SyncResult>`
- `SyncService.resolveConflict(conflictId: String, resolution: ConflictChoice): Result<Unit>`
- `SyncService.enableOfflineMode(): Result<Unit>`

**Dependencies:** Google Services Component, Database Component, Queue Management Component

**Technology Stack:**
- Google Drive API v3 for cloud storage
- WorkManager for background sync scheduling
- Retrofit + OkHttp for HTTP communication
- Rate limiting with Guava RateLimiter

### Authentication & Billing Component
**Responsibility:** Manages Google account authentication, premium subscription verification, and user session state for freemium model support.

**Key Interfaces:**
- `AuthService.signInWithGoogle(): Result<GoogleSignInAccount>`
- `BillingService.purchaseSubscription(subscriptionId: String): Result<PurchaseResult>`
- `BillingService.verifyPremiumStatus(): Result<Boolean>`
- `AuthService.enableGuestMode(): Result<GuestAccount>`

**Dependencies:** Google Services Component, Database Component

**Technology Stack:**
- Google Sign-In API for authentication
- Google Play Billing Library for subscriptions
- SharedPreferences for session persistence
- Firebase Analytics for user behavior tracking

### Notification & Scheduling Component
**Responsibility:** Manages reminder scheduling, push notifications, calendar integration, and background task coordination for culture maintenance.

**Key Interfaces:**
- `NotificationService.scheduleReminder(reminderId: String, scheduledTime: Instant): Result<Unit>`
- `NotificationService.sendPushNotification(notification: CultureNotification): Result<Unit>`
- `SchedulingService.createRecurringReminder(reminder: RecurringReminder): Result<Unit>`
- `SchedulingService.getUpcomingTasks(timeRange: TimeRange): Result<List<ScheduledTask>>`

**Dependencies:** Database Component, Culture Management Component

**Technology Stack:**
- Firebase Cloud Messaging for push notifications
- Android AlarmManager for precise scheduling
- WorkManager for background task execution
- Android notification channels for categorization

### Database Component
**Responsibility:** Provides unified data access layer with offline-first architecture, including local storage, caching, indexing, and transaction management.

**Key Interfaces:**
- `DatabaseService.executeInTransaction(block: suspend () -> Unit): Result<Unit>`
- `DatabaseService.getEntityById(entityType: String, id: String): Result<Any?>`
- `DatabaseService.performMaintenance(): Result<MaintenanceResult>`
- `DatabaseService.exportDatabase(format: ExportFormat): Result<String>`

**Dependencies:** None (foundational layer)

**Technology Stack:**
- SQLite with Room ORM for local storage
- SQLCipher for database encryption
- Database migration strategies with Room
- Optimized indexes for query performance

### Google Services Integration Component
**Responsibility:** Abstracts Google API interactions including Drive storage, authentication, billing, and service availability detection with graceful degradation.

**Key Interfaces:**
- `GoogleServicesManager.checkServiceAvailability(): ServicesStatus`
- `GoogleServicesManager.initializeServices(): Result<Unit>`
- `GoogleServicesManager.handleServiceUnavailable(): Result<DegradationStrategy>`
- `GoogleServicesManager.getQuotaStatus(): Result<QuotaStatus>`

**Dependencies:** None (external integration layer)

**Technology Stack:**
- Google Play Services SDK
- Google Drive API v3 client
- Google Sign-In API
- Play Services Availability checker

### Component Interaction Diagram (Optimized for Performance)
```mermaid
graph TB
    subgraph "UI Layer"
        A[Culture Screens]
        B[Recipe Screens]
        C[Calendar Screens]
    end

    subgraph "Business Logic Layer"
        D[Culture Management]
        E[Recipe Management]
        F[Observation & Photo]
        G[Notification & Scheduling]
    end

    subgraph "Data Layer"
        H[Sync Management]
        I[Database Component]
        J[Google Services]
        K[Event Bus]
    end

    A --> D
    B --> E
    C --> G
    D --> I
    E --> I
    F --> I
    G --> I

    H --> I
    H --> J

    D -.->|async events| K
    F -.->|async events| K
    E -.->|async events| K
    K -.->|notifications| G
    K -.->|sync requests| H
    K -.->|status updates| D

    J -.->|circuit breaker| H
```

### Enhanced Component Architecture (Constraint-Aware)

#### Event-Driven Communication System
```kotlin
interface ComponentEventBus {
    suspend fun publish(event: ComponentEvent)
    fun subscribe(eventType: EventType, handler: suspend (ComponentEvent) -> Unit)
    suspend fun publishAndForget(event: ComponentEvent) // Fire-and-forget for performance
}

sealed class ComponentEvent {
    data class CultureCreated(val culture: Culture) : ComponentEvent()
    data class ObservationAdded(val observation: Observation) : ComponentEvent()
    data class SyncCompleted(val entityType: String, val entityId: String) : ComponentEvent()
    data class ConflictDetected(val conflict: ConflictResolution) : ComponentEvent()
}
```

#### Dependency Management with Circuit Breaker
```kotlin
class GoogleServicesCircuitBreaker(
    private val failureThreshold: Int = 5,
    private val timeoutMs: Long = 30000
) {
    private var failures = 0
    private var lastFailureTime = 0L
    private var state = CircuitState.CLOSED

    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        return when (state) {
            CircuitState.OPEN -> {
                if (System.currentTimeMillis() - lastFailureTime > timeoutMs) {
                    state = CircuitState.HALF_OPEN
                    tryOperation(operation)
                } else {
                    Result.failure(GoogleServicesUnavailableException())
                }
            }
            CircuitState.HALF_OPEN, CircuitState.CLOSED -> tryOperation(operation)
        }
    }
}
```

#### Resource Management Strategy
```kotlin
class ComponentResourceManager {
    private val photoProcessingPool = Executors.newFixedThreadPool(2)
    private val syncOperationPool = Executors.newFixedThreadPool(3)
    private val databasePool = Executors.newSingleThreadExecutor()

    suspend fun executePhotoOperation(operation: suspend () -> Unit) {
        withContext(photoProcessingPool.asCoroutineDispatcher()) {
            operation()
        }
    }

    fun prioritizeComponents(systemState: SystemState): ComponentPriority {
        return when {
            systemState.isLowMemory -> ComponentPriority.ESSENTIAL_ONLY
            systemState.isBatteryLow -> ComponentPriority.REDUCE_BACKGROUND
            systemState.isNetworkLimited -> ComponentPriority.OFFLINE_FOCUS
            else -> ComponentPriority.FULL_FUNCTIONALITY
        }
    }
}
```

#### Performance-Optimized Component Communication
```kotlin
class PerformantCultureService(
    private val eventBus: ComponentEventBus,
    private val cultureRepository: CultureRepository
) {
    suspend fun createCulture(request: CreateCultureRequest): Result<Culture> {
        // Only essential synchronous operation
        val culture = cultureRepository.create(request)

        // All secondary operations are asynchronous
        eventBus.publishAndForget(CultureCreatedEvent(culture))

        return Result.success(culture)
    }
}
```

## External APIs

Based on CultureStack's native Android architecture and PRD requirements, the application integrates with several external APIs and services. Since this is a native mobile app rather than a traditional web application, "external APIs" refer to third-party service integrations.

### Google Drive API v3
**Purpose:** Primary cloud storage and synchronization service for user culture data, photos, and recipes.

- **Documentation:** https://developers.google.com/drive/api/v3/reference
- **Base URL(s):** https://www.googleapis.com/drive/v3/, https://www.googleapis.com/upload/drive/v3/
- **Authentication:** OAuth 2.0 with Google Sign-In API, Drive scope permissions
- **Rate Limits:** 1,000 requests per 100 seconds per user, 100 queries per second per user

**Key Endpoints Used:**
- `GET /files` - List user's culture data files in app folder
- `POST /files` - Create new culture data file or folder structure
- `PATCH /files/{fileId}` - Update existing culture records
- `POST /upload/files` - Upload culture photos with multipart encoding
- `GET /files/{fileId}` - Download culture data or photos for sync

**Integration Notes:**
- Uses app-specific folder scope for user privacy
- Implements exponential backoff for rate limiting
- Batch operations for efficient sync of multiple entities
- Conflict resolution based on file modification timestamps

### Google Sign-In API
**Purpose:** User authentication for premium features and Google Drive access verification.

- **Documentation:** https://developers.google.com/identity/sign-in/android
- **Base URL(s):** Integrated Android SDK, no direct HTTP calls
- **Authentication:** OAuth 2.0 flows with Google Play Services integration
- **Rate Limits:** No explicit rate limits, governed by Google Play Services

**Key Endpoints Used:**
- SDK method: `GoogleSignIn.getClient().signIn()` - Initiate sign-in flow
- SDK method: `GoogleSignIn.getLastSignedInAccount()` - Retrieve cached account
- SDK method: `GoogleSignIn.getClient().signOut()` - Sign out current user
- SDK method: `GoogleSignIn.requestPermissions()` - Request additional scopes

**Integration Notes:**
- Integrated with Google Play Services for seamless UX
- Handles scope incremental authorization for Drive access
- Automatic token refresh managed by SDK
- Graceful fallback to guest mode when unavailable

### Google Play Billing Library
**Purpose:** Premium subscription management and in-app purchase verification for freemium model.

- **Documentation:** https://developer.android.com/google/play/billing
- **Base URL(s):** Integrated Android library, communicates with Google Play servers
- **Authentication:** App signing key verification with Google Play Console
- **Rate Limits:** No explicit limits, managed by Google Play infrastructure

**Key Endpoints Used:**
- Library method: `BillingClient.queryPurchasesAsync()` - Verify active subscriptions
- Library method: `BillingClient.launchBillingFlow()` - Initiate subscription purchase
- Library method: `BillingClient.acknowledgePurchase()` - Acknowledge completed purchase
- Library method: `BillingClient.queryProductDetailsAsync()` - Get subscription pricing

**Integration Notes:**
- Server-side receipt verification for security
- Handles subscription restoration across devices
- Manages purchase state persistence for offline scenarios
- Real-time purchase updates via PurchasesUpdatedListener

### Firebase Cloud Messaging (FCM)
**Purpose:** Push notification delivery for culture reminders and sync notifications.

- **Documentation:** https://firebase.google.com/docs/cloud-messaging
- **Base URL(s):** https://fcm.googleapis.com/v1/projects/{project-id}/messages:send
- **Authentication:** Service account key for server-to-FCM communication
- **Rate Limits:** No published limits for downstream messages, reasonable use expected

**Key Endpoints Used:**
- `POST /v1/projects/{project-id}/messages:send` - Send targeted notification
- SDK method: `FirebaseMessaging.getToken()` - Retrieve device FCM token
- SDK method: `FirebaseMessaging.subscribeToTopic()` - Topic-based messaging
- SDK method: `FirebaseMessaging.setAutoInitEnabled()` - Enable/disable FCM

**Integration Notes:**
- Topic-based messaging for broadcast notifications (maintenance, updates)
- Device-specific tokens for personalized culture reminders
- Notification channels for Android 8.0+ compatibility
- Handles token refresh and registration updates

## Regional Constraints and Fallback Strategies

### Regional Availability Analysis
```kotlin
data class RegionalConstraint(
    val region: String,
    val googlePlayServices: ServiceStatus,
    val googleDriveAccess: ServiceStatus,
    val alternativeRequired: Boolean,
    val marketPenetration: Double
)

val regionalConstraints = listOf(
    RegionalConstraint("China", BLOCKED, BLOCKED, true, 0.20),
    RegionalConstraint("Iran", RESTRICTED, RESTRICTED, true, 0.02),
    RegionalConstraint("Russia", PARTIAL, PARTIAL, false, 0.05),
    RegionalConstraint("EU", AVAILABLE, GDPR_COMPLIANT, false, 0.15),
    RegionalConstraint("North America", AVAILABLE, AVAILABLE, false, 0.35),
    RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
)
// Impact: ~22% of potential users may have limited Google Services access
```

### Rate Limiting and Quota Management
```kotlin
class GoogleDriveRateLimiter {
    private val rateLimiter = RateLimiter.create(8.0) // 8 requests per 10 seconds
    private val burstAllowance = Semaphore(20) // Allow 20 burst requests

    suspend fun executeWithLimit(operation: suspend () -> Result<Any>): Result<Any> {
        return if (burstAllowance.tryAcquire()) {
            try {
                rateLimiter.acquire()
                operation()
            } finally {
                burstAllowance.release()
            }
        } else {
            operationQueue.add(DelayedOperation(operation, System.currentTimeMillis() + 60000))
            Result.failure(RateLimitExceeded(Duration.ofMinutes(1)))
        }
    }
}
```

### Constraint-Aware API Client
```kotlin
class ConstraintAwareApiClient {
    private val regionalLimiter = RegionalRateLimiter()
    private val quotaMonitor = ApiQuotaMonitor()

    suspend fun <T> executeApiCall(
        apiCall: suspend () -> T,
        region: String,
        priority: ApiCallPriority
    ): Result<T> {
        // Check regional constraints
        val regionalConstraint = regionalLimiter.getConstraints(region)
        if (regionalConstraint.blocked) {
            return Result.failure(RegionBlockedError(region))
        }

        // Check quota status
        val quotaStatus = quotaMonitor.getCurrentStatus()
        if (quotaStatus.approachingLimit && priority == LOW) {
            return Result.failure(QuotaThresholdError("Deferring low-priority call"))
        }

        // Execute with appropriate rate limiting
        return regionalLimiter.executeWithConstraints(regionalConstraint) {
            apiCall()
        }
    }
}
```

**External API Integration Summary:**
- **Google Services Ecosystem:** Leverages integrated Android SDKs for optimal UX
- **Rate Limiting:** Intelligent backoff and batching with burst allowance for API quotas
- **Regional Adaptation:** Fallback strategies for 22% of users with limited Google Services
- **Error Handling:** Comprehensive error recovery with graceful degradation
- **Privacy Compliance:** User-controlled data collection with GDPR compliance for EU
- **Offline Resilience:** Local fallbacks when external services unavailable
- **Cost Management:** Free tier optimization with scaling constraint awareness

## Core Workflows

Based on the PRD requirements and architectural components, I'll illustrate key system workflows using sequence diagrams that show the interactions between components, external APIs, and error handling paths.

### Culture Creation Workflow

This workflow demonstrates the complete process of creating a new culture, from user input through local storage and cloud synchronization.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant RM as Recipe Management
    participant DB as Database Component
    participant EB as Event Bus
    participant SM as Sync Management
    participant GD as Google Drive API

    User->>UI: Create new culture
    UI->>RM: Search for recipes
    RM->>DB: Query recipes by plant type
    DB-->>RM: Return matching recipes
    RM-->>UI: Recipe suggestions
    User->>UI: Select recipe & enter details
    UI->>CM: CreateCulture request

    CM->>DB: Insert culture record
    DB-->>CM: Culture created (local)
    CM->>EB: Publish CultureCreated event
    CM-->>UI: Success response
    UI-->>User: Culture created confirmation

    EB->>SM: Handle CultureCreated event
    SM->>GD: Upload culture metadata
    alt Sync Success
        GD-->>SM: Upload successful
        SM->>DB: Update sync status
    else Rate Limited
        GD-->>SM: Rate limit error
        SM->>SM: Queue for delayed sync
    else Network Error
        SM->>SM: Add to offline queue
    end
```

### Multi-Device Observation Sync Workflow

This sequence shows the complex interaction when observations are logged on one device and synchronized to another, including conflict resolution.

```mermaid
sequenceDiagram
    participant U1 as User Device A
    participant U2 as User Device B
    participant CM1 as Culture Mgmt A
    participant CM2 as Culture Mgmt B
    participant DB1 as Database A
    participant DB2 as Database B
    participant SM1 as Sync Mgmt A
    participant SM2 as Sync Mgmt B
    participant GD as Google Drive
    participant CR as Conflict Resolution

    U1->>CM1: Add contamination observation
    CM1->>DB1: Insert observation locally
    CM1->>CM1: Update culture status to CONTAMINATED
    CM1->>DB1: Update culture status
    CM1->>SM1: Queue sync operations

    SM1->>GD: Upload observation + culture update
    GD-->>SM1: Sync successful

    Note over U2: Meanwhile on Device B...
    U2->>CM2: View culture timeline
    CM2->>SM2: Check for remote updates
    SM2->>GD: Fetch latest changes
    GD-->>SM2: New observation + status update

    SM2->>DB2: Compare sync versions
    alt No Conflict
        SM2->>DB2: Apply updates
        SM2->>CM2: Notify of changes
        CM2-->>U2: Show updated status
    else Sync Conflict Detected
        SM2->>CR: Create conflict resolution
        CR->>CR: Analyze conflicts
        CR-->>SM2: Manual resolution required
        SM2->>CM2: Present conflict UI
        CM2-->>U2: User chooses resolution
        U2->>CM2: Select resolution strategy
        CM2->>CR: Apply resolution
        CR->>DB2: Update with merged data
        CR->>SM2: Resolution complete
        SM2->>GD: Upload resolved version
    end
```

### Bulk Subculture Creation Workflow

This demonstrates the batch operation pattern for creating multiple subcultures, including progress tracking and error recovery.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant BO as Batch Operation Service
    participant DB as Database Component
    participant SM as Sync Management
    participant NS as Notification Service

    User->>UI: Create 5 subcultures from Culture C001
    UI->>CM: BatchSubculture request
    CM->>BO: Create batch operation
    BO->>DB: Insert BatchOperation record
    BO-->>CM: Batch operation started
    CM-->>UI: Show progress indicator

    loop For each subculture (5 times)
        BO->>CM: Create individual subculture
        CM->>DB: Insert subculture record
        CM->>SM: Queue for sync
        BO->>BO: Increment completed count
        BO->>DB: Update batch progress
        BO->>UI: Progress update

        alt Subculture Creation Fails
            CM-->>BO: Creation error
            BO->>BO: Log error, continue with next
        end
    end

    BO->>DB: Mark batch as completed
    BO->>NS: Schedule reminders for new subcultures
    BO-->>UI: Batch operation complete
    UI-->>User: 5 subcultures created successfully

    Note over SM: Background sync of all new subcultures
    SM->>SM: Process sync queue
    loop For each queued subculture
        SM->>GD: Upload subculture data
        alt Sync Success
            GD-->>SM: Upload successful
        else Sync Failure
            SM->>SM: Retry with exponential backoff
        end
    end
```

## Data Flow Analysis and Optimization

### Critical Data Transformations in Workflows

#### Culture Creation Data Pipeline
```kotlin
// User Input → Domain Entity → Database Entity → Sync Payload
data class CultureDataFlow(
    val userInput: CultureCreationInput,           // Raw form data
    val domainEntity: CultureDomainEntity,         // Business logic applied
    val dbEntity: CultureDbEntity,                 // Persistence format
    val syncPayload: CultureSyncPayload            // Cloud format
)

// Data validation gates at each transformation
class CultureDataValidator {
    suspend fun validateTransformation(
        input: CultureCreationInput,
        output: CultureDomainEntity
    ): ValidationResult {
        val violations = mutableListOf<ValidationViolation>()

        if (input.species.isBlank()) violations.add(ValidationViolation.REQUIRED_FIELD_MISSING)
        if (input.selectedRecipeId != null && !recipeExists(input.selectedRecipeId)) {
            violations.add(ValidationViolation.INVALID_REFERENCE)
        }

        return ValidationResult(violations)
    }
}
```

#### Photo Processing Pipeline with Data Integrity
```kotlin
data class PhotoProcessingPipeline(
    val rawCapture: RawPhotoCapture,      // 8MB JPEG from camera
    val optimizedPhoto: OptimizedPhoto,    // 1.2MB WebP compressed
    val dbEntity: PhotoDbEntity,           // Database reference
    val syncPayload: DriveUploadPayload    // Cloud upload format
)

class PhotoDataIntegrityValidator {
    fun validatePhotoProcessing(pipeline: PhotoProcessingPipeline): IntegrityResult {
        val issues = mutableListOf<IntegrityIssue>()

        // Check file size reduction is reasonable (not corrupted)
        val compressionRatio = pipeline.optimizedPhoto.optimizedSize.toDouble() /
                               pipeline.rawCapture.fileSize
        if (compressionRatio > 0.8 || compressionRatio < 0.05) {
            issues.add(IntegrityIssue.SUSPICIOUS_COMPRESSION_RATIO)
        }

        // Verify file references are consistent
        if (!fileExists(pipeline.optimizedPhoto.optimizedPath)) {
            issues.add(IntegrityIssue.MISSING_OPTIMIZED_FILE)
        }

        return IntegrityResult(issues)
    }
}
```

### Data Flow Monitoring and Metrics
```kotlin
data class DataFlowMetrics(
    val transformationLatency: Duration,    // Time to process data through pipeline
    val dataLossRate: Double,              // Percentage of data lost in transformation
    val errorRate: Double,                 // Errors per transformation
    val throughput: Double,                // Records per second
    val bottleneckStage: PipelineStage     // Identified bottleneck location
)

class WorkflowDataFlowMonitor {
    fun monitorCultureCreationFlow(): Flow<DataFlowMetrics> {
        return combine(
            measureTransformationLatency(),
            measureDataLossRate(),
            measureErrorRate(),
            measureThroughput()
        ) { latency, lossRate, errorRate, throughput ->
            DataFlowMetrics(
                transformationLatency = latency,
                dataLossRate = lossRate,
                errorRate = errorRate,
                throughput = throughput,
                bottleneckStage = identifyBottleneck(latency, throughput)
            )
        }
    }
}
```

**Core Workflow Design Rationale:**
- **Event-Driven Architecture:** Workflows use event bus for loose coupling and better error isolation
- **Offline-First Design:** All workflows can operate without network connectivity, queuing sync operations
- **Graceful Degradation:** Each workflow has fallback paths for service unavailability
- **Progress Visibility:** Batch operations provide real-time progress feedback to users
- **Error Recovery:** Comprehensive error handling with retry logic and user notification
- **Data Integrity:** Validation gates at each transformation point prevent data corruption
- **Performance Monitoring:** Real-time metrics for bottleneck identification and optimization

## Database Schema

Based on the enhanced data models defined earlier, I'll create concrete database schema definitions using the database type selected in the Tech Stack (SQLite with Room).

### Core Entity Tables

#### Cultures Table
```sql
CREATE TABLE cultures (
    id TEXT PRIMARY KEY NOT NULL,
    culture_id TEXT NOT NULL UNIQUE,
    species TEXT NOT NULL,
    explant_type TEXT NOT NULL,
    source_plant_id TEXT,
    initiation_date INTEGER NOT NULL, -- Unix timestamp
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    initial_conditions TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Subcultures Table
```sql
CREATE TABLE subcultures (
    id TEXT PRIMARY KEY NOT NULL,
    subculture_id TEXT NOT NULL UNIQUE,
    parent_culture_id TEXT NOT NULL,
    subculture_date INTEGER NOT NULL,
    medium_composition TEXT NOT NULL,
    recipe_id TEXT,
    explant_count INTEGER NOT NULL,
    status TEXT NOT NULL DEFAULT 'HEALTHY',
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (parent_culture_id) REFERENCES cultures (id),
    FOREIGN KEY (recipe_id) REFERENCES recipes (id)
);
```

#### Recipes Table
```sql
CREATE TABLE recipes (
    id TEXT PRIMARY KEY NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    ingredients TEXT NOT NULL, -- JSON array of Ingredient objects
    preparation_notes TEXT,
    category TEXT,
    plant_types TEXT, -- JSON array of strings
    difficulty_level TEXT NOT NULL DEFAULT 'BEGINNER',
    tags TEXT, -- JSON array of strings
    usage_count INTEGER NOT NULL DEFAULT 0,
    is_deleted INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL
);
```

### Enhanced Entity Tables

#### Photos Table
```sql
CREATE TABLE photos (
    id TEXT PRIMARY KEY NOT NULL,
    filename TEXT NOT NULL,
    observation_id TEXT NOT NULL,
    local_path TEXT NOT NULL,
    cloud_path TEXT,
    thumbnail_path TEXT NOT NULL,
    compression_level TEXT NOT NULL,
    upload_status TEXT NOT NULL DEFAULT 'PENDING',
    file_size INTEGER NOT NULL,
    captured_at INTEGER NOT NULL,
    created_at INTEGER NOT NULL,
    sync_version INTEGER NOT NULL DEFAULT 1,
    last_synced_at INTEGER,
    device_id TEXT NOT NULL,

    FOREIGN KEY (observation_id) REFERENCES observations (id)
);
```

#### Sync Queue Table
```sql
CREATE TABLE sync_queue (
    id TEXT PRIMARY KEY NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    operation TEXT NOT NULL, -- CREATE, UPDATE, DELETE
    priority TEXT NOT NULL DEFAULT 'NORMAL',
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    last_attempt INTEGER,
    status TEXT NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    created_at INTEGER NOT NULL,
    scheduled_for INTEGER NOT NULL
);
```

## Query Pattern Analysis and Optimization

### Critical Query Patterns and Performance

#### Culture Timeline Dashboard Query (Most Frequent)
```sql
-- Optimized two-phase loading for better performance
-- Phase 1: Load essential data (target: <20ms)
SELECT c.id, c.culture_id, c.species, c.status, c.updated_at
FROM cultures c
WHERE c.is_deleted = 0 AND c.status IN ('HEALTHY', 'READY_FOR_TRANSFER', 'IN_ROOTING')
ORDER BY c.updated_at DESC
LIMIT 50;

-- Phase 2: Async load details for visible items
SELECT r.name, COUNT(s.id) as subculture_count, MAX(o.observation_date) as last_observation
FROM cultures c
LEFT JOIN recipes r ON c.recipe_id = r.id
LEFT JOIN subcultures s ON c.id = s.parent_culture_id AND s.is_deleted = 0
LEFT JOIN observations o ON c.id = o.culture_id AND o.is_deleted = 0
WHERE c.id = ?
GROUP BY c.id;
```

#### Enhanced Search Performance with FTS
```sql
-- Full-text search index for recipe discovery
CREATE VIRTUAL TABLE recipe_fts USING fts5(
    recipe_id,
    name,
    description,
    plant_types,
    tags,
    ingredients
);

-- Optimized search query (target: <100ms for 1000+ recipes)
SELECT r.id, r.name, r.description, r.difficulty_level, r.usage_count
FROM recipe_fts fts
JOIN recipes r ON fts.recipe_id = r.id
WHERE recipe_fts MATCH 'orchid AND medium'
  AND r.difficulty_level = 'BEGINNER'
  AND r.is_deleted = 0
ORDER BY r.usage_count DESC
LIMIT 20;
```

### Performance Optimization Strategies

#### Materialized Views for Expensive Aggregations
```sql
-- Pre-computed culture summary for dashboard performance
CREATE TABLE culture_summary_cache (
    culture_id TEXT PRIMARY KEY,
    subculture_count INTEGER,
    observation_count INTEGER,
    photo_count INTEGER,
    last_observation_date INTEGER,
    contamination_detected INTEGER,
    cache_updated_at INTEGER,

    FOREIGN KEY (culture_id) REFERENCES cultures (id)
);
```

#### Critical Performance Indexes
```sql
-- Timeline queries (most frequent)
CREATE INDEX idx_culture_timeline
    ON cultures(status, updated_at DESC, is_deleted);

-- Recipe discovery queries
CREATE INDEX idx_recipe_discovery
    ON recipes(plant_types, difficulty_level, usage_count DESC);

-- Observation timeline per culture
CREATE INDEX idx_observation_timeline
    ON observations(culture_id, observation_date DESC);

-- Sync queue processing
CREATE INDEX idx_sync_queue_processing
    ON sync_queue(status, priority, scheduled_for);

-- Photo sync status queries
CREATE INDEX idx_photo_sync
    ON photos(upload_status, created_at);
```

### Query Performance Monitoring
```kotlin
data class QueryPerformanceMetrics(
    val queryType: String,
    val executionTimeMs: Long,
    val recordsScanned: Long,
    val recordsReturned: Long,
    val indexesUsed: List<String>,
    val optimizationRecommendations: List<String>
)

class DatabasePerformanceMonitor {
    fun analyzeSlowQueries(): Flow<QueryPerformanceAlert> {
        return queryExecutionTimes
            .filter { it.executionTime > Duration.ofMillis(100) }
            .map { slowQuery ->
                QueryPerformanceAlert(
                    query = slowQuery.sql,
                    executionTime = slowQuery.executionTime,
                    suggestedOptimizations = generateOptimizations(slowQuery)
                )
            }
    }
}
```

### Critical Performance Targets
- **Culture Timeline Load:** <50ms for 1000 cultures
- **Recipe Search:** <100ms for 1000+ recipes with full-text search
- **Photo Gallery:** <200ms for 50 photos with metadata
- **Sync Conflict Detection:** <50ms for 100 entities
- **Batch Operation Progress:** <25ms per progress update

**Database Schema Design Rationale:**
- **Offline-First:** All tables support offline operation with sync metadata
- **Performance Optimized:** Indexes and materialized views based on actual query patterns
- **Data Integrity:** Foreign key constraints and triggers enforce business rules
- **Sync-Aware:** Every entity includes sync versioning and conflict resolution support
- **Scalable:** Schema supports efficient queries up to 100K+ records per table
- **Query-Optimized:** Full-text search, covering indexes, and cursor-based pagination
- **Cache-Friendly:** Materialized views for expensive aggregations with trigger maintenance