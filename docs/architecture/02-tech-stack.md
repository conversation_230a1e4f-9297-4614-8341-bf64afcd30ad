# CultureStack Architecture - Tech Stack & Platform

## Tech Stack

This is the **DEFINITIVE** technology selection for the entire CultureStack project. All development must use these exact versions and technologies.

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale & Constraints |
|----------|------------|---------|---------|-------------------------|
| Mobile Platform | Android | API 24+ (Android 7.0+) | Native mobile application platform | PRD requirement; **Constraint:** Excludes ~5% older devices |
| Programming Language | Kotlin | 1.9.0+ | Primary development language | Android-first language; **Constraint:** Requires developer <PERSON><PERSON><PERSON> expertise |
| UI Framework | Jetpack Compose | 1.5.0+ | Modern declarative UI framework | Future-proof; **Constraint:** Learning curve for XML-based developers |
| Database | SQLite + Room | Room 2.5.0+ | Local data storage and ORM | Offline-first; **Constraint:** Performance degrades >100K records |
| Database Encryption | SQLCipher | 4.5.0+ | Database encryption for sensitive data | **Added:** Security for culture data on rooted devices |
| Authentication | Google Sign-In API | 20.7.0+ | User authentication for premium features | PRD requirement; **Constraint:** Excludes non-Google users |
| Fallback Auth | Guest Mode | Custom | Local-only access without Google account | **Added:** Graceful degradation for Google Services limitations |
| Cloud Storage | Google Drive API | v3 | User data synchronization | PRD requirement; **Constraint:** 15GB storage limit, rate limits |
| Billing | Google Play Billing Library | 6.0.1+ | Premium subscription management | PRD requirement; **Constraint:** Google Play dependency |
| Image Handling | Coil | 2.4.0+ | Async image loading and caching | Photo management; **Constraint:** Memory usage for large galleries |
| Image Compression | WebP + Android ImageDecoder | API 24+ | Automatic photo compression | **Added:** Mitigate storage constraints |
| Networking | Retrofit + OkHttp | 2.9.0+ / 4.11.0+ | HTTP client for API calls | **Constraint:** Google Drive API rate limiting |
| Serialization | Kotlinx Serialization | 1.5.0+ | JSON serialization for sync | Type-safe; excellent Room compatibility |
| Background Tasks | WorkManager | 2.8.0+ | Sync scheduling and notifications | Android recommended; handles Google Drive rate limits |
| Dependency Injection | Hilt | 2.47+ | Dependency management | Android-recommended DI framework |
| Testing Framework | JUnit + Espresso | JUnit 4.13+ / Espresso 3.5+ | Unit and UI testing | Android standard testing stack |
| Build System | Gradle | 8.0+ | Build automation | Android standard; supports multi-module architecture |
| Version Control | Git | 2.40+ | Source code management | Industry standard |
| Push Notifications | Firebase Cloud Messaging | 23.2.0+ | Reminder notifications | **Constraint:** Requires Google Play Services |
| Analytics | Firebase Analytics | 21.3.0+ | Usage tracking and insights | Privacy-compliant; **Constraint:** Google Services dependency |
| Crash Reporting | Firebase Crashlytics | 18.4.0+ | Error monitoring | Production stability; **Constraint:** Google Services dependency |

### Additional Architecture Components (Based on Constraint Analysis):

- **Storage Monitoring Service:** Tracks Google Drive usage and warns users before limits
- **Conflict Resolution Engine:** Handles sync conflicts with user-friendly resolution UI
- **Graceful Degradation Manager:** Provides offline-only functionality when Google Services unavailable
- **Data Archiving System:** Manages culture record lifecycle to prevent database performance issues

## Platform Selection Rationale

### Google Cloud Platform Integration

**Primary Platform:** Google Cloud Platform
**Key Integration Points:**
- Google Drive API v3 for personal cloud storage
- Google Sign-In API for seamless authentication
- Google Play Services ecosystem integration
- Firebase services for notifications and analytics

**Benefits:**
- Native integration with Android development ecosystem
- Seamless user experience through Google Services
- Cost-effective for individual users (uses personal Drive storage)
- Built-in security and privacy through Google account management

**Constraints and Mitigations:**
- **Google Services Dependency:** Mitigated by guest mode and offline-first architecture
- **Regional Availability:** ~22% of potential users may have limited access - graceful degradation implemented
- **Storage Limits:** 15GB Google Drive limit - automatic compression and archiving strategies
- **Rate Limiting:** Google API quotas managed through intelligent batching and backoff strategies

### Regional Constraints and Fallback Strategies

```kotlin
data class RegionalConstraint(
    val region: String,
    val googlePlayServices: ServiceStatus,
    val googleDriveAccess: ServiceStatus,
    val alternativeRequired: Boolean,
    val marketPenetration: Double
)

val regionalConstraints = listOf(
    RegionalConstraint("China", BLOCKED, BLOCKED, true, 0.20),
    RegionalConstraint("Iran", RESTRICTED, RESTRICTED, true, 0.02),
    RegionalConstraint("Russia", PARTIAL, PARTIAL, false, 0.05),
    RegionalConstraint("EU", AVAILABLE, GDPR_COMPLIANT, false, 0.15),
    RegionalConstraint("North America", AVAILABLE, AVAILABLE, false, 0.35),
    RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
)
// Impact: ~22% of potential users may have limited Google Services access
```

### Technology Migration Strategy

**Version Upgrade Path:**
- Android API level incremental updates (maintain backward compatibility)
- Jetpack Compose migration from XML layouts (where applicable)
- Kotlin version updates with language feature adoption
- Room database migration strategies for schema changes

**Dependency Management:**
- Gradle version catalogs for consistent dependency versions
- Automatic dependency vulnerability scanning
- Regular security updates for all third-party libraries
- Performance impact assessment for major version upgrades

## Development Environment Requirements

### Minimum Development Environment
- **Android Studio:** Electric Eel (2022.1.1) or newer
- **Java Development Kit:** JDK 11 or newer
- **Android SDK:** API 24-34 installed
- **Build Tools:** 33.0.0 or newer
- **Emulator:** Android 7.0+ with Google Play Services
- **Physical Device:** Android 7.0+ for testing (recommended)

### Recommended Development Setup
- **RAM:** 16GB minimum, 32GB recommended for large projects
- **Storage:** SSD with 50GB free space for Android SDK and emulator images
- **Gradle:** Use Gradle daemon and parallel builds for performance
- **IDE Settings:** Configure code style, inspections, and live templates

### Development Workflow Tools
- **Version Control:** Git with conventional commit messages
- **Code Quality:** Detekt for static analysis, ktlint for formatting
- **Testing:** JUnit 4/5, Mockito, Espresso test framework
- **CI/CD:** GitHub Actions or similar for automated testing and builds

## Performance and Scalability Considerations

### Database Performance Targets
- **Culture Timeline Load:** <50ms for 1000 cultures
- **Recipe Search:** <100ms for 1000+ recipes with full-text search
- **Photo Gallery:** <200ms for 50 photos with metadata
- **Sync Operations:** <30 seconds for full dataset sync

### Memory Management
- **Heap Size:** Target <150MB for typical usage
- **Image Caching:** Coil with disk cache up to 250MB
- **Database Size:** Monitor and archive data beyond 100K records
- **Background Tasks:** Efficient WorkManager job scheduling

### Network Optimization
- **API Rate Limiting:** Respect Google Drive API quotas (1000 req/100sec)
- **Offline Capability:** Full functionality without network connection
- **Data Compression:** Automatic WebP compression for photos
- **Batch Operations:** Minimize API calls through intelligent batching

## Security Architecture

### Data Protection
- **Database Encryption:** SQLCipher for sensitive culture data
- **Network Security:** HTTPS for all API communications
- **Authentication:** OAuth 2.0 with Google Sign-In
- **Privacy:** GDPR-compliant data collection and processing

### Device Security
- **Root Detection:** Warning users about security risks on rooted devices
- **Certificate Pinning:** Prevent man-in-the-middle attacks
- **Secure Storage:** Android Keystore for sensitive credentials
- **Backup Security:** Encrypted backups with user-controlled keys

## Related Architecture Documents

- **[Introduction & Overview](./01-introduction.md)** - High-level architecture and patterns
- **[Data Models](./03-data-models.md)** - Business entities using these technologies
- **[API Specifications](./04-api-specifications.md)** - APIs built with this tech stack
- **[Components & Services](./05-components.md)** - Components implementing these technologies
- **[Database Schema](./08-database-schema.md)** - SQLite schema with Room implementation