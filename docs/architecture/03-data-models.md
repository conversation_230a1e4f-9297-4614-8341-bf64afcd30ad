# CultureStack Architecture - Data Models

Based on the PRD requirements, this document defines the core business entities for CultureStack's offline-first architecture with cloud synchronization.

## Core Business Entities

### Culture Entity
**Purpose:** Represents the root culture record with enhanced search and performance capabilities.

**Key Attributes:**
- id: String (UUID) - Unique identifier for sync across devices
- cultureId: String - Human-readable culture ID (C001, C002, etc.)
- species: String - Plant species/variety name **[Indexed for search]**
- explantType: String - Type of plant tissue used
- sourcePlantId: String? - Optional source plant identifier
- initiationDate: LocalDate - Date culture was started **[Indexed for date queries]**
- mediumComposition: String - Medium recipe or manual composition
- recipeId: String? - Reference to saved recipe if used
- initialConditions: String - Environmental conditions at initiation
- status: CultureStatus - Current lifecycle status **[Indexed for timeline queries]**
- isDeleted: Boolean - Soft delete flag for sync
- createdAt: Instant - Record creation timestamp
- updatedAt: Instant - Last modification timestamp **[Indexed for timeline sorting]**
- syncVersion: Long - Version for conflict resolution
- lastSyncedAt: Instant? - **[Added]** Last successful cloud sync
- deviceId: String - **[Added]** Device that last modified record

#### Kotlin Data Class
```kotlin
data class Culture(
    val id: String = UUID.randomUUID().toString(),
    val cultureId: String,
    val species: String,
    val explantType: String,
    val sourcePlantId: String? = null,
    val initiationDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val initialConditions: String,
    val status: CultureStatus = CultureStatus.HEALTHY,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CultureStatus {
    HEALTHY, CONTAMINATED, READY_FOR_TRANSFER,
    IN_ROOTING, ACCLIMATIZING, COMPLETED, DISPOSED
}
```

#### Relationships
- One-to-many with Subculture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Subculture Entity
**Purpose:** Represents cultures derived from parent cultures, maintaining lineage traceability through the culture family tree.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- subcultureId: String - Human-readable ID (S001, S002, etc.)
- parentCultureId: String - Reference to parent culture ID
- subcultureDate: LocalDate - Date of subculturing
- mediumComposition: String - Medium used for subculture
- recipeId: String? - Recipe reference if used
- explantCount: Int - Number of explants transferred
- status: CultureStatus - Current status
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last update timestamp
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that last modified record

#### Kotlin Data Class
```kotlin
data class Subculture(
    val id: String = UUID.randomUUID().toString(),
    val subcultureId: String,
    val parentCultureId: String,
    val subcultureDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String? = null,
    val explantCount: Int,
    val status: CultureStatus = CultureStatus.HEALTHY,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)
```

#### Relationships
- Many-to-one with Culture (parent relationship)
- One-to-many with Observation
- Many-to-one with Recipe (optional)

### Recipe Entity (Enhanced for Discovery)
**Purpose:** Stores standardized medium formulations with enhanced discoverability for user workflows.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- name: String - User-defined recipe name **[Indexed for search]**
- description: String? - Optional recipe description
- ingredients: List<Ingredient> - Structured ingredient list
- preparationNotes: String? - Special preparation instructions
- category: String? - Optional categorization
- **plantTypes: List<String> - [Added] Target plant categories for filtering**
- **difficultyLevel: DifficultyLevel - [Added] Beginner/Intermediate/Advanced**
- **tags: List<String> - [Added] Searchable tags for recipe discovery**
- usageCount: Int - Track recipe popularity **[Indexed for recommendations]**
- createdAt: Instant - Creation timestamp
- updatedAt: Instant - Last modification
- version: Int - Recipe version for history tracking
- syncVersion: Long - Sync conflict resolution
- **lastSyncedAt: Instant? - [Added] Sync metadata**
- **deviceId: String - [Added] Last modification device**

#### Kotlin Data Class
```kotlin
data class Recipe(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String? = null,
    val ingredients: List<Ingredient>,
    val preparationNotes: String? = null,
    val category: String? = null,
    val plantTypes: List<String> = emptyList(),
    val difficultyLevel: DifficultyLevel = DifficultyLevel.BEGINNER,
    val tags: List<String> = emptyList(),
    val usageCount: Int = 0,
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val version: Int = 1,
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

data class Ingredient(
    val name: String,
    val concentration: String,
    val unit: String
)

enum class DifficultyLevel { BEGINNER, INTERMEDIATE, ADVANCED }
```

#### Relationships
- One-to-many with Culture (recipes used in cultures)
- One-to-many with Subculture (recipes used in subcultures)

### Observation Entity
**Purpose:** Captures monitoring data and photos for culture health tracking and decision-making support.

**Key Attributes:**
- id: String (UUID) - Unique identifier
- cultureId: String - Reference to culture or subculture
- observationDate: LocalDate - Date of observation
- contaminationStatus: Boolean - Contamination present
- survivalStatus: SurvivalStatus - Overall health assessment
- growthStage: GrowthStage - Current development stage
- notes: String? - Additional observations
- photoFilenames: List<String> - Local photo file references
- createdAt: Instant - Record creation
- syncVersion: Long - Sync version control
- lastSyncedAt: Instant? - Last successful cloud sync
- deviceId: String - Device that created record

#### Kotlin Data Class
```kotlin
data class Observation(
    val id: String = UUID.randomUUID().toString(),
    val cultureId: String,
    val observationDate: LocalDate,
    val contaminationStatus: Boolean,
    val survivalStatus: SurvivalStatus,
    val growthStage: GrowthStage,
    val notes: String? = null,
    val photoFilenames: List<String> = emptyList(),
    val isDeleted: Boolean = false,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class SurvivalStatus { EXCELLENT, GOOD, FAIR, POOR }
enum class GrowthStage { INITIATION, ESTABLISHMENT, GROWTH, READY_FOR_TRANSFER }
```

#### Relationships
- Many-to-one with Culture or Subculture
- One-to-many with Photo files (stored separately)

## Enhanced Entities for System Support

### Photo Entity (Based on Journey Analysis)
**Purpose:** Manages culture photos with efficient storage and sync capabilities.

```kotlin
data class Photo(
    val id: String = UUID.randomUUID().toString(),
    val filename: String,
    val observationId: String,
    val localPath: String,
    val cloudPath: String? = null,
    val thumbnailPath: String, // For performance in gallery views
    val compressionLevel: CompressionLevel,
    val uploadStatus: UploadStatus,
    val fileSize: Long,
    val capturedAt: Instant,
    val createdAt: Instant = Instant.now(),
    val syncVersion: Long = 1,
    val lastSyncedAt: Instant? = null,
    val deviceId: String
)

enum class CompressionLevel { ORIGINAL, HIGH, MEDIUM, LOW }
enum class UploadStatus { PENDING, UPLOADING, COMPLETED, FAILED }
```

### BatchOperation Entity (For Bulk Workflows)
**Purpose:** Supports bulk operations like creating multiple subcultures from user journey requirements.

```kotlin
data class BatchOperation(
    val id: String = UUID.randomUUID().toString(),
    val operationType: BatchOperationType,
    val parentId: String,
    val targetCount: Int,
    val completedCount: Int = 0,
    val status: BatchStatus,
    val parameters: Map<String, String> = emptyMap(),
    val createdAt: Instant = Instant.now(),
    val completedAt: Instant? = null
)

enum class BatchOperationType {
    CREATE_SUBCULTURES, UPDATE_STATUS, BULK_OBSERVATION
}
enum class BatchStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

## Sync and System Support Entities

### SyncQueue Entity (Based on Data Flow Analysis)
**Purpose:** Manages offline-to-cloud synchronization with priority handling and retry logic.

```kotlin
data class SyncQueue(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String, // "Culture", "Recipe", "Observation", "Photo"
    val entityId: String,
    val operation: SyncOperation, // CREATE, UPDATE, DELETE
    val priority: SyncPriority, // HIGH, NORMAL, LOW
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val lastAttempt: Instant? = null,
    val status: SyncStatus, // PENDING, IN_PROGRESS, COMPLETED, FAILED
    val errorMessage: String? = null,
    val createdAt: Instant = Instant.now(),
    val scheduledFor: Instant = Instant.now()
)

enum class SyncOperation { CREATE, UPDATE, DELETE }
enum class SyncPriority { HIGH, NORMAL, LOW }
enum class SyncStatus { PENDING, IN_PROGRESS, COMPLETED, FAILED }
```

### ConflictResolution Entity (For Multi-Device Conflicts)
**Purpose:** Handles sync conflicts when same entity is modified on multiple devices.

```kotlin
data class ConflictResolution(
    val id: String = UUID.randomUUID().toString(),
    val entityType: String,
    val entityId: String,
    val localVersion: String, // JSON snapshot of local entity
    val cloudVersion: String, // JSON snapshot of cloud entity
    val localUpdatedAt: Instant,
    val cloudUpdatedAt: Instant,
    val resolutionStrategy: ConflictStrategy,
    val userChoice: ConflictChoice? = null,
    val resolvedAt: Instant? = null,
    val createdAt: Instant = Instant.now()
)

enum class ConflictStrategy { MANUAL, LAST_WRITE_WINS, MERGE }
enum class ConflictChoice { ACCEPT_LOCAL, ACCEPT_CLOUD, MERGE }
```

## Data Relationships and Lineage

### Entity Relationship Diagram
```mermaid
erDiagram
    CULTURE ||--o{ SUBCULTURE : "spawns"
    CULTURE ||--o{ OBSERVATION : "monitored by"
    SUBCULTURE ||--o{ OBSERVATION : "monitored by"
    RECIPE ||--o{ CULTURE : "used in"
    RECIPE ||--o{ SUBCULTURE : "used in"
    OBSERVATION ||--o{ PHOTO : "contains"
    CULTURE ||--o{ BATCH_OPERATION : "source of"
    SYNC_QUEUE ||--|| CULTURE : "syncs"
    SYNC_QUEUE ||--|| SUBCULTURE : "syncs"
    SYNC_QUEUE ||--|| RECIPE : "syncs"
    SYNC_QUEUE ||--|| OBSERVATION : "syncs"
    SYNC_QUEUE ||--|| PHOTO : "syncs"
    CONFLICT_RESOLUTION ||--|| CULTURE : "resolves"
    CONFLICT_RESOLUTION ||--|| SUBCULTURE : "resolves"
    CONFLICT_RESOLUTION ||--|| RECIPE : "resolves"
    CONFLICT_RESOLUTION ||--|| OBSERVATION : "resolves"
```

### Data Integrity Rules

#### Business Logic Constraints
- **Culture ID Uniqueness:** CultureId must be unique within user's dataset
- **Parent-Child Relationships:** Subcultures cannot be created from disposed cultures
- **Date Validation:** Subculture dates must be after parent culture initiation
- **Recipe Usage Tracking:** Usage count increments when recipe is applied to culture
- **Observation Timeline:** Observations must have logical date progression

#### Sync Integrity Rules
- **Version Control:** SyncVersion increments on every modification
- **Device Tracking:** DeviceId tracks last modification source
- **Conflict Detection:** Based on syncVersion and updatedAt comparison
- **Soft Deletes:** isDeleted flag prevents data loss during sync conflicts

## Data Validation and Constraints

### Field Validation Rules
```kotlin
object DataValidation {
    fun validateCultureCreation(culture: Culture): ValidationResult {
        val errors = mutableListOf<ValidationError>()

        if (culture.species.isBlank()) {
            errors.add(ValidationError.REQUIRED_FIELD_MISSING("species"))
        }

        if (culture.cultureId.length > 20) {
            errors.add(ValidationError.FIELD_TOO_LONG("cultureId", 20))
        }

        if (culture.initiationDate.isAfter(LocalDate.now())) {
            errors.add(ValidationError.FUTURE_DATE_NOT_ALLOWED("initiationDate"))
        }

        return ValidationResult(errors)
    }

    fun validateSubcultureCreation(
        subculture: Subculture,
        parentCulture: Culture
    ): ValidationResult {
        val errors = mutableListOf<ValidationError>()

        if (parentCulture.status == CultureStatus.DISPOSED) {
            errors.add(ValidationError.INVALID_PARENT_STATUS("Cannot subculture from disposed culture"))
        }

        if (subculture.subcultureDate.isBefore(parentCulture.initiationDate)) {
            errors.add(ValidationError.INVALID_DATE_SEQUENCE("Subculture date before parent initiation"))
        }

        return ValidationResult(errors)
    }
}

data class ValidationResult(val errors: List<ValidationError>)
data class ValidationError(val type: ErrorType, val message: String, val field: String?)

enum class ErrorType {
    REQUIRED_FIELD_MISSING,
    FIELD_TOO_LONG,
    FUTURE_DATE_NOT_ALLOWED,
    INVALID_PARENT_STATUS,
    INVALID_DATE_SEQUENCE
}
```

## Performance Optimization Entities

### Database Indexes for Performance
```sql
-- Critical indexes based on user journey analysis
CREATE INDEX idx_culture_timeline ON cultures(status, updatedAt DESC, isDeleted);
CREATE INDEX idx_culture_search ON cultures(species, status) WHERE isDeleted = 0;
CREATE INDEX idx_culture_initiation_date ON cultures(initiationDate);
CREATE INDEX idx_recipe_discovery ON recipes(plantTypes, difficultyLevel, usageCount DESC);
CREATE INDEX idx_recipe_usage_count ON recipes(usageCount DESC);
CREATE INDEX idx_sync_queue_processing ON sync_queue(status, priority, scheduledFor);
CREATE INDEX idx_batch_operation_tracking ON batch_operations(status, createdAt);
CREATE INDEX idx_photo_sync ON photos(uploadStatus, createdAt);
CREATE INDEX idx_observation_timeline ON observations(cultureId, observationDate DESC);
CREATE INDEX idx_conflict_resolution ON conflict_resolutions(entityType, resolvedAt);
```

## Related Architecture Documents

- **[Tech Stack](./02-tech-stack.md)** - Room ORM and Kotlinx Serialization implementation
- **[API Specifications](./04-api-specifications.md)** - Data access APIs for these models
- **[Database Schema](./08-database-schema.md)** - Complete SQL schema implementation
- **[Components & Services](./05-components.md)** - Components that manipulate these entities