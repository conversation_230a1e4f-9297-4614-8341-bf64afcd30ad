# CultureStack Architecture - API Specifications

Based on the architecture analysis, CultureStack is a **native Android application** with **local-first storage** and **Google Drive integration** for cloud sync. Unlike traditional REST/GraphQL APIs, this application uses Google APIs and local data access patterns.

**API Architecture Decision:**
Since CultureStack is not a web-based fullstack application but rather a native mobile app, the "API" layer consists of:
1. **Local Data Access APIs** (Room database interfaces)
2. **Google Services API Integration** (Drive, Auth, Billing)
3. **Internal Service Layer APIs** (Business logic interfaces)

## Local Data Access API (Room Database)

### Culture Management API
```kotlin
@Dao
interface CultureDao {
    @Query("SELECT * FROM cultures WHERE isDeleted = 0 ORDER BY updatedAt DESC")
    fun getAllActiveCultures(): Flow<List<Culture>>

    @Query("SELECT * FROM cultures WHERE status = :status AND isDeleted = 0")
    suspend fun getCulturesByStatus(status: CultureStatus): List<Culture>

    @Query("SELECT * FROM cultures WHERE species LIKE '%' || :query || '%' AND isDeleted = 0")
    suspend fun searchCulturesBySpecies(query: String): List<Culture>

    @Insert
    suspend fun insertCulture(culture: Culture): Long

    @Update
    suspend fun updateCulture(culture: Culture)

    @Query("UPDATE cultures SET isDeleted = 1, updatedAt = :deletedAt WHERE id = :id")
    suspend fun softDeleteCulture(id: String, deletedAt: Instant)

    @Query("SELECT * FROM cultures WHERE id = :id AND isDeleted = 0")
    suspend fun getCultureById(id: String): Culture?

    @Query("SELECT COUNT(*) FROM cultures WHERE status = :status AND isDeleted = 0")
    suspend fun getCultureCountByStatus(status: CultureStatus): Int

    @Query("SELECT * FROM cultures WHERE initiationDate BETWEEN :startDate AND :endDate AND isDeleted = 0")
    suspend fun getCulturesByDateRange(startDate: LocalDate, endDate: LocalDate): List<Culture>
}
```

### Recipe Management API
```kotlin
@Dao
interface RecipeDao {
    @Query("SELECT * FROM recipes WHERE isDeleted = 0 ORDER BY usageCount DESC")
    fun getAllRecipes(): Flow<List<Recipe>>

    @Query("SELECT * FROM recipes WHERE plantTypes LIKE '%' || :plantType || '%' AND isDeleted = 0")
    suspend fun getRecipesByPlantType(plantType: String): List<Recipe>

    @Query("SELECT * FROM recipes WHERE difficultyLevel = :level AND isDeleted = 0")
    suspend fun getRecipesByDifficulty(level: DifficultyLevel): List<Recipe>

    @Query("SELECT * FROM recipes WHERE name LIKE '%' || :query || '%' OR tags LIKE '%' || :query || '%' AND isDeleted = 0")
    suspend fun searchRecipes(query: String): List<Recipe>

    @Insert
    suspend fun insertRecipe(recipe: Recipe): Long

    @Update
    suspend fun updateRecipe(recipe: Recipe)

    @Query("UPDATE recipes SET usageCount = usageCount + 1 WHERE id = :id")
    suspend fun incrementUsageCount(id: String)

    @Query("SELECT * FROM recipes WHERE id = :id AND isDeleted = 0")
    suspend fun getRecipeById(id: String): Recipe?
}
```

### Observation Management API
```kotlin
@Dao
interface ObservationDao {
    @Query("SELECT * FROM observations WHERE cultureId = :cultureId AND isDeleted = 0 ORDER BY observationDate DESC")
    suspend fun getObservationsForCulture(cultureId: String): List<Observation>

    @Query("SELECT * FROM observations WHERE observationDate = :date AND isDeleted = 0")
    suspend fun getObservationsByDate(date: LocalDate): List<Observation>

    @Insert
    suspend fun insertObservation(observation: Observation): Long

    @Update
    suspend fun updateObservation(observation: Observation)

    @Query("SELECT * FROM observations WHERE contaminationStatus = 1 AND isDeleted = 0 ORDER BY observationDate DESC")
    suspend fun getContaminatedObservations(): List<Observation>

    @Query("SELECT COUNT(*) FROM observations WHERE cultureId = :cultureId AND isDeleted = 0")
    suspend fun getObservationCountForCulture(cultureId: String): Int
}
```

### Photo Management API
```kotlin
@Dao
interface PhotoDao {
    @Query("SELECT * FROM photos WHERE observationId = :observationId")
    suspend fun getPhotosForObservation(observationId: String): List<Photo>

    @Query("SELECT * FROM photos WHERE uploadStatus = :status")
    suspend fun getPhotosByUploadStatus(status: UploadStatus): List<Photo>

    @Insert
    suspend fun insertPhoto(photo: Photo): Long

    @Update
    suspend fun updatePhoto(photo: Photo)

    @Query("UPDATE photos SET uploadStatus = :status WHERE id = :id")
    suspend fun updatePhotoUploadStatus(id: String, status: UploadStatus)

    @Query("SELECT SUM(fileSize) FROM photos")
    suspend fun getTotalPhotoStorage(): Long
}
```

## Google Services Integration API

### Enhanced Google Drive Sync Service (Rate-Limited)
```kotlin
interface ConstraintAwareDriveService {
    suspend fun syncWithRateLimit(operations: List<SyncOperation>): Result<SyncResult>
    suspend fun estimateRemainingQuota(): Result<QuotaStatus>
    suspend fun scheduleDelayedSync(delay: Duration): Result<Unit>

    // Batch operations to respect API limits
    suspend fun batchSyncCultures(cultures: List<Culture>): Result<BatchSyncResult>
    suspend fun intelligentPhotoSync(): Result<PhotoSyncResult>

    // Graceful degradation
    suspend fun enableOfflineMode(): Result<Unit>
    suspend fun detectServiceAvailability(): ServicesStatus

    // File operations
    suspend fun uploadFile(filePath: String, metadata: FileMetadata): Result<DriveFile>
    suspend fun downloadFile(fileId: String, destinationPath: String): Result<Unit>
    suspend fun deleteFile(fileId: String): Result<Unit>
    suspend fun listFiles(query: String): Result<List<DriveFile>>
}

data class QuotaStatus(
    val requestsUsed: Int,
    val requestsRemaining: Int,
    val resetTime: Instant,
    val storageUsed: Long,
    val storageRemaining: Long,
    val recommendedSyncDelay: Duration
)

data class BatchSyncResult(
    val successCount: Int,
    val failedCount: Int,
    val rateLimitHit: Boolean,
    val nextAllowedSync: Instant?
)

data class PhotoSyncResult(
    val photosUploaded: Int,
    val photosSkipped: Int,
    val totalDataUploaded: Long,
    val compressionApplied: Boolean
)
```

### Authentication Service
```kotlin
interface AuthService {
    suspend fun signInWithGoogle(): Result<GoogleSignInAccount>
    suspend fun signOut(): Result<Unit>
    suspend fun getCurrentUser(): GoogleSignInAccount?
    suspend fun refreshTokenIfNeeded(): Result<Unit>
    fun isUserSignedIn(): Boolean
    suspend fun requestDrivePermissions(): Result<Unit>
    suspend fun requestPhotoPermissions(): Result<Unit>

    // Guest mode support
    suspend fun enableGuestMode(): Result<GuestAccount>
    suspend fun migrateGuestToGoogle(guestAccount: GuestAccount): Result<MigrationResult>
    fun isGuestMode(): Boolean
}

data class GuestAccount(
    val id: String,
    val displayName: String,
    val createdAt: Instant
)

data class MigrationResult(
    val success: Boolean,
    val migratedRecords: Int,
    val errors: List<String>
)
```

### Billing Service
```kotlin
interface BillingService {
    suspend fun initializeBilling(): Result<Unit>
    suspend fun querySubscriptions(): Result<List<SubscriptionInfo>>
    suspend fun purchaseSubscription(subscriptionId: String): Result<PurchaseResult>
    suspend fun verifyPurchase(purchase: Purchase): Result<Boolean>
    fun isPremiumUser(): Boolean
    suspend fun restorePurchases(): Result<List<Purchase>>
    suspend fun getPricingInfo(): Result<PricingInfo>

    // Subscription management
    suspend fun cancelSubscription(subscriptionId: String): Result<Unit>
    suspend fun getSubscriptionStatus(): Result<SubscriptionStatus>
}

data class SubscriptionInfo(
    val id: String,
    val title: String,
    val description: String,
    val price: String,
    val isActive: Boolean
)

data class PricingInfo(
    val monthly: String,
    val yearly: String,
    val currency: String
)
```

## Internal Service Layer API

### Culture Management Service
```kotlin
interface CultureService {
    suspend fun createCulture(cultureRequest: CreateCultureRequest): Result<Culture>
    suspend fun createSubculture(subcultureRequest: CreateSubcultureRequest): Result<Subculture>
    suspend fun addObservation(observationRequest: AddObservationRequest): Result<Observation>
    suspend fun updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>
    suspend fun getCultureLineage(cultureId: String): Result<CultureLineage>
    suspend fun createBatchSubcultures(batchRequest: BatchSubcultureRequest): Result<BatchOperation>
    suspend fun searchCultures(searchQuery: SearchQuery): Result<List<Culture>>
    suspend fun getCultureStatistics(): Result<CultureStatistics>

    // Culture lifecycle management
    suspend fun archiveCulture(cultureId: String): Result<Unit>
    suspend fun disposeCulture(cultureId: String, reason: String): Result<Unit>
    suspend fun restoreCulture(cultureId: String): Result<Unit>
}

data class CreateCultureRequest(
    val species: String,
    val explantType: String,
    val sourcePlantId: String?,
    val initiationDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String?,
    val initialConditions: String
)

data class CreateSubcultureRequest(
    val parentCultureId: String,
    val subcultureDate: LocalDate,
    val mediumComposition: String,
    val recipeId: String?,
    val explantCount: Int
)

data class AddObservationRequest(
    val cultureId: String,
    val observationDate: LocalDate,
    val contaminationStatus: Boolean,
    val survivalStatus: SurvivalStatus,
    val growthStage: GrowthStage,
    val notes: String?,
    val photoFilenames: List<String>
)

data class CultureLineage(
    val rootCulture: Culture,
    val subcultures: List<Subculture>,
    val totalGenerations: Int,
    val activeSubcultures: Int
)

data class CultureStatistics(
    val totalCultures: Int,
    val activeCultures: Int,
    val contaminatedCultures: Int,
    val successRate: Double,
    val averageGrowthTime: Duration
)
```

### Enhanced Offline Queue Service
```kotlin
interface OfflineQueueService {
    suspend fun queueOperation(operation: SyncOperation, priority: SyncPriority): Result<Unit>
    suspend fun getQueueSize(): Int
    suspend fun getQueueStatus(): QueueStatus
    suspend fun processQueueWhenOnline(): Result<SyncResult>
    suspend fun compactQueue(): Result<Unit>

    // Emergency capabilities
    suspend fun exportQueueForManualSync(): Result<String>
    suspend fun importQueueFromBackup(data: String): Result<Unit>

    // Queue management
    suspend fun clearQueue(): Result<Unit>
    suspend fun removeFailedOperations(): Result<Int>
    suspend fun retryFailedOperations(): Result<RetryResult>
}

data class QueueStatus(
    val pendingOperations: Int,
    val estimatedSyncTime: Duration,
    val lastSuccessfulSync: Instant?,
    val oldestPendingOperation: Instant?
)

data class RetryResult(
    val operationsRetried: Int,
    val operationsSucceeded: Int,
    val operationsFailed: Int
)
```

### Graceful Degradation Service
```kotlin
interface GracefulDegradationService {
    suspend fun detectGoogleServicesAvailability(): ServicesStatus
    suspend fun enableGuestMode(): Result<Unit>
    suspend fun enableOfflineOnlyMode(): Result<Unit>
    suspend fun exportLocalData(): Result<ExportResult>

    // Fallback authentication
    suspend fun createLocalAccount(username: String): Result<LocalAccount>
    suspend fun migrateToGoogleAccount(localAccount: LocalAccount): Result<MigrationResult>

    // Service recovery
    suspend fun attemptServiceRecovery(): Result<RecoveryResult>
    suspend fun getAlternativeFeatures(): List<AlternativeFeature>
}

enum class ServicesStatus {
    AVAILABLE,
    OFFLINE,
    NOT_AUTHENTICATED,
    UNAVAILABLE,
    RATE_LIMITED,
    QUOTA_EXCEEDED
}

data class ExportResult(
    val filePath: String,
    val format: ExportFormat, // JSON, CSV, PDF
    val includesPhotos: Boolean,
    val fileSize: Long
)

data class RecoveryResult(
    val servicesRecovered: List<String>,
    val servicesFailed: List<String>,
    val recommendedAction: String
)
```

### Photo Processing Service
```kotlin
interface PhotoProcessingService {
    suspend fun capturePhoto(cultureId: String): Result<Photo>
    suspend fun compressPhoto(photo: Photo, level: CompressionLevel): Result<OptimizedPhoto>
    suspend fun generateThumbnail(photo: Photo): Result<String>
    suspend fun batchCompressPhotos(photos: List<Photo>): Result<BatchCompressionResult>

    // Gallery management
    suspend fun getPhotoGallery(cultureId: String): Result<PhotoGallery>
    suspend fun deletePhoto(photoId: String): Result<Unit>
    suspend fun restorePhoto(photoId: String): Result<Unit>

    // Storage management
    suspend fun cleanupTempFiles(): Result<CleanupResult>
    suspend fun getStorageUsage(): Result<StorageUsage>
}

data class OptimizedPhoto(
    val originalPath: String,
    val optimizedPath: String,
    val compressionRatio: Double,
    val fileSizeBefore: Long,
    val fileSizeAfter: Long
)

data class PhotoGallery(
    val photos: List<Photo>,
    val thumbnails: Map<String, String>,
    val totalSize: Long,
    val photoCount: Int
)
```

## Error Handling (Enhanced with Constraints)

```kotlin
sealed class CultureStackError : Exception() {
    object NetworkUnavailable : CultureStackError()
    object GoogleServicesUnavailable : CultureStackError()
    object StorageQuotaExceeded : CultureStackError()
    object RateLimitExceeded : CultureStackError()
    object SyncConflictDetected : CultureStackError()
    object LocalStorageFull : CultureStackError()
    object InvalidDataFormat : CultureStackError()
    object PermissionDenied : CultureStackError()
    object DatabaseCorruption : CultureStackError()

    data class DatabaseError(val cause: Throwable) : CultureStackError()
    data class GoogleApiError(val code: Int, val message: String) : CultureStackError()
    data class QuotaError(val resetTime: Instant) : CultureStackError()
    data class ValidationError(val field: String, val message: String) : CultureStackError()
    data class FileSystemError(val operation: String, val path: String) : CultureStackError()
}

// Error handling extension functions
fun <T> Result<T>.handleCultureStackError(
    onError: (CultureStackError) -> Unit
): Result<T> {
    return this.onFailure { throwable ->
        when (throwable) {
            is CultureStackError -> onError(throwable)
            else -> onError(CultureStackError.DatabaseError(throwable))
        }
    }
}
```

## API Response Models

### Search and Query Results
```kotlin
data class SearchResult<T>(
    val results: List<T>,
    val totalCount: Int,
    val hasMore: Boolean,
    val searchTerm: String,
    val executionTimeMs: Long
)

data class SearchQuery(
    val term: String,
    val filters: Map<String, String>,
    val sortBy: String = "updatedAt",
    val sortOrder: SortOrder = SortOrder.DESC,
    val limit: Int = 50,
    val offset: Int = 0
)

enum class SortOrder { ASC, DESC }
```

### Pagination Support
```kotlin
data class PagedResult<T>(
    val items: List<T>,
    val currentPage: Int,
    val totalPages: Int,
    val totalItems: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean
)

data class PaginationRequest(
    val page: Int = 1,
    val size: Int = 20,
    val sort: List<String> = listOf("updatedAt:desc")
)
```

## API Performance Specifications

### Performance Targets
- **Culture Timeline Load:** <50ms for 1000 cultures
- **Recipe Search:** <100ms for 1000+ recipes with full-text search
- **Photo Gallery:** <200ms for 50 photos with metadata
- **Sync Conflict Detection:** <50ms for 100 entities
- **Batch Operation Progress:** <25ms per progress update

### Rate Limiting Configuration
```kotlin
data class RateLimitConfig(
    val requestsPerMinute: Int = 60,
    val burstLimit: Int = 20,
    val backoffStrategy: BackoffStrategy = BackoffStrategy.EXPONENTIAL,
    val maxRetries: Int = 3
)

enum class BackoffStrategy {
    FIXED, LINEAR, EXPONENTIAL
}
```

## Related Architecture Documents

- **[Data Models](./03-data-models.md)** - Entities used by these APIs
- **[Components & Services](./05-components.md)** - Components implementing these APIs
- **[External APIs](./07-external-apis.md)** - Google Services integration details
- **[Database Schema](./08-database-schema.md)** - Database implementation of data access APIs