# CultureStack Architecture - Components & Services

Based on the architectural patterns, tech stack, and constraint analysis, this document defines the major logical components across CultureStack's native Android architecture.

## Core Business Components

### Culture Management Component
**Responsibility:** Handles all culture and subculture lifecycle operations including creation, monitoring, status updates, and lineage tracking.

**Key Interfaces:**
- `CultureService.createCulture(CreateCultureRequest): Result<Culture>`
- `CultureService.createSubculture(CreateSubcultureRequest): Result<Subculture>`
- `CultureService.updateCultureStatus(cultureId: String, status: CultureStatus): Result<Unit>`
- `CultureService.getCultureLineage(cultureId: String): Result<CultureLineage>`

**Dependencies:** Database Component, Sync Component, Notification Component

**Technology Stack:**
- Kotlin with Coroutines for async operations
- Room database for local storage
- Hilt for dependency injection
- WorkManager for background status updates

**Implementation Details:**
```kotlin
@Singleton
class CultureManagementComponent @Inject constructor(
    private val cultureDao: CultureDao,
    private val syncComponent: SyncManagementComponent,
    private val notificationComponent: NotificationComponent,
    private val eventBus: ComponentEventBus
) : CultureService {

    override suspend fun createCulture(
        cultureRequest: CreateCultureRequest
    ): Result<Culture> {
        return try {
            val culture = Culture(
                cultureId = generateCultureId(),
                species = cultureRequest.species,
                explantType = cultureRequest.explantType,
                initiationDate = cultureRequest.initiationDate,
                // ... other fields
            )

            cultureDao.insertCulture(culture)
            syncComponent.queueForSync(culture)
            eventBus.publishAndForget(CultureCreatedEvent(culture))

            Result.success(culture)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }

    private fun generateCultureId(): String {
        // Generate human-readable culture ID (C001, C002, etc.)
        return "C${System.currentTimeMillis().toString().takeLast(6)}"
    }
}
```

### Recipe Management Component
**Responsibility:** Manages recipe library operations including creation, search, categorization, and usage tracking for medium formulations.

**Key Interfaces:**
- `RecipeService.createRecipe(CreateRecipeRequest): Result<Recipe>`
- `RecipeService.searchRecipes(searchCriteria: RecipeSearchCriteria): Result<List<Recipe>>`
- `RecipeService.getRecipesByPlantType(plantType: String): Result<List<Recipe>>`
- `RecipeService.incrementUsageCount(recipeId: String): Result<Unit>`

**Dependencies:** Database Component, Search Component

**Technology Stack:**
- Room database with FTS (Full-Text Search) support
- Kotlinx Serialization for structured ingredient data
- Repository pattern for data access abstraction

**Implementation Details:**
```kotlin
@Singleton
class RecipeManagementComponent @Inject constructor(
    private val recipeDao: RecipeDao,
    private val searchComponent: SearchComponent,
    private val cacheManager: CacheManager
) : RecipeService {

    override suspend fun searchRecipes(
        searchCriteria: RecipeSearchCriteria
    ): Result<List<Recipe>> {
        return try {
            val cacheKey = "recipes_${searchCriteria.hashCode()}"
            val cachedResult = cacheManager.get<List<Recipe>>(cacheKey)

            if (cachedResult != null) {
                return Result.success(cachedResult)
            }

            val recipes = when {
                searchCriteria.plantType != null -> {
                    recipeDao.getRecipesByPlantType(searchCriteria.plantType)
                }
                searchCriteria.difficultyLevel != null -> {
                    recipeDao.getRecipesByDifficulty(searchCriteria.difficultyLevel)
                }
                searchCriteria.searchTerm.isNotBlank() -> {
                    recipeDao.searchRecipes(searchCriteria.searchTerm)
                }
                else -> recipeDao.getAllRecipes().first()
            }

            cacheManager.put(cacheKey, recipes, Duration.ofMinutes(30))
            Result.success(recipes)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }
}
```

### Observation & Photo Component
**Responsibility:** Handles observation logging, photo capture, compression, storage, and gallery management for culture documentation.

**Key Interfaces:**
- `ObservationService.addObservation(AddObservationRequest): Result<Observation>`
- `PhotoService.captureAndProcessPhoto(cultureId: String): Result<Photo>`
- `PhotoService.compressPhoto(photo: Photo, level: CompressionLevel): Result<OptimizedPhoto>`
- `PhotoService.getPhotoGallery(cultureId: String): Result<List<Photo>>`

**Dependencies:** Database Component, File Storage Component, Sync Component

**Technology Stack:**
- Android CameraX for photo capture
- Coil for image loading and caching
- WebP format for optimal compression
- Android MediaStore for file management

**Implementation Details:**
```kotlin
@Singleton
class ObservationPhotoComponent @Inject constructor(
    private val observationDao: ObservationDao,
    private val photoDao: PhotoDao,
    private val photoProcessor: PhotoProcessor,
    private val fileManager: FileManager,
    private val syncComponent: SyncManagementComponent
) : ObservationService, PhotoService {

    override suspend fun captureAndProcessPhoto(cultureId: String): Result<Photo> {
        return try {
            // Capture photo using CameraX
            val rawPhotoPath = photoProcessor.capturePhoto()

            // Process and compress
            val optimizedPhoto = photoProcessor.compressPhoto(
                rawPhotoPath,
                CompressionLevel.HIGH
            )

            // Generate thumbnail
            val thumbnailPath = photoProcessor.generateThumbnail(optimizedPhoto.optimizedPath)

            // Create photo entity
            val photo = Photo(
                filename = File(optimizedPhoto.optimizedPath).name,
                observationId = cultureId,
                localPath = optimizedPhoto.optimizedPath,
                thumbnailPath = thumbnailPath,
                compressionLevel = CompressionLevel.HIGH,
                uploadStatus = UploadStatus.PENDING,
                fileSize = optimizedPhoto.fileSizeAfter,
                capturedAt = Instant.now(),
                deviceId = getDeviceId()
            )

            photoDao.insertPhoto(photo)
            syncComponent.queueForSync(photo, SyncPriority.NORMAL)

            Result.success(photo)
        } catch (e: Exception) {
            Result.failure(CultureStackError.FileSystemError("photo_capture", cultureId))
        }
    }
}
```

## Infrastructure Components

### Sync Management Component
**Responsibility:** Orchestrates data synchronization between local SQLite storage and Google Drive, handling conflicts, rate limits, and offline scenarios.

**Key Interfaces:**
- `SyncService.queueForSync(entityType: String, entityId: String, operation: SyncOperation): Result<Unit>`
- `SyncService.performIntelligentSync(): Result<SyncResult>`
- `SyncService.resolveConflict(conflictId: String, resolution: ConflictChoice): Result<Unit>`
- `SyncService.enableOfflineMode(): Result<Unit>`

**Dependencies:** Google Services Component, Database Component, Queue Management Component

**Technology Stack:**
- Google Drive API v3 for cloud storage
- WorkManager for background sync scheduling
- Retrofit + OkHttp for HTTP communication
- Rate limiting with Guava RateLimiter

**Implementation Details:**
```kotlin
@Singleton
class SyncManagementComponent @Inject constructor(
    private val driveService: ConstraintAwareDriveService,
    private val syncQueueDao: SyncQueueDao,
    private val conflictResolver: ConflictResolutionEngine,
    private val rateLimiter: GoogleDriveRateLimiter,
    private val workManager: WorkManager
) : SyncService {

    override suspend fun performIntelligentSync(): Result<SyncResult> {
        return try {
            // Check quota status first
            val quotaStatus = driveService.estimateRemainingQuota().getOrThrow()

            if (quotaStatus.requestsRemaining < 10) {
                // Defer sync until quota resets
                scheduleDelayedSync(quotaStatus.resetTime)
                return Result.failure(CultureStackError.QuotaError(quotaStatus.resetTime))
            }

            // Get pending operations
            val pendingOps = syncQueueDao.getPendingOperations()

            // Prioritize operations
            val prioritizedOps = prioritizeOperations(pendingOps)

            // Execute batch sync
            val result = driveService.batchSyncCultures(
                prioritizedOps.filter { it.entityType == "Culture" }.map { it.entityId }
            ).getOrThrow()

            // Update sync queue
            updateSyncQueueStatus(prioritizedOps, result)

            Result.success(SyncResult(
                totalOperations = prioritizedOps.size,
                successfulOperations = result.successCount,
                failedOperations = result.failedCount
            ))
        } catch (e: Exception) {
            Result.failure(CultureStackError.NetworkUnavailable)
        }
    }

    private fun prioritizeOperations(operations: List<SyncQueue>): List<SyncQueue> {
        return operations.sortedWith(
            compareByDescending<SyncQueue> { it.priority }
                .thenBy { it.createdAt }
        )
    }
}
```

### Authentication & Billing Component
**Responsibility:** Manages Google account authentication, premium subscription verification, and user session state for freemium model support.

**Key Interfaces:**
- `AuthService.signInWithGoogle(): Result<GoogleSignInAccount>`
- `BillingService.purchaseSubscription(subscriptionId: String): Result<PurchaseResult>`
- `BillingService.verifyPremiumStatus(): Result<Boolean>`
- `AuthService.enableGuestMode(): Result<GuestAccount>`

**Dependencies:** Google Services Component, Database Component

**Technology Stack:**
- Google Sign-In API for authentication
- Google Play Billing Library for subscriptions
- SharedPreferences for session persistence
- Firebase Analytics for user behavior tracking

**Implementation Details:**
```kotlin
@Singleton
class AuthBillingComponent @Inject constructor(
    private val googleSignInClient: GoogleSignInClient,
    private val billingClient: BillingClient,
    private val sessionManager: SessionManager,
    private val analyticsTracker: AnalyticsTracker
) : AuthService, BillingService {

    override suspend fun signInWithGoogle(): Result<GoogleSignInAccount> {
        return try {
            val signInIntent = googleSignInClient.signInIntent
            val account = GoogleSignIn.getLastSignedInAccount(context)

            if (account != null && !account.isExpired) {
                sessionManager.saveSession(account)
                analyticsTracker.trackUserLogin("google")
                Result.success(account)
            } else {
                // Handle sign-in flow through activity result
                Result.failure(CultureStackError.PermissionDenied)
            }
        } catch (e: Exception) {
            Result.failure(CultureStackError.GoogleServicesUnavailable)
        }
    }

    override fun isPremiumUser(): Boolean {
        return sessionManager.getCurrentSession()?.isPremium ?: false
    }
}
```

### Notification & Scheduling Component
**Responsibility:** Manages reminder scheduling, push notifications, calendar integration, and background task coordination for culture maintenance.

**Key Interfaces:**
- `NotificationService.scheduleReminder(reminderId: String, scheduledTime: Instant): Result<Unit>`
- `NotificationService.sendPushNotification(notification: CultureNotification): Result<Unit>`
- `SchedulingService.createRecurringReminder(reminder: RecurringReminder): Result<Unit>`
- `SchedulingService.getUpcomingTasks(timeRange: TimeRange): Result<List<ScheduledTask>>`

**Dependencies:** Database Component, Culture Management Component

**Technology Stack:**
- Firebase Cloud Messaging for push notifications
- Android AlarmManager for precise scheduling
- WorkManager for background task execution
- Android notification channels for categorization

**Implementation Details:**
```kotlin
@Singleton
class NotificationSchedulingComponent @Inject constructor(
    private val firebaseMessaging: FirebaseMessaging,
    private val alarmManager: AlarmManager,
    private val workManager: WorkManager,
    private val notificationManager: NotificationManager
) : NotificationService, SchedulingService {

    override suspend fun scheduleReminder(
        reminderId: String,
        scheduledTime: Instant
    ): Result<Unit> {
        return try {
            // Schedule local notification
            val intent = createReminderIntent(reminderId)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                reminderId.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                scheduledTime.toEpochMilli(),
                pendingIntent
            )

            // Also schedule WorkManager job as backup
            val workRequest = OneTimeWorkRequestBuilder<ReminderWorker>()
                .setInitialDelay(
                    Duration.between(Instant.now(), scheduledTime).toMillis(),
                    TimeUnit.MILLISECONDS
                )
                .setInputData(workDataOf("reminderId" to reminderId))
                .build()

            workManager.enqueue(workRequest)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }
}
```

## Foundation Components

### Database Component
**Responsibility:** Provides unified data access layer with offline-first architecture, including local storage, caching, indexing, and transaction management.

**Key Interfaces:**
- `DatabaseService.executeInTransaction(block: suspend () -> Unit): Result<Unit>`
- `DatabaseService.getEntityById(entityType: String, id: String): Result<Any?>`
- `DatabaseService.performMaintenance(): Result<MaintenanceResult>`
- `DatabaseService.exportDatabase(format: ExportFormat): Result<String>`

**Dependencies:** None (foundational layer)

**Technology Stack:**
- SQLite with Room ORM for local storage
- SQLCipher for database encryption
- Database migration strategies with Room
- Optimized indexes for query performance

**Implementation Details:**
```kotlin
@Database(
    entities = [Culture::class, Subculture::class, Recipe::class, Observation::class, Photo::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class CultureStackDatabase : RoomDatabase() {
    abstract fun cultureDao(): CultureDao
    abstract fun subcultureDao(): SubcultureDao
    abstract fun recipeDao(): RecipeDao
    abstract fun observationDao(): ObservationDao
    abstract fun photoDao(): PhotoDao
    abstract fun syncQueueDao(): SyncQueueDao
}

@Singleton
class DatabaseComponent @Inject constructor(
    private val database: CultureStackDatabase,
    private val encryptionManager: DatabaseEncryptionManager
) : DatabaseService {

    override suspend fun executeInTransaction(
        block: suspend () -> Unit
    ): Result<Unit> {
        return try {
            database.withTransaction {
                block()
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }

    override suspend fun performMaintenance(): Result<MaintenanceResult> {
        return try {
            val maintenanceResult = MaintenanceResult(
                vacuumPerformed = true,
                indexesOptimized = true,
                orphanedRecordsRemoved = 0,
                databaseSizeAfter = getDatabaseSize()
            )
            Result.success(maintenanceResult)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }
}
```

### Google Services Integration Component
**Responsibility:** Abstracts Google API interactions including Drive storage, authentication, billing, and service availability detection with graceful degradation.

**Key Interfaces:**
- `GoogleServicesManager.checkServiceAvailability(): ServicesStatus`
- `GoogleServicesManager.initializeServices(): Result<Unit>`
- `GoogleServicesManager.handleServiceUnavailable(): Result<DegradationStrategy>`
- `GoogleServicesManager.getQuotaStatus(): Result<QuotaStatus>`

**Dependencies:** None (external integration layer)

**Technology Stack:**
- Google Play Services SDK
- Google Drive API v3 client
- Google Sign-In API
- Play Services Availability checker

## Component Interaction Architecture

### Component Interaction Diagram
```mermaid
graph TB
    subgraph "UI Layer"
        A[Culture Screens]
        B[Recipe Screens]
        C[Calendar Screens]
    end

    subgraph "Business Logic Layer"
        D[Culture Management]
        E[Recipe Management]
        F[Observation & Photo]
        G[Notification & Scheduling]
    end

    subgraph "Data Layer"
        H[Sync Management]
        I[Database Component]
        J[Google Services]
        K[Event Bus]
    end

    A --> D
    B --> E
    C --> G
    D --> I
    E --> I
    F --> I
    G --> I

    H --> I
    H --> J

    D -.->|async events| K
    F -.->|async events| K
    E -.->|async events| K
    K -.->|notifications| G
    K -.->|sync requests| H
    K -.->|status updates| D

    J -.->|circuit breaker| H
```

### Event-Driven Communication System
```kotlin
interface ComponentEventBus {
    suspend fun publish(event: ComponentEvent)
    fun subscribe(eventType: EventType, handler: suspend (ComponentEvent) -> Unit)
    suspend fun publishAndForget(event: ComponentEvent) // Fire-and-forget for performance
}

sealed class ComponentEvent {
    data class CultureCreated(val culture: Culture) : ComponentEvent()
    data class ObservationAdded(val observation: Observation) : ComponentEvent()
    data class SyncCompleted(val entityType: String, val entityId: String) : ComponentEvent()
    data class ConflictDetected(val conflict: ConflictResolution) : ComponentEvent()
    data class QuotaWarning(val quotaStatus: QuotaStatus) : ComponentEvent()
    data class ServiceUnavailable(val service: String, val reason: String) : ComponentEvent()
}
```

### Dependency Management with Circuit Breaker
```kotlin
class GoogleServicesCircuitBreaker(
    private val failureThreshold: Int = 5,
    private val timeoutMs: Long = 30000
) {
    private var failures = 0
    private var lastFailureTime = 0L
    private var state = CircuitState.CLOSED

    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        return when (state) {
            CircuitState.OPEN -> {
                if (System.currentTimeMillis() - lastFailureTime > timeoutMs) {
                    state = CircuitState.HALF_OPEN
                    tryOperation(operation)
                } else {
                    Result.failure(GoogleServicesUnavailableException())
                }
            }
            CircuitState.HALF_OPEN, CircuitState.CLOSED -> tryOperation(operation)
        }
    }

    private suspend fun <T> tryOperation(operation: suspend () -> T): Result<T> {
        return try {
            val result = operation()
            onSuccess()
            Result.success(result)
        } catch (e: Exception) {
            onFailure()
            Result.failure(e)
        }
    }

    private fun onSuccess() {
        failures = 0
        state = CircuitState.CLOSED
    }

    private fun onFailure() {
        failures++
        lastFailureTime = System.currentTimeMillis()
        if (failures >= failureThreshold) {
            state = CircuitState.OPEN
        }
    }
}

enum class CircuitState { CLOSED, OPEN, HALF_OPEN }
```

### Resource Management Strategy
```kotlin
class ComponentResourceManager {
    private val photoProcessingPool = Executors.newFixedThreadPool(2)
    private val syncOperationPool = Executors.newFixedThreadPool(3)
    private val databasePool = Executors.newSingleThreadExecutor()

    suspend fun executePhotoOperation(operation: suspend () -> Unit) {
        withContext(photoProcessingPool.asCoroutineDispatcher()) {
            operation()
        }
    }

    fun prioritizeComponents(systemState: SystemState): ComponentPriority {
        return when {
            systemState.isLowMemory -> ComponentPriority.ESSENTIAL_ONLY
            systemState.isBatteryLow -> ComponentPriority.REDUCE_BACKGROUND
            systemState.isNetworkLimited -> ComponentPriority.OFFLINE_FOCUS
            else -> ComponentPriority.FULL_FUNCTIONALITY
        }
    }
}

enum class ComponentPriority {
    ESSENTIAL_ONLY, REDUCE_BACKGROUND, OFFLINE_FOCUS, FULL_FUNCTIONALITY
}
```

## Related Architecture Documents

- **[Data Models](./03-data-models.md)** - Entities manipulated by these components
- **[API Specifications](./04-api-specifications.md)** - APIs implemented by these components
- **[Workflows](./06-workflows.md)** - Business processes orchestrated by these components
- **[External APIs](./07-external-apis.md)** - External services integrated by these components