# CultureStack Architecture - Core Workflows

Based on the PRD requirements and architectural components, this document illustrates key system workflows using sequence diagrams that show the interactions between components, external APIs, and error handling paths.

## Primary Business Workflows

### Culture Creation Workflow

This workflow demonstrates the complete process of creating a new culture, from user input through local storage and cloud synchronization.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant RM as Recipe Management
    participant DB as Database Component
    participant EB as Event Bus
    participant SM as Sync Management
    participant GD as Google Drive API

    User->>UI: Create new culture
    UI->>RM: Search for recipes
    RM->>DB: Query recipes by plant type
    DB-->>RM: Return matching recipes
    RM-->>UI: Recipe suggestions
    User->>UI: Select recipe & enter details
    UI->>CM: CreateCulture request

    CM->>DB: Insert culture record
    DB-->>CM: Culture created (local)
    CM->>EB: Publish CultureCreated event
    CM-->>UI: Success response
    UI-->>User: Culture created confirmation

    EB->>SM: Handle CultureCreated event
    SM->>GD: Upload culture metadata
    alt Sync Success
        GD-->>SM: Upload successful
        SM->>DB: Update sync status
    else Rate Limited
        GD-->>SM: Rate limit error
        SM->>SM: Queue for delayed sync
    else Network Error
        SM->>SM: Add to offline queue
    end
```

**Workflow Characteristics:**
- **Offline-First:** Culture is created locally first, sync happens asynchronously
- **Event-Driven:** Uses event bus to decouple operations
- **Error Resilient:** Multiple fallback paths for different failure scenarios
- **User Experience:** Immediate feedback, background sync doesn't block UI

### Multi-Device Observation Sync Workflow

This sequence shows the complex interaction when observations are logged on one device and synchronized to another, including conflict resolution.

```mermaid
sequenceDiagram
    participant U1 as User Device A
    participant U2 as User Device B
    participant CM1 as Culture Mgmt A
    participant CM2 as Culture Mgmt B
    participant DB1 as Database A
    participant DB2 as Database B
    participant SM1 as Sync Mgmt A
    participant SM2 as Sync Mgmt B
    participant GD as Google Drive
    participant CR as Conflict Resolution

    U1->>CM1: Add contamination observation
    CM1->>DB1: Insert observation locally
    CM1->>CM1: Update culture status to CONTAMINATED
    CM1->>DB1: Update culture status
    CM1->>SM1: Queue sync operations

    SM1->>GD: Upload observation + culture update
    GD-->>SM1: Sync successful

    Note over U2: Meanwhile on Device B...
    U2->>CM2: View culture timeline
    CM2->>SM2: Check for remote updates
    SM2->>GD: Fetch latest changes
    GD-->>SM2: New observation + status update

    SM2->>DB2: Compare sync versions
    alt No Conflict
        SM2->>DB2: Apply updates
        SM2->>CM2: Notify of changes
        CM2-->>U2: Show updated status
    else Sync Conflict Detected
        SM2->>CR: Create conflict resolution
        CR->>CR: Analyze conflicts
        CR-->>SM2: Manual resolution required
        SM2->>CM2: Present conflict UI
        CM2-->>U2: User chooses resolution
        U2->>CM2: Select resolution strategy
        CM2->>CR: Apply resolution
        CR->>DB2: Update with merged data
        CR->>SM2: Resolution complete
        SM2->>GD: Upload resolved version
    end
```

**Workflow Characteristics:**
- **Multi-Device Support:** Handles synchronization across multiple user devices
- **Conflict Resolution:** Intelligent conflict detection and user-guided resolution
- **Data Integrity:** Maintains data consistency across all devices
- **Real-Time Updates:** Provides immediate feedback on data changes

### Bulk Subculture Creation Workflow

This demonstrates the batch operation pattern for creating multiple subcultures, including progress tracking and error recovery.

```mermaid
sequenceDiagram
    participant User
    participant UI as Culture UI
    participant CM as Culture Management
    participant BO as Batch Operation Service
    participant DB as Database Component
    participant SM as Sync Management
    participant NS as Notification Service

    User->>UI: Create 5 subcultures from Culture C001
    UI->>CM: BatchSubculture request
    CM->>BO: Create batch operation
    BO->>DB: Insert BatchOperation record
    BO-->>CM: Batch operation started
    CM-->>UI: Show progress indicator

    loop For each subculture (5 times)
        BO->>CM: Create individual subculture
        CM->>DB: Insert subculture record
        CM->>SM: Queue for sync
        BO->>BO: Increment completed count
        BO->>DB: Update batch progress
        BO->>UI: Progress update

        alt Subculture Creation Fails
            CM-->>BO: Creation error
            BO->>BO: Log error, continue with next
        end
    end

    BO->>DB: Mark batch as completed
    BO->>NS: Schedule reminders for new subcultures
    BO-->>UI: Batch operation complete
    UI-->>User: 5 subcultures created successfully

    Note over SM: Background sync of all new subcultures
    SM->>SM: Process sync queue
    loop For each queued subculture
        SM->>GD: Upload subculture data
        alt Sync Success
            GD-->>SM: Upload successful
        else Sync Failure
            SM->>SM: Retry with exponential backoff
        end
    end
```

**Workflow Characteristics:**
- **Batch Processing:** Efficient handling of bulk operations
- **Progress Tracking:** Real-time progress feedback to users
- **Error Recovery:** Continues processing even if individual operations fail
- **Background Sync:** Manages large sync operations without blocking UI

## Photo Processing Workflows

### Photo Capture and Processing Workflow

```mermaid
sequenceDiagram
    participant User
    participant UI as Camera UI
    participant PC as Photo Component
    participant PP as Photo Processor
    participant FM as File Manager
    participant DB as Database
    participant SM as Sync Management

    User->>UI: Capture culture photo
    UI->>PC: CapturePhoto request
    PC->>PP: Initialize camera
    PP-->>PC: Camera ready

    PC->>PP: Capture raw photo
    PP-->>PC: Raw photo (8MB JPEG)

    PC->>PP: Compress to WebP
    PP-->>PC: Optimized photo (1.2MB)

    PC->>PP: Generate thumbnail
    PP-->>PC: Thumbnail (50KB)

    PC->>FM: Save processed files
    FM-->>PC: Files saved

    PC->>DB: Insert photo record
    DB-->>PC: Photo record created

    PC->>SM: Queue for cloud sync
    PC-->>UI: Photo capture complete
    UI-->>User: Show captured photo

    Note over SM: Background upload
    SM->>SM: Check quota and connectivity
    alt Upload Available
        SM->>GD: Upload optimized photo
        GD-->>SM: Upload successful
        SM->>DB: Update sync status
    else Quota Exceeded
        SM->>SM: Schedule delayed upload
    end
```

**Photo Processing Features:**
- **Automatic Compression:** Reduces file size while maintaining quality
- **Thumbnail Generation:** Improves gallery performance
- **Smart Upload:** Respects Google Drive quotas and connectivity
- **Local Storage:** Maintains offline access to photos

## Sync and Conflict Resolution Workflows

### Intelligent Sync Workflow

```mermaid
sequenceDiagram
    participant SM as Sync Manager
    participant QL as Queue Logic
    participant RL as Rate Limiter
    participant GD as Google Drive
    participant DB as Database
    participant NS as Notification Service

    SM->>QL: Check pending operations
    QL-->>SM: Return prioritized queue

    SM->>RL: Check rate limits
    RL-->>SM: Rate limit status

    alt Rate Limit OK
        SM->>GD: Estimate quota usage
        GD-->>SM: Quota available

        loop For each priority operation
            SM->>RL: Acquire rate limit token
            RL-->>SM: Token acquired
            SM->>GD: Execute sync operation
            alt Sync Success
                GD-->>SM: Operation successful
                SM->>DB: Update sync status
                SM->>QL: Remove from queue
            else Sync Failure
                GD-->>SM: Operation failed
                SM->>QL: Update retry count
                SM->>QL: Reschedule if retries available
            end
        end

    else Rate Limited
        SM->>QL: Schedule delayed sync
        SM->>NS: Notify user of delay
    end

    SM->>DB: Update last sync timestamp
    SM->>NS: Send sync completion notification
```

**Sync Intelligence Features:**
- **Priority-Based Processing:** High-priority items sync first
- **Rate Limit Awareness:** Respects Google Drive API limits
- **Quota Management:** Prevents exceeding user storage limits
- **Retry Logic:** Handles temporary failures gracefully

### Conflict Resolution Workflow

```mermaid
sequenceDiagram
    participant SM as Sync Manager
    participant CD as Conflict Detector
    participant CR as Conflict Resolver
    participant UI as Conflict UI
    participant User
    participant DB as Database

    SM->>CD: Compare local vs cloud data
    CD->>CD: Analyze sync versions
    CD->>CD: Check modification timestamps

    alt No Conflict
        CD-->>SM: Data consistent
        SM->>DB: Apply cloud updates
    else Conflict Detected
        CD->>CR: Create conflict record
        CR->>CR: Analyze conflict type

        alt Auto-Resolvable
            CR->>CR: Apply last-write-wins
            CR->>DB: Update with winning version
            CR-->>SM: Conflict resolved
        else Manual Resolution Required
            CR->>UI: Present conflict options
            UI->>User: Show data differences
            User->>UI: Select resolution strategy
            UI->>CR: Apply user choice

            alt Accept Local
                CR->>DB: Keep local version
                CR->>GD: Upload local to cloud
            else Accept Cloud
                CR->>DB: Replace with cloud version
            else Merge Data
                CR->>CR: Merge non-conflicting fields
                CR->>UI: Show merge preview
                User->>UI: Confirm merge
                CR->>DB: Apply merged version
                CR->>GD: Upload merged result
            end

            CR-->>SM: Conflict resolved
        end
    end

    SM->>DB: Mark sync complete
```

**Conflict Resolution Features:**
- **Intelligent Detection:** Identifies conflicts using version numbers and timestamps
- **User-Friendly UI:** Clear presentation of conflicting data
- **Multiple Resolution Strategies:** Last-write-wins, user choice, or intelligent merge
- **Data Preservation:** No data loss during conflict resolution

## Data Flow Analysis and Optimization

### Critical Data Transformations in Workflows

#### Culture Creation Data Pipeline
```kotlin
// User Input → Domain Entity → Database Entity → Sync Payload
data class CultureDataFlow(
    val userInput: CultureCreationInput,           // Raw form data
    val domainEntity: CultureDomainEntity,         // Business logic applied
    val dbEntity: CultureDbEntity,                 // Persistence format
    val syncPayload: CultureSyncPayload            // Cloud format
)

// Data validation gates at each transformation
class CultureDataValidator {
    suspend fun validateTransformation(
        input: CultureCreationInput,
        output: CultureDomainEntity
    ): ValidationResult {
        val violations = mutableListOf<ValidationViolation>()

        if (input.species.isBlank()) violations.add(ValidationViolation.REQUIRED_FIELD_MISSING)
        if (input.selectedRecipeId != null && !recipeExists(input.selectedRecipeId)) {
            violations.add(ValidationViolation.INVALID_REFERENCE)
        }

        return ValidationResult(violations)
    }
}
```

#### Photo Processing Pipeline with Data Integrity
```kotlin
data class PhotoProcessingPipeline(
    val rawCapture: RawPhotoCapture,      // 8MB JPEG from camera
    val optimizedPhoto: OptimizedPhoto,    // 1.2MB WebP compressed
    val dbEntity: PhotoDbEntity,           // Database reference
    val syncPayload: DriveUploadPayload    // Cloud upload format
)

class PhotoDataIntegrityValidator {
    fun validatePhotoProcessing(pipeline: PhotoProcessingPipeline): IntegrityResult {
        val issues = mutableListOf<IntegrityIssue>()

        // Check file size reduction is reasonable (not corrupted)
        val compressionRatio = pipeline.optimizedPhoto.optimizedSize.toDouble() /
                               pipeline.rawCapture.fileSize
        if (compressionRatio > 0.8 || compressionRatio < 0.05) {
            issues.add(IntegrityIssue.SUSPICIOUS_COMPRESSION_RATIO)
        }

        // Verify file references are consistent
        if (!fileExists(pipeline.optimizedPhoto.optimizedPath)) {
            issues.add(IntegrityIssue.MISSING_OPTIMIZED_FILE)
        }

        return IntegrityResult(issues)
    }
}
```

## Error Handling and Recovery Workflows

### Graceful Degradation Workflow

```mermaid
flowchart TD
    A[User Action] --> B{Google Services Available?}
    B -->|Yes| C[Normal Operation]
    B -->|No| D{Critical Feature?}

    D -->|Yes| E[Show Alternative Options]
    D -->|No| F[Offline Mode]

    E --> G{User Chooses Alternative?}
    G -->|Yes| H[Execute Alternative]
    G -->|No| I[Explain Limitation]

    F --> J[Local Storage Only]
    J --> K[Queue for Later Sync]

    C --> L[Cloud Sync]
    H --> M[Limited Functionality]

    L --> N{Sync Successful?}
    N -->|Yes| O[Update UI]
    N -->|No| P[Add to Retry Queue]

    P --> Q[Background Retry]
    Q --> R{Retry Successful?}
    R -->|Yes| O
    R -->|No| S[Notify User of Issue]
```

### Data Flow Monitoring and Metrics

```kotlin
data class DataFlowMetrics(
    val transformationLatency: Duration,    // Time to process data through pipeline
    val dataLossRate: Double,              // Percentage of data lost in transformation
    val errorRate: Double,                 // Errors per transformation
    val throughput: Double,                // Records per second
    val bottleneckStage: PipelineStage     // Identified bottleneck location
)

class WorkflowDataFlowMonitor {
    fun monitorCultureCreationFlow(): Flow<DataFlowMetrics> {
        return combine(
            measureTransformationLatency(),
            measureDataLossRate(),
            measureErrorRate(),
            measureThroughput()
        ) { latency, lossRate, errorRate, throughput ->
            DataFlowMetrics(
                transformationLatency = latency,
                dataLossRate = lossRate,
                errorRate = errorRate,
                throughput = throughput,
                bottleneckStage = identifyBottleneck(latency, throughput)
            )
        }
    }
}
```

## Performance Optimization Workflows

### Background Sync Optimization

```mermaid
sequenceDiagram
    participant WM as WorkManager
    participant SM as Sync Manager
    participant PM as Performance Monitor
    participant RL as Rate Limiter
    participant GD as Google Drive

    WM->>SM: Scheduled sync triggered
    SM->>PM: Check system resources
    PM-->>SM: Resource availability

    alt Low Resources
        SM->>SM: Defer non-critical sync
    else Normal Resources
        SM->>RL: Check rate limits
        RL-->>SM: Current quota status

        alt Quota Available
            SM->>GD: Begin sync operations
            loop Sync Queue Processing
                SM->>PM: Monitor performance
                alt Performance Degraded
                    SM->>SM: Reduce batch size
                else Performance Good
                    SM->>SM: Maintain batch size
                end
            end
        else Quota Limited
            SM->>WM: Schedule delayed retry
        end
    end

    SM->>PM: Record performance metrics
    SM->>WM: Schedule next sync cycle
```

## Workflow Design Principles

**Core Workflow Design Rationale:**
- **Event-Driven Architecture:** Workflows use event bus for loose coupling and better error isolation
- **Offline-First Design:** All workflows can operate without network connectivity, queuing sync operations
- **Graceful Degradation:** Each workflow has fallback paths for service unavailability
- **Progress Visibility:** Batch operations provide real-time progress feedback to users
- **Error Recovery:** Comprehensive error handling with retry logic and user notification
- **Data Integrity:** Validation gates at each transformation point prevent data corruption
- **Performance Monitoring:** Real-time metrics for bottleneck identification and optimization

**Performance Targets:**
- **Culture Creation:** <500ms end-to-end completion
- **Photo Capture and Processing:** <2 seconds for compression and storage
- **Batch Operations:** Progress updates every <100ms
- **Sync Operations:** <30 seconds for full dataset sync
- **Conflict Resolution:** UI response within <200ms

## Related Architecture Documents

- **[Components & Services](./05-components.md)** - Components that execute these workflows
- **[API Specifications](./04-api-specifications.md)** - APIs used within these workflows
- **[External APIs](./07-external-apis.md)** - External services integrated in workflows
- **[Data Models](./03-data-models.md)** - Entities flowing through these workflows