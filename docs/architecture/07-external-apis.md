# CultureStack Architecture - External APIs & Integrations

Based on CultureStack's native Android architecture and PRD requirements, the application integrates with several external APIs and services. Since this is a native mobile app rather than a traditional web application, "external APIs" refer to third-party service integrations.

## Google Services Ecosystem

### Google Drive API v3
**Purpose:** Primary cloud storage and synchronization service for user culture data, photos, and recipes.

- **Documentation:** https://developers.google.com/drive/api/v3/reference
- **Base URL(s):** https://www.googleapis.com/drive/v3/, https://www.googleapis.com/upload/drive/v3/
- **Authentication:** OAuth 2.0 with Google Sign-In API, Drive scope permissions
- **Rate Limits:** 1,000 requests per 100 seconds per user, 100 queries per second per user

**Key Endpoints Used:**
- `GET /files` - List user's culture data files in app folder
- `POST /files` - Create new culture data file or folder structure
- `PATCH /files/{fileId}` - Update existing culture records
- `POST /upload/files` - Upload culture photos with multipart encoding
- `GET /files/{fileId}` - Download culture data or photos for sync

**Integration Implementation:**
```kotlin
@Singleton
class GoogleDriveIntegration @Inject constructor(
    private val driveService: Drive,
    private val rateLimiter: RateLimiter,
    private val retryHandler: RetryHandler
) {

    suspend fun uploadCultureData(culture: Culture): Result<DriveFile> {
        return rateLimiter.execute {
            try {
                val fileMetadata = File().apply {
                    name = "culture_${culture.id}.json"
                    parents = listOf(getAppFolderId())
                }

                val mediaContent = ByteArrayContent(
                    "application/json",
                    Json.encodeToString(culture).toByteArray()
                )

                val uploadedFile = driveService.files()
                    .create(fileMetadata, mediaContent)
                    .execute()

                Result.success(uploadedFile)
            } catch (e: GoogleJsonResponseException) {
                handleDriveApiError(e)
            }
        }
    }

    private fun handleDriveApiError(e: GoogleJsonResponseException): Result<Nothing> {
        return when (e.statusCode) {
            403 -> Result.failure(CultureStackError.QuotaError(getQuotaResetTime()))
            429 -> Result.failure(CultureStackError.RateLimitExceeded)
            else -> Result.failure(CultureStackError.GoogleApiError(e.statusCode, e.message))
        }
    }
}
```

**Integration Notes:**
- Uses app-specific folder scope for user privacy
- Implements exponential backoff for rate limiting
- Batch operations for efficient sync of multiple entities
- Conflict resolution based on file modification timestamps

### Google Sign-In API
**Purpose:** User authentication for premium features and Google Drive access verification.

- **Documentation:** https://developers.google.com/identity/sign-in/android
- **Base URL(s):** Integrated Android SDK, no direct HTTP calls
- **Authentication:** OAuth 2.0 flows with Google Play Services integration
- **Rate Limits:** No explicit rate limits, governed by Google Play Services

**Key SDK Methods Used:**
- `GoogleSignIn.getClient().signIn()` - Initiate sign-in flow
- `GoogleSignIn.getLastSignedInAccount()` - Retrieve cached account
- `GoogleSignIn.getClient().signOut()` - Sign out current user
- `GoogleSignIn.requestPermissions()` - Request additional scopes

**Integration Implementation:**
```kotlin
@Singleton
class GoogleAuthIntegration @Inject constructor(
    private val context: Context,
    private val sessionManager: SessionManager
) {

    private val googleSignInClient by lazy {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .requestScopes(Scope(DriveScopes.DRIVE_FILE))
            .build()
        GoogleSignIn.getClient(context, gso)
    }

    suspend fun signInWithGoogle(): Result<GoogleSignInAccount> {
        return try {
            val account = GoogleSignIn.getLastSignedInAccount(context)

            if (account != null && !account.isExpired) {
                sessionManager.saveSession(account)
                Result.success(account)
            } else {
                // Trigger sign-in flow through activity
                Result.failure(CultureStackError.PermissionDenied)
            }
        } catch (e: Exception) {
            Result.failure(CultureStackError.GoogleServicesUnavailable)
        }
    }

    suspend fun requestDrivePermissions(): Result<Unit> {
        return try {
            GoogleSignIn.requestPermissions(
                activity,
                RC_DRIVE_PERMISSIONS,
                GoogleSignIn.getLastSignedInAccount(context),
                Scope(DriveScopes.DRIVE_FILE)
            )
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(CultureStackError.PermissionDenied)
        }
    }
}
```

**Integration Notes:**
- Integrated with Google Play Services for seamless UX
- Handles scope incremental authorization for Drive access
- Automatic token refresh managed by SDK
- Graceful fallback to guest mode when unavailable

### Google Play Billing Library
**Purpose:** Premium subscription management and in-app purchase verification for freemium model.

- **Documentation:** https://developer.android.com/google/play/billing
- **Base URL(s):** Integrated Android library, communicates with Google Play servers
- **Authentication:** App signing key verification with Google Play Console
- **Rate Limits:** No explicit limits, managed by Google Play infrastructure

**Key Library Methods Used:**
- `BillingClient.queryPurchasesAsync()` - Verify active subscriptions
- `BillingClient.launchBillingFlow()` - Initiate subscription purchase
- `BillingClient.acknowledgePurchase()` - Acknowledge completed purchase
- `BillingClient.queryProductDetailsAsync()` - Get subscription pricing

**Integration Implementation:**
```kotlin
@Singleton
class GooglePlayBillingIntegration @Inject constructor(
    private val context: Context,
    private val purchaseValidator: PurchaseValidator
) : PurchasesUpdatedListener {

    private lateinit var billingClient: BillingClient

    fun initializeBilling(): Result<Unit> {
        billingClient = BillingClient.newBuilder(context)
            .setListener(this)
            .enablePendingPurchases()
            .build()

        return try {
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(result: BillingResult) {
                    if (result.responseCode == BillingClient.BillingResponseCode.OK) {
                        // Billing client is ready
                        queryExistingPurchases()
                    }
                }

                override fun onBillingServiceDisconnected() {
                    // Handle disconnection
                }
            })
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(CultureStackError.GoogleServicesUnavailable)
        }
    }

    override fun onPurchasesUpdated(result: BillingResult, purchases: List<Purchase>?) {
        when (result.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                purchases?.forEach { purchase ->
                    handlePurchase(purchase)
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                // User canceled purchase
            }
            else -> {
                // Handle other response codes
            }
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            // Verify purchase server-side
            purchaseValidator.validatePurchase(purchase) { isValid ->
                if (isValid) {
                    acknowledgePurchase(purchase)
                    updateUserPremiumStatus(true)
                }
            }
        }
    }
}
```

**Integration Notes:**
- Server-side receipt verification for security
- Handles subscription restoration across devices
- Manages purchase state persistence for offline scenarios
- Real-time purchase updates via PurchasesUpdatedListener

### Firebase Cloud Messaging (FCM)
**Purpose:** Push notification delivery for culture reminders and sync notifications.

- **Documentation:** https://firebase.google.com/docs/cloud-messaging
- **Base URL(s):** https://fcm.googleapis.com/v1/projects/{project-id}/messages:send
- **Authentication:** Service account key for server-to-FCM communication
- **Rate Limits:** No published limits for downstream messages, reasonable use expected

**Key Endpoints Used:**
- `POST /v1/projects/{project-id}/messages:send` - Send targeted notification
- SDK method: `FirebaseMessaging.getToken()` - Retrieve device FCM token
- SDK method: `FirebaseMessaging.subscribeToTopic()` - Topic-based messaging
- SDK method: `FirebaseMessaging.setAutoInitEnabled()` - Enable/disable FCM

**Integration Implementation:**
```kotlin
@Singleton
class FirebaseMessagingIntegration @Inject constructor(
    private val firebaseMessaging: FirebaseMessaging,
    private val notificationManager: NotificationManager
) : FirebaseMessagingService() {

    suspend fun initializeMessaging(): Result<String> {
        return try {
            val token = firebaseMessaging.token.await()

            // Subscribe to relevant topics
            firebaseMessaging.subscribeToTopic("culture_reminders")
            firebaseMessaging.subscribeToTopic("app_updates")

            Result.success(token)
        } catch (e: Exception) {
            Result.failure(CultureStackError.GoogleServicesUnavailable)
        }
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        val notificationType = remoteMessage.data["type"]

        when (notificationType) {
            "culture_reminder" -> {
                val cultureId = remoteMessage.data["culture_id"]
                createCultureReminderNotification(cultureId, remoteMessage.notification)
            }
            "sync_complete" -> {
                createSyncNotification(remoteMessage.notification)
            }
            else -> {
                createGenericNotification(remoteMessage.notification)
            }
        }
    }

    private fun createCultureReminderNotification(
        cultureId: String?,
        notification: RemoteMessage.Notification?
    ) {
        val intent = Intent(this, CultureDetailActivity::class.java).apply {
            putExtra("culture_id", cultureId)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_IMMUTABLE
        )

        val notificationBuilder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_culture)
            .setContentTitle(notification?.title ?: "Culture Reminder")
            .setContentText(notification?.body ?: "Check your culture")
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)

        notificationManager.notify(cultureId.hashCode(), notificationBuilder.build())
    }
}
```

**Integration Notes:**
- Topic-based messaging for broadcast notifications (maintenance, updates)
- Device-specific tokens for personalized culture reminders
- Notification channels for Android 8.0+ compatibility
- Handles token refresh and registration updates

## Regional Constraints and Fallback Strategies

### Regional Availability Analysis
```kotlin
data class RegionalConstraint(
    val region: String,
    val googlePlayServices: ServiceStatus,
    val googleDriveAccess: ServiceStatus,
    val alternativeRequired: Boolean,
    val marketPenetration: Double
)

val regionalConstraints = listOf(
    RegionalConstraint("China", BLOCKED, BLOCKED, true, 0.20),
    RegionalConstraint("Iran", RESTRICTED, RESTRICTED, true, 0.02),
    RegionalConstraint("Russia", PARTIAL, PARTIAL, false, 0.05),
    RegionalConstraint("EU", AVAILABLE, GDPR_COMPLIANT, false, 0.15),
    RegionalConstraint("North America", AVAILABLE, AVAILABLE, false, 0.35),
    RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
)
// Impact: ~22% of potential users may have limited Google Services access
```

### Rate Limiting and Quota Management
```kotlin
class GoogleDriveRateLimiter {
    private val rateLimiter = RateLimiter.create(8.0) // 8 requests per 10 seconds
    private val burstAllowance = Semaphore(20) // Allow 20 burst requests

    suspend fun executeWithLimit(operation: suspend () -> Result<Any>): Result<Any> {
        return if (burstAllowance.tryAcquire()) {
            try {
                rateLimiter.acquire()
                operation()
            } finally {
                burstAllowance.release()
            }
        } else {
            operationQueue.add(DelayedOperation(operation, System.currentTimeMillis() + 60000))
            Result.failure(RateLimitExceeded(Duration.ofMinutes(1)))
        }
    }
}
```

### Constraint-Aware API Client
```kotlin
class ConstraintAwareApiClient {
    private val regionalLimiter = RegionalRateLimiter()
    private val quotaMonitor = ApiQuotaMonitor()

    suspend fun <T> executeApiCall(
        apiCall: suspend () -> T,
        region: String,
        priority: ApiCallPriority
    ): Result<T> {
        // Check regional constraints
        val regionalConstraint = regionalLimiter.getConstraints(region)
        if (regionalConstraint.blocked) {
            return Result.failure(RegionBlockedError(region))
        }

        // Check quota status
        val quotaStatus = quotaMonitor.getCurrentStatus()
        if (quotaStatus.approachingLimit && priority == LOW) {
            return Result.failure(QuotaThresholdError("Deferring low-priority call"))
        }

        // Execute with appropriate rate limiting
        return regionalLimiter.executeWithConstraints(regionalConstraint) {
            apiCall()
        }
    }
}
```

## Graceful Degradation Strategies

### Service Availability Detection
```kotlin
@Singleton
class ServiceAvailabilityMonitor @Inject constructor(
    private val context: Context,
    private val connectivityManager: ConnectivityManager
) {

    fun checkGooglePlayServicesAvailability(): ServicesStatus {
        val availability = GoogleApiAvailability.getInstance()
        val result = availability.isGooglePlayServicesAvailable(context)

        return when (result) {
            ConnectionResult.SUCCESS -> ServicesStatus.AVAILABLE
            ConnectionResult.SERVICE_DISABLED,
            ConnectionResult.SERVICE_INVALID -> ServicesStatus.UNAVAILABLE
            ConnectionResult.NETWORK_ERROR -> ServicesStatus.OFFLINE
            else -> ServicesStatus.UNAVAILABLE
        }
    }

    fun checkInternetConnectivity(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
               capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
    }

    fun getRegionalConstraints(): RegionalConstraint {
        val locale = Locale.getDefault()
        val country = locale.country

        return regionalConstraints.find {
            it.region.equals(country, ignoreCase = true)
        } ?: RegionalConstraint("Other", AVAILABLE, AVAILABLE, false, 0.23)
    }
}
```

### Fallback Service Implementation
```kotlin
@Singleton
class FallbackServiceManager @Inject constructor(
    private val serviceMonitor: ServiceAvailabilityMonitor,
    private val localStorageService: LocalStorageService,
    private val exportService: DataExportService
) {

    suspend fun enableOfflineMode(): Result<Unit> {
        return try {
            // Disable all cloud-dependent features
            disableCloudSync()
            disableRemoteNotifications()

            // Enable local-only alternatives
            enableLocalNotifications()
            enableDataExport()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }

    suspend fun enableGuestMode(): Result<GuestAccount> {
        return try {
            val guestAccount = GuestAccount(
                id = UUID.randomUUID().toString(),
                displayName = "Guest User",
                createdAt = Instant.now()
            )

            localStorageService.saveGuestAccount(guestAccount)

            Result.success(guestAccount)
        } catch (e: Exception) {
            Result.failure(CultureStackError.DatabaseError(e))
        }
    }

    suspend fun exportDataForManualBackup(): Result<ExportResult> {
        return exportService.exportAllData(
            format = ExportFormat.JSON,
            includePhotos = true,
            compressionLevel = CompressionLevel.HIGH
        )
    }
}
```

## Error Handling and Circuit Breaker Pattern

### Circuit Breaker Implementation
```kotlin
class GoogleServicesCircuitBreaker(
    private val failureThreshold: Int = 5,
    private val timeoutMs: Long = 30000,
    private val halfOpenMaxCalls: Int = 3
) {
    private var failures = 0
    private var lastFailureTime = 0L
    private var state = CircuitState.CLOSED
    private var halfOpenCalls = 0

    suspend fun <T> execute(operation: suspend () -> T): Result<T> {
        return when (state) {
            CircuitState.OPEN -> {
                if (System.currentTimeMillis() - lastFailureTime > timeoutMs) {
                    state = CircuitState.HALF_OPEN
                    halfOpenCalls = 0
                    tryOperation(operation)
                } else {
                    Result.failure(GoogleServicesUnavailableException())
                }
            }
            CircuitState.HALF_OPEN -> {
                if (halfOpenCalls < halfOpenMaxCalls) {
                    halfOpenCalls++
                    tryOperation(operation)
                } else {
                    Result.failure(GoogleServicesUnavailableException())
                }
            }
            CircuitState.CLOSED -> tryOperation(operation)
        }
    }

    private suspend fun <T> tryOperation(operation: suspend () -> T): Result<T> {
        return try {
            val result = operation()
            onSuccess()
            Result.success(result)
        } catch (e: Exception) {
            onFailure()
            Result.failure(e)
        }
    }

    private fun onSuccess() {
        failures = 0
        state = CircuitState.CLOSED
    }

    private fun onFailure() {
        failures++
        lastFailureTime = System.currentTimeMillis()

        if (failures >= failureThreshold) {
            state = CircuitState.OPEN
        }
    }
}

enum class CircuitState { CLOSED, OPEN, HALF_OPEN }
```

## API Performance Monitoring

### Performance Metrics Collection
```kotlin
@Singleton
class ExternalApiMonitor @Inject constructor() {

    private val apiMetrics = mutableMapOf<String, ApiMetrics>()

    suspend fun <T> trackApiCall(
        apiName: String,
        operation: suspend () -> T
    ): Result<T> {
        val startTime = System.currentTimeMillis()

        return try {
            val result = operation()
            val endTime = System.currentTimeMillis()

            recordSuccess(apiName, endTime - startTime)
            Result.success(result)
        } catch (e: Exception) {
            val endTime = System.currentTimeMillis()

            recordFailure(apiName, endTime - startTime, e)
            Result.failure(e)
        }
    }

    private fun recordSuccess(apiName: String, latencyMs: Long) {
        val metrics = apiMetrics.getOrPut(apiName) { ApiMetrics() }
        metrics.recordSuccess(latencyMs)
    }

    private fun recordFailure(apiName: String, latencyMs: Long, error: Exception) {
        val metrics = apiMetrics.getOrPut(apiName) { ApiMetrics() }
        metrics.recordFailure(latencyMs, error)
    }

    fun getMetricsReport(): Map<String, ApiMetrics> = apiMetrics.toMap()
}

data class ApiMetrics(
    private var totalCalls: Long = 0,
    private var successfulCalls: Long = 0,
    private var failedCalls: Long = 0,
    private var totalLatency: Long = 0,
    private val errorCounts: MutableMap<String, Long> = mutableMapOf()
) {
    fun recordSuccess(latencyMs: Long) {
        totalCalls++
        successfulCalls++
        totalLatency += latencyMs
    }

    fun recordFailure(latencyMs: Long, error: Exception) {
        totalCalls++
        failedCalls++
        totalLatency += latencyMs
        errorCounts[error::class.simpleName ?: "Unknown"] =
            errorCounts.getOrDefault(error::class.simpleName ?: "Unknown", 0) + 1
    }

    val successRate: Double get() = if (totalCalls > 0) successfulCalls.toDouble() / totalCalls else 0.0
    val averageLatency: Double get() = if (totalCalls > 0) totalLatency.toDouble() / totalCalls else 0.0
}
```

**External API Integration Summary:**
- **Google Services Ecosystem:** Leverages integrated Android SDKs for optimal UX
- **Rate Limiting:** Intelligent backoff and batching with burst allowance for API quotas
- **Regional Adaptation:** Fallback strategies for 22% of users with limited Google Services
- **Error Handling:** Comprehensive error recovery with graceful degradation
- **Privacy Compliance:** User-controlled data collection with GDPR compliance for EU
- **Offline Resilience:** Local fallbacks when external services unavailable
- **Cost Management:** Free tier optimization with scaling constraint awareness

## Related Architecture Documents

- **[Components & Services](./05-components.md)** - Components implementing these external APIs
- **[API Specifications](./04-api-specifications.md)** - Internal APIs that interact with external services
- **[Tech Stack](./02-tech-stack.md)** - Technologies used for external API integration
- **[Workflows](./06-workflows.md)** - Business processes using external APIs