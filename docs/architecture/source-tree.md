# CultureStack Android Source Tree Structure

## Overview
This document defines the complete source code organization for the CultureStack native Android application. The structure follows Android best practices with a multi-module architecture optimized for maintainability, testability, and team collaboration.

---

## **Project Root Structure**

```
CultureStack-Android/
├── app/                           # Main application module
├── core/                          # Core shared modules
├── feature/                       # Feature-specific modules
├── shared/                        # Cross-module dependencies
├── gradle/                        # Gradle configuration
├── docs/                          # Project documentation
├── scripts/                       # Build and utility scripts
├── .github/                       # GitHub Actions workflows
└── tools/                         # Development tools and configs
```

---

## **Application Module (app/)**

The main application module that orchestrates all features and handles app-level configuration.

```
app/
├── src/
│   ├── main/
│   │   ├── java/com/culturestack/android/
│   │   │   ├── CultureStackApplication.kt      # Application class
│   │   │   ├── MainActivity.kt                 # Main activity
│   │   │   ├── di/                            # App-level DI modules
│   │   │   │   ├── ApplicationModule.kt       # Core app bindings
│   │   │   │   ├── DatabaseModule.kt          # Database configuration
│   │   │   │   ├── NetworkModule.kt           # Network configuration
│   │   │   │   └── GoogleServicesModule.kt    # Google APIs
│   │   │   ├── navigation/                    # App navigation
│   │   │   │   ├── NavigationHost.kt          # Compose navigation
│   │   │   │   ├── NavigationRoutes.kt        # Route definitions
│   │   │   │   └── BottomNavigationBar.kt     # Bottom nav component
│   │   │   └── ui/                           # App-level UI
│   │   │       ├── theme/                    # Material Design theme
│   │   │       ├── components/               # Shared UI components
│   │   │       └── utils/                    # UI utilities
│   │   ├── res/                              # Android resources
│   │   │   ├── layout/                       # XML layouts (minimal)
│   │   │   ├── values/                       # String, colors, dimensions
│   │   │   ├── drawable/                     # Icons and graphics
│   │   │   └── mipmap/                       # App icons
│   │   └── AndroidManifest.xml              # App manifest
│   ├── test/                                # Unit tests
│   └── androidTest/                         # Instrumentation tests
├── build.gradle.kts                         # App module build config
└── proguard-rules.pro                       # ProGuard configuration
```

---

## **Core Modules (core/)**

Shared functionality used across multiple features, organized by concern.

### **Core Common (core/common/)**
```
core/common/
├── src/main/java/com/culturestack/core/common/
│   ├── util/                                # Utility classes
│   │   ├── DateUtils.kt                     # Date/time utilities
│   │   ├── StringUtils.kt                   # String operations
│   │   ├── ValidationUtils.kt               # Input validation
│   │   └── FileUtils.kt                     # File operations
│   ├── extensions/                          # Kotlin extensions
│   │   ├── ContextExtensions.kt            # Context utilities
│   │   ├── ViewExtensions.kt               # View utilities
│   │   └── FlowExtensions.kt               # Flow utilities
│   ├── constants/                           # App constants
│   │   ├── AppConstants.kt                 # General constants
│   │   ├── CultureConstants.kt             # Culture-specific
│   │   └── ApiConstants.kt                 # API endpoints
│   ├── result/                              # Result wrapper classes
│   │   ├── Result.kt                       # Success/Error wrapper
│   │   ├── NetworkError.kt                 # Network error types
│   │   └── ValidationError.kt              # Validation errors
│   └── di/                                  # Common DI modules
│       └── CommonModule.kt                 # Common dependencies
└── build.gradle.kts
```

### **Core Database (core/database/)**
```
core/database/
├── src/main/java/com/culturestack/core/database/
│   ├── CultureDatabase.kt                   # Room database
│   ├── entities/                            # Database entities
│   │   ├── Culture.kt                      # Culture entity
│   │   ├── Subculture.kt                   # Subculture entity
│   │   ├── Recipe.kt                       # Recipe entity
│   │   ├── Observation.kt                  # Observation entity
│   │   ├── Photo.kt                        # Photo entity
│   │   ├── SyncQueue.kt                    # Sync queue entity
│   │   └── ConflictResolution.kt           # Conflict resolution
│   ├── daos/                               # Data Access Objects
│   │   ├── CultureDao.kt                   # Culture operations
│   │   ├── SubcultureDao.kt                # Subculture operations
│   │   ├── RecipeDao.kt                    # Recipe operations
│   │   ├── ObservationDao.kt               # Observation operations
│   │   ├── PhotoDao.kt                     # Photo operations
│   │   └── SyncQueueDao.kt                 # Sync operations
│   ├── converters/                         # Type converters
│   │   ├── DateConverters.kt               # Date conversions
│   │   ├── ListConverters.kt               # List serialization
│   │   └── EnumConverters.kt               # Enum conversions
│   ├── migrations/                         # Database migrations
│   │   ├── Migration1To2.kt                # Version migrations
│   │   └── MigrationUtils.kt               # Migration helpers
│   └── di/                                 # Database DI
│       └── DatabaseModule.kt               # Database bindings
└── build.gradle.kts
```

### **Core Network (core/network/)**
```
core/network/
├── src/main/java/com/culturestack/core/network/
│   ├── di/                                 # Network DI modules
│   │   └── NetworkModule.kt                # HTTP client config
│   ├── google/                             # Google APIs
│   │   ├── DriveApiService.kt              # Google Drive client
│   │   ├── AuthApiService.kt               # Google Sign-In
│   │   ├── BillingService.kt               # Play Billing
│   │   └── dto/                            # API DTOs
│   ├── interceptors/                       # HTTP interceptors
│   │   ├── AuthInterceptor.kt              # Auth header injection
│   │   ├── RateLimitInterceptor.kt         # Rate limiting
│   │   └── LoggingInterceptor.kt           # Request logging
│   ├── adapters/                           # Network adapters
│   │   ├── ResultCallAdapter.kt            # Result wrapper
│   │   └── FlowCallAdapter.kt              # Flow adapter
│   └── utils/                              # Network utilities
│       ├── NetworkMonitor.kt               # Connectivity monitor
│       ├── NetworkError.kt                 # Error handling
│       └── RetryPolicy.kt                  # Retry strategies
└── build.gradle.kts
```

### **Core UI (core/ui/)**
```
core/ui/
├── src/main/java/com/culturestack/core/ui/
│   ├── components/                         # Reusable UI components
│   │   ├── buttons/                        # Button components
│   │   │   ├── PrimaryButton.kt            # Primary action button
│   │   │   ├── SecondaryButton.kt          # Secondary button
│   │   │   └── FloatingActionButton.kt     # FAB component
│   │   ├── cards/                          # Card components
│   │   │   ├── CultureCard.kt              # Culture display card
│   │   │   ├── RecipeCard.kt               # Recipe display card
│   │   │   └── ObservationCard.kt          # Observation card
│   │   ├── forms/                          # Form components
│   │   │   ├── FormTextField.kt            # Text input field
│   │   │   ├── FormDropdown.kt             # Dropdown selector
│   │   │   ├── FormDatePicker.kt           # Date picker
│   │   │   └── FormValidation.kt           # Validation helpers
│   │   ├── indicators/                     # Status indicators
│   │   │   ├── StatusIndicator.kt          # Culture status
│   │   │   ├── ProgressIndicator.kt        # Loading progress
│   │   │   └── SyncIndicator.kt            # Sync status
│   │   ├── navigation/                     # Navigation components
│   │   │   ├── TopAppBar.kt                # App bar component
│   │   │   ├── NavigationDrawer.kt         # Side drawer
│   │   │   └── Breadcrumbs.kt              # Breadcrumb nav
│   │   └── dialogs/                        # Dialog components
│   │       ├── ConfirmationDialog.kt       # Confirmation prompt
│   │       ├── ErrorDialog.kt              # Error display
│   │       └── LoadingDialog.kt            # Loading overlay
│   ├── theme/                              # Material Design theme
│   │   ├── Theme.kt                        # App theme definition
│   │   ├── Color.kt                        # Color palette
│   │   ├── Typography.kt                   # Typography scale
│   │   ├── Shape.kt                        # Component shapes
│   │   └── Dimension.kt                    # Spacing/sizing
│   ├── utils/                              # UI utilities
│   │   ├── ComposeUtils.kt                 # Compose helpers
│   │   ├── PreviewUtils.kt                 # Preview helpers
│   │   └── AccessibilityUtils.kt           # A11y helpers
│   └── preview/                            # Preview providers
│       ├── ThemePreview.kt                 # Theme previews
│       └── ComponentPreviews.kt            # Component previews
└── build.gradle.kts
```

---

## **Feature Modules (feature/)**

Feature-specific modules following clean architecture principles.

### **Culture Management (feature/cultures/)**
```
feature/cultures/
├── src/main/java/com/culturestack/feature/cultures/
│   ├── data/                               # Data layer
│   │   ├── repository/                     # Repository implementations
│   │   │   └── CultureRepositoryImpl.kt    # Culture repository
│   │   ├── datasource/                     # Data sources
│   │   │   ├── CultureLocalDataSource.kt   # Local data source
│   │   │   └── CultureRemoteDataSource.kt  # Remote data source
│   │   └── mappers/                        # Data mappers
│   │       └── CultureMappers.kt           # Entity/DTO mapping
│   ├── domain/                             # Domain layer
│   │   ├── model/                          # Domain models
│   │   │   ├── Culture.kt                  # Culture domain model
│   │   │   ├── Subculture.kt               # Subculture model
│   │   │   └── CultureLineage.kt           # Lineage model
│   │   ├── repository/                     # Repository interfaces
│   │   │   └── CultureRepository.kt        # Repository contract
│   │   └── usecase/                        # Use cases
│   │       ├── CreateCultureUseCase.kt     # Create culture logic
│   │       ├── GetCulturesUseCase.kt       # Get cultures logic
│   │       ├── UpdateCultureUseCase.kt     # Update culture logic
│   │       └── DeleteCultureUseCase.kt     # Delete culture logic
│   ├── presentation/                       # Presentation layer
│   │   ├── ui/                             # UI components
│   │   │   ├── list/                       # Culture list screens
│   │   │   │   ├── CultureListScreen.kt    # List view
│   │   │   │   └── CultureListItem.kt      # List item
│   │   │   ├── detail/                     # Culture detail screens
│   │   │   │   ├── CultureDetailScreen.kt  # Detail view
│   │   │   │   └── CultureDetailTabs.kt    # Detail tabs
│   │   │   ├── create/                     # Culture creation
│   │   │   │   ├── CreateCultureScreen.kt  # Create screen
│   │   │   │   └── CreateCultureForm.kt    # Create form
│   │   │   └── edit/                       # Culture editing
│   │   │       ├── EditCultureScreen.kt    # Edit screen
│   │   │       └── EditCultureForm.kt      # Edit form
│   │   ├── viewmodel/                      # View models
│   │   │   ├── CultureListViewModel.kt     # List view model
│   │   │   ├── CultureDetailViewModel.kt   # Detail view model
│   │   │   └── CreateCultureViewModel.kt   # Create view model
│   │   └── navigation/                     # Feature navigation
│   │       ├── CultureNavigation.kt        # Navigation setup
│   │       └── CultureRoutes.kt            # Route definitions
│   └── di/                                 # Feature DI
│       └── CultureModule.kt                # Feature bindings
└── build.gradle.kts
```

### **Recipe System (feature/recipes/)**
```
feature/recipes/
├── src/main/java/com/culturestack/feature/recipes/
│   ├── data/
│   │   ├── repository/
│   │   │   └── RecipeRepositoryImpl.kt
│   │   ├── datasource/
│   │   │   ├── RecipeLocalDataSource.kt
│   │   │   └── RecipeRemoteDataSource.kt
│   │   └── mappers/
│   │       └── RecipeMappers.kt
│   ├── domain/
│   │   ├── model/
│   │   │   ├── Recipe.kt
│   │   │   ├── Ingredient.kt
│   │   │   └── RecipeCategory.kt
│   │   ├── repository/
│   │   │   └── RecipeRepository.kt
│   │   └── usecase/
│   │       ├── CreateRecipeUseCase.kt
│   │       ├── GetRecipesUseCase.kt
│   │       ├── SearchRecipesUseCase.kt
│   │       └── UpdateRecipeUseCase.kt
│   ├── presentation/
│   │   ├── ui/
│   │   │   ├── library/
│   │   │   │   ├── RecipeLibraryScreen.kt
│   │   │   │   └── RecipeLibraryGrid.kt
│   │   │   ├── detail/
│   │   │   │   ├── RecipeDetailScreen.kt
│   │   │   │   └── IngredientList.kt
│   │   │   ├── create/
│   │   │   │   ├── CreateRecipeScreen.kt
│   │   │   │   └── IngredientEditor.kt
│   │   │   └── search/
│   │   │       ├── RecipeSearchScreen.kt
│   │   │       └── RecipeSearchBar.kt
│   │   ├── viewmodel/
│   │   │   ├── RecipeLibraryViewModel.kt
│   │   │   ├── RecipeDetailViewModel.kt
│   │   │   └── CreateRecipeViewModel.kt
│   │   └── navigation/
│   │       ├── RecipeNavigation.kt
│   │       └── RecipeRoutes.kt
│   └── di/
│       └── RecipeModule.kt
└── build.gradle.kts
```

### **Observation System (feature/observations/)**
```
feature/observations/
├── src/main/java/com/culturestack/feature/observations/
│   ├── data/
│   │   ├── repository/
│   │   │   └── ObservationRepositoryImpl.kt
│   │   └── datasource/
│   │       ├── ObservationLocalDataSource.kt
│   │       └── PhotoStorageDataSource.kt
│   ├── domain/
│   │   ├── model/
│   │   │   ├── Observation.kt
│   │   │   ├── Photo.kt
│   │   │   └── ObservationStats.kt
│   │   ├── repository/
│   │   │   └── ObservationRepository.kt
│   │   └── usecase/
│   │       ├── AddObservationUseCase.kt
│   │       ├── CapturePhotoUseCase.kt
│   │       └── GetObservationHistoryUseCase.kt
│   ├── presentation/
│   │   ├── ui/
│   │   │   ├── add/
│   │   │   │   ├── AddObservationScreen.kt
│   │   │   │   └── PhotoCaptureComponent.kt
│   │   │   ├── history/
│   │   │   │   ├── ObservationHistoryScreen.kt
│   │   │   │   └── ObservationTimeline.kt
│   │   │   └── gallery/
│   │   │       ├── PhotoGalleryScreen.kt
│   │   │       └── PhotoViewer.kt
│   │   ├── viewmodel/
│   │   │   ├── AddObservationViewModel.kt
│   │   │   ├── ObservationHistoryViewModel.kt
│   │   │   └── PhotoGalleryViewModel.kt
│   │   └── navigation/
│   │       └── ObservationNavigation.kt
│   └── di/
│       └── ObservationModule.kt
└── build.gradle.kts
```

### **Authentication (feature/auth/)**
```
feature/auth/
├── src/main/java/com/culturestack/feature/auth/
│   ├── data/
│   │   ├── repository/
│   │   │   └── AuthRepositoryImpl.kt
│   │   └── datasource/
│   │       ├── GoogleAuthDataSource.kt
│   │       └── AuthPreferencesDataSource.kt
│   ├── domain/
│   │   ├── model/
│   │   │   ├── User.kt
│   │   │   └── AuthState.kt
│   │   ├── repository/
│   │   │   └── AuthRepository.kt
│   │   └── usecase/
│   │       ├── SignInUseCase.kt
│   │       ├── SignOutUseCase.kt
│   │       └── GetAuthStateUseCase.kt
│   ├── presentation/
│   │   ├── ui/
│   │   │   ├── signin/
│   │   │   │   └── SignInScreen.kt
│   │   │   └── profile/
│   │   │       └── ProfileScreen.kt
│   │   ├── viewmodel/
│   │   │   ├── SignInViewModel.kt
│   │   │   └── ProfileViewModel.kt
│   │   └── navigation/
│   │       └── AuthNavigation.kt
│   └── di/
│       └── AuthModule.kt
└── build.gradle.kts
```

---

## **Shared Modules (shared/)**

Cross-cutting concerns and utilities shared across features.

### **Shared Testing (shared/testing/)**
```
shared/testing/
├── src/main/java/com/culturestack/shared/testing/
│   ├── factory/                            # Test data factories
│   │   ├── CultureFactory.kt               # Culture test data
│   │   ├── RecipeFactory.kt                # Recipe test data
│   │   └── ObservationFactory.kt           # Observation test data
│   ├── rules/                              # Custom test rules
│   │   ├── DatabaseRule.kt                 # Database test rule
│   │   └── NetworkRule.kt                  # Network test rule
│   ├── fakes/                              # Fake implementations
│   │   ├── FakeCultureRepository.kt        # Fake repository
│   │   └── FakeNetworkDataSource.kt        # Fake network
│   ├── fixtures/                           # Test fixtures
│   │   ├── TestData.kt                     # Static test data
│   │   └── TestConstants.kt                # Test constants
│   └── utils/                              # Test utilities
│       ├── ComposeTestUtils.kt             # Compose testing
│       └── CoroutineTestUtils.kt           # Coroutine testing
└── build.gradle.kts
```

### **Shared Analytics (shared/analytics/)**
```
shared/analytics/
├── src/main/java/com/culturestack/shared/analytics/
│   ├── AnalyticsTracker.kt                 # Analytics interface
│   ├── FirebaseAnalyticsTracker.kt         # Firebase implementation
│   ├── events/                             # Analytics events
│   │   ├── CultureEvents.kt                # Culture tracking events
│   │   ├── RecipeEvents.kt                 # Recipe tracking events
│   │   └── UserEvents.kt                   # User behavior events
│   └── di/
│       └ AnalyticsModule.kt                # Analytics DI
└── build.gradle.kts
```

---

## **Build Configuration**

### **Root build.gradle.kts**
```
// Project-level build configuration
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.android.library) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.hilt) apply false
}

// Shared build configuration for all modules
subprojects {
    apply(from = "$rootDir/gradle/common.gradle")
}
```

### **gradle/libs.versions.toml**
```
# Version catalog for dependency management
[versions]
kotlin = "1.9.10"
android-gradle = "8.1.2"
compose = "1.5.4"
hilt = "2.48"
room = "2.5.0"
# ... other versions

[libraries]
# Grouped dependencies
# ... library definitions

[plugins]
# Plugin aliases
# ... plugin definitions
```

---

## **Development Scripts (scripts/)**

```
scripts/
├── setup/
│   ├── setup-development.sh               # Development environment setup
│   ├── install-dependencies.sh            # Dependency installation
│   └── configure-git-hooks.sh             # Git hooks setup
├── build/
│   ├── build-release.sh                   # Release build script
│   ├── run-tests.sh                       # Test execution
│   └── generate-apk.sh                    # APK generation
├── quality/
│   ├── run-detekt.sh                      # Static analysis
│   ├── run-lint.sh                        # Android lint
│   └── check-formatting.sh                # Code formatting
└── deployment/
    ├── deploy-beta.sh                      # Beta deployment
    └── deploy-production.sh               # Production deployment
```

---

## **Documentation Structure (docs/)**

```
docs/
├── architecture/                          # Architecture documentation
├── prd/                                   # Product requirements (sharded)
├── frontend/                              # UI/UX specifications (sharded)
├── api/                                   # API documentation
├── development/                           # Development guides
├── deployment/                            # Deployment procedures
└── user/                                  # User documentation
```

---

## **File Naming Conventions**

### **Kotlin Files**
- **Classes**: PascalCase (e.g., `CultureRepository.kt`)
- **Interfaces**: PascalCase (e.g., `CultureDao.kt`)
- **Objects**: PascalCase (e.g., `DatabaseModule.kt`)
- **Extensions**: DescriptiveExtensions.kt (e.g., `StringExtensions.kt`)

### **Compose Files**
- **Screens**: `[Feature]Screen.kt` (e.g., `CultureListScreen.kt`)
- **Components**: `[Purpose]Component.kt` (e.g., `StatusIndicatorComponent.kt`)
- **Dialogs**: `[Purpose]Dialog.kt` (e.g., `ConfirmationDialog.kt`)

### **Test Files**
- **Unit Tests**: `[ClassName]Test.kt` (e.g., `CultureRepositoryTest.kt`)
- **Integration Tests**: `[Feature]IntegrationTest.kt`
- **UI Tests**: `[Screen]ScreenTest.kt` (e.g., `CultureListScreenTest.kt`)

### **Resource Files**
- **Strings**: Feature-based grouping (e.g., `culture_`, `recipe_`, `auth_`)
- **Dimensions**: Semantic naming (e.g., `spacing_small`, `text_size_large`)
- **Colors**: Descriptive names (e.g., `culture_healthy`, `error_red`)

---

## **Module Dependencies**

### **Dependency Flow**
```
app
├── depends on all feature modules
├── depends on all core modules
└── depends on shared modules

feature/*
├── depends on core modules
├── depends on shared modules
└── no cross-feature dependencies

core/*
├── minimal external dependencies
└── no feature module dependencies

shared/*
├── depends on core/common
└── no feature dependencies
```

### **Circular Dependency Prevention**
- **Features never depend on other features**
- **Core modules have minimal dependencies**
- **Shared modules only depend on core/common**
- **App module orchestrates all dependencies**

---

## **Code Organization Principles**

### **Clean Architecture**
- **Presentation Layer**: UI components, ViewModels, Navigation
- **Domain Layer**: Use cases, Repository interfaces, Domain models
- **Data Layer**: Repository implementations, Data sources, DTOs

### **Separation of Concerns**
- **Single Responsibility**: Each class/file has one clear purpose
- **Interface Segregation**: Small, focused interfaces
- **Dependency Inversion**: Depend on abstractions, not concretions

### **Testing Strategy**
- **Unit Tests**: Domain layer and ViewModels (70%)
- **Integration Tests**: Repository and data source integration (20%)
- **UI Tests**: Critical user flows and navigation (10%)

---

## **Migration & Maintenance**

### **Module Evolution**
- **Feature Addition**: Create new feature module following template
- **Feature Removal**: Clean removal without breaking dependencies
- **Core Updates**: Coordinate across all dependent modules

### **Refactoring Guidelines**
- **Extract Common Code**: Move to appropriate core module
- **Split Large Features**: Create sub-modules when features grow
- **Update Dependencies**: Use version catalog for consistency

This source tree structure provides a solid foundation for the CultureStack Android application development, ensuring maintainability, testability, and team collaboration efficiency.