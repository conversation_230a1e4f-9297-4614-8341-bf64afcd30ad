# **Pipeline Architecture Overview**

```mermaid
graph TB
    subgraph "Code Repository (GitHub)"
        A1[Feature Branch] --> A2[Pull Request]
        A3[Main Branch] --> A4[Release Branch]
    end

    subgraph "CI Pipeline (GitHub Actions)"
        B1[PR Validation] --> B2[Code Quality]
        B2 --> B3[Unit Tests]
        B3 --> B4[Integration Tests]
        B4 --> B5[Build APK]
        B5 --> B6[Security Scan]
    end

    subgraph "CD Pipeline (Release)"
        C1[Release Build] --> C2[UI Tests]
        C2 --> C3[Performance Tests]
        C3 --> C4[Sign APK/AAB]
        C4 --> C5[Upload to Play Console]
        C5 --> C6[Deploy to Beta Track]
    end

    subgraph "Monitoring & Feedback"
        D1[Firebase Crashlytics]
        D2[Play Console Vitals]
        D3[Performance Monitoring]
    end

    A2 --> B1
    A4 --> C1
    C6 --> D1
    C6 --> D2
    C6 --> D3
```

---
