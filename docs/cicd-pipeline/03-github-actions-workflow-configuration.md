# **GitHub Actions Workflow Configuration**

## **1. Pull Request Validation Pipeline**

**File:** `.github/workflows/pr-validation.yml`

```yaml
name: PR Validation Pipeline

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'app/**'
      - 'core/**'
      - 'feature/**'
      - 'gradle/**'
      - '*.gradle.kts'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Run Ktlint Check
      run: ./gradlew ktlintCheck

    - name: Run Detekt Analysis
      run: ./gradlew detekt

    - name: Upload Detekt Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: detekt-reports
        path: build/reports/detekt/

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Run Unit Tests
      run: ./gradlew testDebugUnitTest --continue

    - name: Generate Test Report
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Unit Test Results
        path: '**/build/test-results/testDebugUnitTest/TEST-*.xml'
        reporter: java-junit

    - name: Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        files: '**/build/reports/jacoco/testDebugUnitTestCoverage/testDebugUnitTestCoverage.xml'
        fail_ci_if_error: true

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: unit-tests

    strategy:
      matrix:
        api-level: [24, 30, 34]
        target: [google_apis]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Enable KVM (Hardware Acceleration)
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: AVD Cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}-${{ matrix.target }}

    - name: Create AVD and Generate Snapshot
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."

    - name: Run Integration Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: ./gradlew connectedDebugAndroidTest --continue

  build-validation:
    name: Build Validation
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [code-quality, unit-tests]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Build Debug APK
      run: ./gradlew assembleDebug

    - name: Build Release APK (Unsigned)
      run: ./gradlew assembleRelease

    - name: Validate APK
      run: |
        # Check APK size (should be < 50MB)
        APK_SIZE=$(stat -c%s app/build/outputs/apk/debug/app-debug.apk)
        if [ $APK_SIZE -gt 52428800 ]; then
          echo "APK size too large: $APK_SIZE bytes"
          exit 1
        fi
        echo "APK size acceptable: $APK_SIZE bytes"

    - name: Upload Debug APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: app/build/outputs/apk/debug/app-debug.apk
```

## **2. Release Deployment Pipeline**

**File:** `.github/workflows/release-deploy.yml`

```yaml
name: Release Deployment Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      release_track:
        description: 'Release track'
        required: true
        default: 'beta'
        type: choice
        options:
          - beta
          - production

env:
  VERSION_CODE_BASE: 1000

jobs:
  build-release:
    name: Build Release
    runs-on: ubuntu-latest
    timeout-minutes: 30

    outputs:
      version_code: ${{ steps.version.outputs.version_code }}
      version_name: ${{ steps.version.outputs.version_name }}

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Calculate Version
      id: version
      run: |
        if [[ $GITHUB_REF == refs/tags/* ]]; then
          VERSION_NAME=${GITHUB_REF#refs/tags/v}
        else
          VERSION_NAME="1.0.0-$(git rev-parse --short HEAD)"
        fi
        VERSION_CODE=$((VERSION_CODE_BASE + GITHUB_RUN_NUMBER))
        echo "version_code=$VERSION_CODE" >> $GITHUB_OUTPUT
        echo "version_name=$VERSION_NAME" >> $GITHUB_OUTPUT
        echo "Building version: $VERSION_NAME ($VERSION_CODE)"

    - name: Create Release Keystore
      run: |
        echo "${{ secrets.RELEASE_KEYSTORE_BASE64 }}" | base64 -d > release.keystore

    - name: Build Release AAB
      run: ./gradlew bundleRelease
      env:
        RELEASE_KEYSTORE_PATH: ${{ github.workspace }}/release.keystore
        RELEASE_KEY_ALIAS: ${{ secrets.RELEASE_KEY_ALIAS }}
        RELEASE_KEY_PASSWORD: ${{ secrets.RELEASE_KEY_PASSWORD }}
        RELEASE_STORE_PASSWORD: ${{ secrets.RELEASE_STORE_PASSWORD }}
        VERSION_CODE: ${{ steps.version.outputs.version_code }}
        VERSION_NAME: ${{ steps.version.outputs.version_name }}

    - name: Build Release APK
      run: ./gradlew assembleRelease
      env:
        RELEASE_KEYSTORE_PATH: ${{ github.workspace }}/release.keystore
        RELEASE_KEY_ALIAS: ${{ secrets.RELEASE_KEY_ALIAS }}
        RELEASE_KEY_PASSWORD: ${{ secrets.RELEASE_KEY_PASSWORD }}
        RELEASE_STORE_PASSWORD: ${{ secrets.RELEASE_STORE_PASSWORD }}
        VERSION_CODE: ${{ steps.version.outputs.version_code }}
        VERSION_NAME: ${{ steps.version.outputs.version_name }}

    - name: Sign APK with Play App Signing
      uses: r0adkll/sign-android-release@v1
      with:
        releaseDirectory: app/build/outputs/apk/release
        signingKeyBase64: ${{ secrets.RELEASE_KEYSTORE_BASE64 }}
        alias: ${{ secrets.RELEASE_KEY_ALIAS }}
        keyStorePassword: ${{ secrets.RELEASE_STORE_PASSWORD }}
        keyPassword: ${{ secrets.RELEASE_KEY_PASSWORD }}

    - name: Upload Release Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: release-artifacts
        path: |
          app/build/outputs/bundle/release/app-release.aab
          app/build/outputs/apk/release/app-release.apk

    - name: Clean up Keystore
      if: always()
      run: rm -f release.keystore

  ui-tests:
    name: UI Tests (Release)
    runs-on: ubuntu-latest
    timeout-minutes: 45
    needs: build-release

    strategy:
      matrix:
        api-level: [28, 33]
        target: [google_apis_playstore]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Enable KVM
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Run UI Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          adb install app-release.apk
          ./gradlew connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.culturestack.CriticalPathTest

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: build-release

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Run MobSF Security Scan
      uses: fundacaocerti/mobsf-action@v1.7.1
      with:
        input-file-path: app-release.apk
        mobsf-url: ${{ secrets.MOBSF_URL }}
        mobsf-api-key: ${{ secrets.MOBSF_API_KEY }}

    - name: OWASP Dependency Check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'CultureStack'
        path: '.'
        format: 'ALL'

    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: reports/

  deploy-play-store:
    name: Deploy to Play Store
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [build-release, ui-tests, security-scan]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Setup Service Account
      run: |
        echo "${{ secrets.GOOGLE_SERVICES_JSON }}" | base64 -d > service-account.json

    - name: Determine Release Track
      id: track
      run: |
        if [[ "${{ github.event.inputs.release_track }}" ]]; then
          TRACK="${{ github.event.inputs.release_track }}"
        elif [[ $GITHUB_REF == refs/tags/* ]]; then
          TRACK="production"
        else
          TRACK="beta"
        fi
        echo "track=$TRACK" >> $GITHUB_OUTPUT
        echo "Deploying to track: $TRACK"

    - name: Upload to Play Store
      uses: r0adkll/upload-google-play@v1.1.1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.culturestack.android
        releaseFiles: app-release.aab
        track: ${{ steps.track.outputs.track }}
        status: completed
        inAppUpdatePriority: 2
        userFraction: 0.1
        whatsNewDirectory: distribution/whatsnew
        mappingFile: app/build/outputs/mapping/release/mapping.txt
        debugSymbols: app/build/outputs/native-debug-symbols/release/native-debug-symbols.zip

    - name: Create GitHub Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          app-release.apk
          app-release.aab
        body: |
          ## Release Notes
          Version: ${{ needs.build-release.outputs.version_name }}
          Build: ${{ needs.build-release.outputs.version_code }}

          ### Changes in this release
          - See commit history for detailed changes

          ### Testing
          - ✅ All unit tests passed
          - ✅ Integration tests passed
          - ✅ UI tests passed
          - ✅ Security scan completed
        draft: false
        prerelease: false

    - name: Clean up Service Account
      if: always()
      run: rm -f service-account.json

  notify-completion:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-play-store]
    if: always()

    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#culturestack-releases'
        text: |
          CultureStack Android ${{ needs.build-release.outputs.version_name }}
          deployed to Play Store ${{ steps.track.outputs.track }} track
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
```

---
