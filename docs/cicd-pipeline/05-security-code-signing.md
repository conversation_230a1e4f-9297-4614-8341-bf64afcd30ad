# **Security & Code Signing**

## **Keystore Management**

### **Debug Keystore (Development)**
```bash
# Auto-generated by Android SDK
~/.android/debug.keystore
# Credentials:
# Store password: android
# Key alias: androiddebugkey
# Key password: android
```

### **Release Keystore (Production)**
```bash
# Generate release keystore (one-time setup)
keytool -genkey -v -keystore release.keystore -keyalg RSA -keysize 2048 -validity 10000 -alias release

# Store as GitHub Secret (base64 encoded)
base64 -i release.keystore | pbcopy
# Add to GitHub Secrets as RELEASE_KEYSTORE_BASE64
```

## **Play App Signing Integration**

```yaml
# Google Play Console → Setup → App signing
# Upload signing key certificate
# Enable Play App Signing for automated key management
# Download upload certificate for CI/CD pipeline

# GitHub Secrets required:
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON='{...service account json...}'
RELEASE_KEYSTORE_BASE64='...base64 encoded keystore...'
RELEASE_KEY_ALIAS='release'
RELEASE_KEY_PASSWORD='secure_password'
RELEASE_STORE_PASSWORD='secure_password'
```

---
