# **Testing Strategy Integration**

## **Test Execution Matrix**

| Test Type | Environment | Execution | Coverage Target |
|-----------|-------------|-----------|-----------------|
| **Unit Tests** | JVM | Every PR | 80%+ |
| **Integration Tests** | Android Emulator | Every PR | Key workflows |
| **UI Tests** | Multiple API levels | Release only | Critical paths |
| **Performance Tests** | Real devices | Weekly | Load & memory |
| **Security Tests** | Static analysis | Every release | OWASP compliance |

## **Test Configuration Files**

**File:** `app/src/test/resources/robolectric.properties`
```properties
sdk=33
qualifiers=en-rUS-w411dp-h731dp-xhdpi
```

**File:** `app/src/androidTest/java/com/culturestack/HiltTestRunner.kt`
```kotlin
@HiltAndroidApp
class HiltTestApplication : Application()

class HiltTestRunner : AndroidJUnitRunner() {
    override fun newApplication(
        cl: ClassLoader?,
        name: String?,
        context: Context?
    ): Application {
        return super.newApplication(cl, HiltTestApplication::class.java.name, context)
    }
}
```

---
