# **Performance Monitoring Integration**

## **Build Performance Metrics**

**File:** `.github/workflows/performance-monitoring.yml`

```yaml
name: Performance Monitoring

on:
  schedule:
    - cron: '0 6 * * 1'  # Weekly on Monday 6 AM
  workflow_dispatch:

jobs:
  build-performance:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Measure Build Performance
      run: |
        # Clean build timing
        time ./gradlew clean build --profile

        # Incremental build timing
        touch app/src/main/java/com/culturestack/MainActivity.kt
        time ./gradlew assembleDebug --profile

    - name: Upload Build Scan
      run: ./gradlew build --scan
```

## **App Performance Monitoring**

```kotlin
// Firebase Performance integration
class CultureStackApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        if (BuildConfig.ENABLE_CRASHLYTICS) {
            // Initialize performance monitoring
            val performance = FirebasePerformance.getInstance()
            performance.isPerformanceCollectionEnabled = true

            // Custom traces for key operations
            val cultureCreationTrace = performance.newTrace("culture_creation")
            val photoUploadTrace = performance.newTrace("photo_upload")
            val syncOperationTrace = performance.newTrace("sync_operation")
        }
    }
}
```

---
