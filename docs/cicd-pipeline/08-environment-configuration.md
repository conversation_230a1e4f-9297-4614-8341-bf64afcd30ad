# **Environment Configuration**

## **GitHub Secrets Management**

```yaml
# Required GitHub Repository Secrets:

# Google Play Console
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON='{...service account json...}'

# Code Signing
RELEASE_KEYSTORE_BASE64='...base64 encoded keystore...'
RELEASE_KEY_ALIAS='release'
RELEASE_KEY_PASSWORD='secure_password'
RELEASE_STORE_PASSWORD='secure_password'

# Firebase/Google Services
GOOGLE_SERVICES_JSON='{...google-services.json content...}'

# External Services
MOBSF_URL='https://mobsf.example.com'
MOBSF_API_KEY='api_key_for_security_scanning'

# Notifications
SLACK_WEBHOOK='https://hooks.slack.com/...'
```

## **Branch Protection Rules**

```yaml
# GitHub Repository Settings → Branches → Branch protection rules

main:
  required_status_checks:
    - "Code Quality Checks"
    - "Unit Tests"
    - "Integration Tests"
    - "Build Validation"
  enforce_admins: true
  required_pull_request_reviews:
    required_approving_reviews: 1
    dismiss_stale_reviews: true
    require_code_owner_reviews: true
  restrictions: null

develop:
  required_status_checks:
    - "Code Quality Checks"
    - "Unit Tests"
  enforce_admins: false
  required_pull_request_reviews:
    required_approving_reviews: 1
```

---
