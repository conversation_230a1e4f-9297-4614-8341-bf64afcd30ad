# GitHub Actions Workflows

## Overview
This document provides complete GitHub Actions workflow configurations for CultureStack's CI/CD pipeline, including pull request validation, release deployment, and performance monitoring workflows.

---

## **Pull Request Validation Pipeline**

### **Workflow Configuration**

**File:** `.github/workflows/pr-validation.yml`

```yaml
name: PR Validation Pipeline

on:
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'app/**'
      - 'core/**'
      - 'feature/**'
      - 'gradle/**'
      - '*.gradle.kts'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  code-quality:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Run Ktlint Check
      run: ./gradlew ktlintCheck

    - name: Run Detekt Analysis
      run: ./gradlew detekt

    - name: Upload Detekt Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: detekt-reports
        path: build/reports/detekt/

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Run Unit Tests
      run: ./gradlew testDebugUnitTest --continue

    - name: Generate Test Report
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Unit Test Results
        path: '**/build/test-results/testDebugUnitTest/TEST-*.xml'
        reporter: java-junit

    - name: Upload Coverage Reports
      uses: codecov/codecov-action@v3
      with:
        files: '**/build/reports/jacoco/testDebugUnitTestCoverage/testDebugUnitTestCoverage.xml'
        fail_ci_if_error: true

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: unit-tests

    strategy:
      matrix:
        api-level: [24, 30, 34]
        target: [google_apis]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Enable KVM (Hardware Acceleration)
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: AVD Cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}-${{ matrix.target }}

    - name: Create AVD and Generate Snapshot
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."

    - name: Run Integration Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: ./gradlew connectedDebugAndroidTest --continue

  build-validation:
    name: Build Validation
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [code-quality, unit-tests]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Build Debug APK
      run: ./gradlew assembleDebug

    - name: Build Release APK (Unsigned)
      run: ./gradlew assembleRelease

    - name: Validate APK
      run: |
        # Check APK size (should be < 50MB)
        APK_SIZE=$(stat -c%s app/build/outputs/apk/debug/app-debug.apk)
        if [ $APK_SIZE -gt 52428800 ]; then
          echo "APK size too large: $APK_SIZE bytes"
          exit 1
        fi
        echo "APK size acceptable: $APK_SIZE bytes"

    - name: Upload Debug APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: app/build/outputs/apk/debug/app-debug.apk
```

---

## **Release Deployment Pipeline**

### **Workflow Configuration**

**File:** `.github/workflows/release-deploy.yml`

```yaml
name: Release Deployment Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      release_track:
        description: 'Release track'
        required: true
        default: 'beta'
        type: choice
        options:
          - beta
          - production

env:
  VERSION_CODE_BASE: 1000

jobs:
  build-release:
    name: Build Release
    runs-on: ubuntu-latest
    timeout-minutes: 30

    outputs:
      version_code: ${{ steps.version.outputs.version_code }}
      version_name: ${{ steps.version.outputs.version_name }}

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Calculate Version
      id: version
      run: |
        if [[ $GITHUB_REF == refs/tags/* ]]; then
          VERSION_NAME=${GITHUB_REF#refs/tags/v}
        else
          VERSION_NAME="1.0.0-$(git rev-parse --short HEAD)"
        fi
        VERSION_CODE=$((VERSION_CODE_BASE + GITHUB_RUN_NUMBER))
        echo "version_code=$VERSION_CODE" >> $GITHUB_OUTPUT
        echo "version_name=$VERSION_NAME" >> $GITHUB_OUTPUT
        echo "Building version: $VERSION_NAME ($VERSION_CODE)"

    - name: Create Release Keystore
      run: |
        echo "${{ secrets.RELEASE_KEYSTORE_BASE64 }}" | base64 -d > release.keystore

    - name: Build Release AAB
      run: ./gradlew bundleRelease
      env:
        RELEASE_KEYSTORE_PATH: ${{ github.workspace }}/release.keystore
        RELEASE_KEY_ALIAS: ${{ secrets.RELEASE_KEY_ALIAS }}
        RELEASE_KEY_PASSWORD: ${{ secrets.RELEASE_KEY_PASSWORD }}
        RELEASE_STORE_PASSWORD: ${{ secrets.RELEASE_STORE_PASSWORD }}
        VERSION_CODE: ${{ steps.version.outputs.version_code }}
        VERSION_NAME: ${{ steps.version.outputs.version_name }}

    - name: Build Release APK
      run: ./gradlew assembleRelease
      env:
        RELEASE_KEYSTORE_PATH: ${{ github.workspace }}/release.keystore
        RELEASE_KEY_ALIAS: ${{ secrets.RELEASE_KEY_ALIAS }}
        RELEASE_KEY_PASSWORD: ${{ secrets.RELEASE_KEY_PASSWORD }}
        RELEASE_STORE_PASSWORD: ${{ secrets.RELEASE_STORE_PASSWORD }}
        VERSION_CODE: ${{ steps.version.outputs.version_code }}
        VERSION_NAME: ${{ steps.version.outputs.version_name }}

    - name: Sign APK with Play App Signing
      uses: r0adkll/sign-android-release@v1
      with:
        releaseDirectory: app/build/outputs/apk/release
        signingKeyBase64: ${{ secrets.RELEASE_KEYSTORE_BASE64 }}
        alias: ${{ secrets.RELEASE_KEY_ALIAS }}
        keyStorePassword: ${{ secrets.RELEASE_STORE_PASSWORD }}
        keyPassword: ${{ secrets.RELEASE_KEY_PASSWORD }}

    - name: Upload Release Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: release-artifacts
        path: |
          app/build/outputs/bundle/release/app-release.aab
          app/build/outputs/apk/release/app-release.apk

    - name: Clean up Keystore
      if: always()
      run: rm -f release.keystore

  ui-tests:
    name: UI Tests (Release)
    runs-on: ubuntu-latest
    timeout-minutes: 45
    needs: build-release

    strategy:
      matrix:
        api-level: [28, 33]
        target: [google_apis_playstore]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Enable KVM
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: Run UI Tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: ${{ matrix.target }}
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          adb install app-release.apk
          ./gradlew connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.culturestack.CriticalPathTest

  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: build-release

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Run MobSF Security Scan
      uses: fundacaocerti/mobsf-action@v1.7.1
      with:
        input-file-path: app-release.apk
        mobsf-url: ${{ secrets.MOBSF_URL }}
        mobsf-api-key: ${{ secrets.MOBSF_API_KEY }}

    - name: OWASP Dependency Check
      uses: dependency-check/Dependency-Check_Action@main
      with:
        project: 'CultureStack'
        path: '.'
        format: 'ALL'

    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: reports/

  deploy-play-store:
    name: Deploy to Play Store
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [build-release, ui-tests, security-scan]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/')

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Download Release Artifacts
      uses: actions/download-artifact@v3
      with:
        name: release-artifacts

    - name: Setup Service Account
      run: |
        echo "${{ secrets.GOOGLE_SERVICES_JSON }}" | base64 -d > service-account.json

    - name: Determine Release Track
      id: track
      run: |
        if [[ "${{ github.event.inputs.release_track }}" ]]; then
          TRACK="${{ github.event.inputs.release_track }}"
        elif [[ $GITHUB_REF == refs/tags/* ]]; then
          TRACK="production"
        else
          TRACK="beta"
        fi
        echo "track=$TRACK" >> $GITHUB_OUTPUT
        echo "Deploying to track: $TRACK"

    - name: Upload to Play Store
      uses: r0adkll/upload-google-play@v1.1.1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.culturestack.android
        releaseFiles: app-release.aab
        track: ${{ steps.track.outputs.track }}
        status: completed
        inAppUpdatePriority: 2
        userFraction: 0.1
        whatsNewDirectory: distribution/whatsnew
        mappingFile: app/build/outputs/mapping/release/mapping.txt
        debugSymbols: app/build/outputs/native-debug-symbols/release/native-debug-symbols.zip

    - name: Create GitHub Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          app-release.apk
          app-release.aab
        body: |
          ## Release Notes
          Version: ${{ needs.build-release.outputs.version_name }}
          Build: ${{ needs.build-release.outputs.version_code }}

          ### Changes in this release
          - See commit history for detailed changes

          ### Testing
          - ✅ All unit tests passed
          - ✅ Integration tests passed
          - ✅ UI tests passed
          - ✅ Security scan completed
        draft: false
        prerelease: false

    - name: Clean up Service Account
      if: always()
      run: rm -f service-account.json

  notify-completion:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-play-store]
    if: always()

    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#culturestack-releases'
        text: |
          CultureStack Android ${{ needs.build-release.outputs.version_name }}
          deployed to Play Store ${{ steps.track.outputs.track }} track
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      if: always()
```

---

## **Performance Monitoring Workflow**

### **Workflow Configuration**

**File:** `.github/workflows/performance-monitoring.yml`

```yaml
name: Performance Monitoring

on:
  schedule:
    - cron: '0 6 * * 1'  # Weekly on Monday 6 AM
  workflow_dispatch:

jobs:
  build-performance:
    name: Build Performance Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 45

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Warm Up Gradle Daemon
      run: ./gradlew help

    - name: Measure Clean Build Performance
      run: |
        echo "🧹 Starting clean build measurement..."
        ./gradlew clean
        START_TIME=$(date +%s)
        ./gradlew assembleDebug --profile
        END_TIME=$(date +%s)
        CLEAN_BUILD_TIME=$((END_TIME - START_TIME))
        echo "CLEAN_BUILD_TIME=$CLEAN_BUILD_TIME" >> $GITHUB_ENV
        echo "✅ Clean build completed in $CLEAN_BUILD_TIME seconds"

    - name: Measure Incremental Build Performance
      run: |
        echo "🔄 Starting incremental build measurement..."
        # Simulate code change
        touch app/src/main/java/com/culturestack/MainActivity.kt
        START_TIME=$(date +%s)
        ./gradlew assembleDebug --profile
        END_TIME=$(date +%s)
        INCREMENTAL_BUILD_TIME=$((END_TIME - START_TIME))
        echo "INCREMENTAL_BUILD_TIME=$INCREMENTAL_BUILD_TIME" >> $GITHUB_ENV
        echo "✅ Incremental build completed in $INCREMENTAL_BUILD_TIME seconds"

    - name: Upload Build Scan
      run: ./gradlew build --scan

    - name: Generate Performance Report
      run: |
        echo "# Build Performance Report" > performance-report.md
        echo "" >> performance-report.md
        echo "**Date:** $(date)" >> performance-report.md
        echo "**Commit:** ${{ github.sha }}" >> performance-report.md
        echo "" >> performance-report.md
        echo "| Metric | Time | Threshold | Status |" >> performance-report.md
        echo "|--------|------|-----------|--------|" >> performance-report.md

        # Clean build analysis
        if [ $CLEAN_BUILD_TIME -le 600 ]; then
          CLEAN_STATUS="✅ PASS"
        else
          CLEAN_STATUS="❌ FAIL"
        fi
        echo "| Clean Build | ${CLEAN_BUILD_TIME}s | ≤ 600s | $CLEAN_STATUS |" >> performance-report.md

        # Incremental build analysis
        if [ $INCREMENTAL_BUILD_TIME -le 60 ]; then
          INCREMENTAL_STATUS="✅ PASS"
        else
          INCREMENTAL_STATUS="❌ FAIL"
        fi
        echo "| Incremental Build | ${INCREMENTAL_BUILD_TIME}s | ≤ 60s | $INCREMENTAL_STATUS |" >> performance-report.md

    - name: Upload Performance Report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.md

    - name: Comment Performance Results
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('performance-report.md', 'utf8');

          const { data: releases } = await github.rest.repos.listReleases({
            owner: context.repo.owner,
            repo: context.repo.repo,
            per_page: 1
          });

          if (releases.length > 0) {
            await github.rest.repos.createReleaseComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: releases[0].id,
              body: report
            });
          }

  app-performance:
    name: App Performance Benchmarking
    runs-on: ubuntu-latest
    timeout-minutes: 60

    strategy:
      matrix:
        api-level: [28, 33]

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Enable KVM
      run: |
        echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
        sudo udevadm control --reload-rules
        sudo udevadm trigger --name-match=kvm

    - name: AVD Cache
      uses: actions/cache@v3
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: perf-avd-${{ matrix.api-level }}

    - name: Run Performance Benchmarks
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        target: google_apis
        arch: x86_64
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: |
          ./gradlew :benchmark:connectedBenchmarkAndroidTest
          adb pull /storage/emulated/0/Android/data/com.culturestack.android.benchmark/files/benchmark-reports/ ./benchmark-reports/

    - name: Upload Benchmark Results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results-api${{ matrix.api-level }}
        path: benchmark-reports/

  memory-analysis:
    name: Memory Leak Detection
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
    - name: Checkout Code
      uses: actions/checkout@v4

    - name: Setup JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Setup Gradle Cache
      uses: gradle/gradle-build-action@v2

    - name: Run Memory Leak Detection
      run: |
        ./gradlew :app:testDebugUnitTest -Dtest.single=*MemoryLeakTest*

    - name: Run Static Memory Analysis
      run: |
        ./gradlew :app:lintDebug

    - name: Upload Memory Analysis Reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: memory-analysis-reports
        path: |
          app/build/reports/lint-results-debug.html
          app/build/test-results/testDebugUnitTest/
```

---

## **Workflow Optimization Strategies**

### **Caching Strategy**

#### **Gradle Build Cache**
```yaml
- name: Setup Gradle Cache
  uses: gradle/gradle-build-action@v2
  with:
    gradle-version: wrapper
    cache-read-only: ${{ github.ref != 'refs/heads/main' }}
```

#### **Android AVD Cache**
```yaml
- name: AVD Cache
  uses: actions/cache@v3
  id: avd-cache
  with:
    path: |
      ~/.android/avd/*
      ~/.android/adb*
    key: avd-${{ matrix.api-level }}-${{ matrix.target }}-${{ hashFiles('**/avd-config.ini') }}
    restore-keys: |
      avd-${{ matrix.api-level }}-${{ matrix.target }}
      avd-${{ matrix.api-level }}
```

### **Parallel Execution**

#### **Job Dependencies Optimization**
```yaml
# Efficient parallel execution
jobs:
  code-quality:
    # Fast feedback - runs first
  unit-tests:
    needs: code-quality
    # Parallel with build-validation
  build-validation:
    needs: code-quality
    # Parallel with unit-tests
  integration-tests:
    needs: unit-tests
    # Long-running tests run after fast checks
```

#### **Matrix Strategy for Testing**
```yaml
strategy:
  fail-fast: false  # Continue other matrix jobs on failure
  matrix:
    api-level: [24, 30, 34]
    include:
      - api-level: 24
        target: google_apis
      - api-level: 30
        target: google_apis
      - api-level: 34
        target: google_apis_playstore
```

---

## **Error Handling & Recovery**

### **Failure Notifications**
```yaml
- name: Notify Build Failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    channel: '#culturestack-alerts'
    text: |
      🚨 Build failed: ${{ github.workflow }}
      Branch: ${{ github.ref }}
      Commit: ${{ github.sha }}
      Author: ${{ github.actor }}
      Job: ${{ github.job }}
    webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### **Retry Logic**
```yaml
- name: Run Flaky Test with Retry
  uses: nick-invision/retry@v2
  with:
    timeout_minutes: 10
    max_attempts: 3
    retry_on: error
    command: ./gradlew connectedDebugAndroidTest
```

### **Cleanup on Failure**
```yaml
- name: Clean up Keystore
  if: always()
  run: |
    rm -f release.keystore
    rm -f service-account.json
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After workflow implementation and testing