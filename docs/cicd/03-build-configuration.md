# Build Configuration

## Overview
This document provides comprehensive build configuration for CultureStack Android application, including Gradle scripts, build variants, dependency management, and optimization strategies.

---

## **Gradle Build Scripts**

### **Root Project Configuration**

**File:** `build.gradle.kts` (Root)

```kotlin
plugins {
    id("com.android.application") version "8.2.0" apply false
    id("com.android.library") version "8.2.0" apply false
    id("org.jetbrains.kotlin.android") version "1.9.20" apply false
    id("com.google.dagger.hilt.android") version "2.48" apply false
    id("com.google.gms.google-services") version "4.4.0" apply false
    id("com.google.firebase.crashlytics") version "2.9.9" apply false
    id("androidx.navigation.safeargs.kotlin") version "2.7.5" apply false
    id("org.jetbrains.kotlin.plugin.serialization") version "1.9.20" apply false
}

tasks.register("clean", Delete::class) {
    delete(rootProject.buildDir)
}

// Build scan configuration
gradle.settingsEvaluated {
    if (System.getenv("CI") == "true") {
        buildScan {
            termsOfServiceUrl = "https://gradle.com/terms-of-service"
            termsOfServiceAgree = "yes"
            tag("CI")
        }
    }
}
```

**File:** `gradle.properties`

```properties
# Gradle Configuration
org.gradle.jvmargs=-Xmx4g -XX:+UseParallelGC -XX:+HeapDumpOnOutOfMemoryError
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configuration-cache=true
org.gradle.configureondemand=true

# Android Configuration
android.useAndroidX=true
android.enableJetifier=true
android.nonTransitiveRClass=true
android.enableR8.fullMode=true

# Kotlin Configuration
kotlin.code.style=official
kotlin.incremental.useClasspathSnapshot=true

# Build Optimization
android.experimental.enableSourceSetPathsMap=true
android.experimental.cacheCompileLibResources=true
```

### **App Module Configuration**

**File:** `app/build.gradle.kts`

```kotlin
plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("dagger.hilt.android.plugin")
    id("kotlin-kapt")
    id("kotlin-parcelize")
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    id("androidx.navigation.safeargs.kotlin")
    id("kotlinx-serialization")
}

android {
    namespace = "com.culturestack.android"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.culturestack.android"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()

        versionCode = System.getenv("VERSION_CODE")?.toIntOrNull() ?: 1
        versionName = System.getenv("VERSION_NAME") ?: "1.0.0-dev"

        testInstrumentationRunner = "com.culturestack.android.HiltTestRunner"
        vectorDrawables.useSupportLibrary = true

        // Room schema export
        kapt {
            arguments {
                arg("room.schemaLocation", "$projectDir/schemas")
            }
        }

        // ProGuard/R8 optimization
        proguardFiles(
            getDefaultProguardFile("proguard-android-optimize.txt"),
            "proguard-rules.pro"
        )
    }

    signingConfigs {
        create("release") {
            storeFile = file(System.getenv("RELEASE_KEYSTORE_PATH") ?: "release.keystore")
            storePassword = System.getenv("RELEASE_STORE_PASSWORD")
            keyAlias = System.getenv("RELEASE_KEY_ALIAS")
            keyPassword = System.getenv("RELEASE_KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            isMinifyEnabled = false
            isShrinkResources = false
            applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"

            buildConfigField("String", "API_BASE_URL", "\"https://api-dev.culturestack.com\"")
            buildConfigField("boolean", "ENABLE_CRASHLYTICS", "false")
            buildConfigField("boolean", "ENABLE_STRICT_MODE", "true")

            // Enable code coverage for debug builds
            enableUnitTestCoverage = true
            enableAndroidTestCoverage = true
        }

        release {
            isDebuggable = false
            isMinifyEnabled = true
            isShrinkResources = true
            signingConfig = signingConfigs.getByName("release")

            buildConfigField("String", "API_BASE_URL", "\"https://api.culturestack.com\"")
            buildConfigField("boolean", "ENABLE_CRASHLYTICS", "true")
            buildConfigField("boolean", "ENABLE_STRICT_MODE", "false")
        }

        create("beta") {
            initWith(getByName("release"))
            applicationIdSuffix = ".beta"
            versionNameSuffix = "-beta"
            buildConfigField("String", "API_BASE_URL", "\"https://api-beta.culturestack.com\"")

            // Enable debugging for beta testing
            isDebuggable = true
        }

        create("benchmark") {
            initWith(getByName("release"))
            signingConfig = signingConfigs.getByName("debug")
            matchingFallbacks += listOf("release")
            isDebuggable = false
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
        freeCompilerArgs += listOf(
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
            "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi"
        )
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = libs.versions.compose.compiler.get()
    }

    testOptions {
        unitTests {
            isIncludeAndroidResources = true
            isReturnDefaultValues = true
        }
        animationsDisabled = true
        execution = "ANDROIDX_TEST_ORCHESTRATOR"
    }

    packaging {
        resources {
            excludes += setOf(
                "/META-INF/{AL2.0,LGPL2.1}",
                "/META-INF/gradle/incremental.annotation.processors",
                "/META-INF/DEPENDENCIES",
                "/META-INF/LICENSE",
                "/META-INF/LICENSE.txt",
                "/META-INF/license.txt",
                "/META-INF/NOTICE",
                "/META-INF/NOTICE.txt",
                "/META-INF/notice.txt",
                "/META-INF/ASL2.0",
                "/META-INF/*.kotlin_module"
            )
        }
    }

    lint {
        abortOnError = false
        checkReleaseBuilds = false
        disable += setOf("MissingTranslation", "VectorPath")
    }
}

dependencies {
    // Core Android
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.activity.compose)

    // Compose BOM and UI
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.compose.ui)
    implementation(libs.androidx.compose.ui.tooling.preview)
    implementation(libs.androidx.compose.material3)
    implementation(libs.androidx.compose.material.icons.extended)

    // Navigation
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)

    // Dependency Injection
    implementation(libs.dagger.hilt.android)
    kapt(libs.dagger.hilt.compiler)

    // Database
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    kapt(libs.androidx.room.compiler)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.kotlinx.serialization)
    implementation(libs.okhttp.logging.interceptor)
    implementation(libs.kotlinx.serialization.json)

    // Image Loading
    implementation(libs.coil.compose)

    // Coroutines
    implementation(libs.kotlinx.coroutines.android)

    // Date/Time
    coreLibraryDesugaring(libs.android.desugarJdkLibs)
    implementation(libs.kotlinx.datetime)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.crashlytics.ktx)
    implementation(libs.firebase.performance.ktx)

    // Testing - Unit
    testImplementation(libs.junit)
    testImplementation(libs.kotlinx.coroutines.test)
    testImplementation(libs.androidx.arch.core.testing)
    testImplementation(libs.mockito.kotlin)
    testImplementation(libs.google.truth)
    testImplementation(libs.turbine)
    testImplementation(libs.robolectric)

    // Testing - Android
    androidTestImplementation(libs.androidx.test.ext.junit)
    androidTestImplementation(libs.androidx.test.espresso.core)
    androidTestImplementation(libs.androidx.compose.ui.test.junit4)
    androidTestImplementation(libs.dagger.hilt.android.testing)
    androidTestImplementation(libs.androidx.test.orchestrator)
    kaptAndroidTest(libs.dagger.hilt.android.compiler)

    // Debug
    debugImplementation(libs.androidx.compose.ui.tooling)
    debugImplementation(libs.androidx.compose.ui.test.manifest)
    debugImplementation(libs.leakcanary.android)
}

// Allow references to generated code
kapt {
    correctErrorTypes = true
}
```

---

## **Dependency Management**

### **Version Catalog Configuration**

**File:** `gradle/libs.versions.toml`

```toml
[versions]
# SDK Versions
compileSdk = "34"
minSdk = "24"
targetSdk = "34"

# Core Versions
kotlin = "1.9.20"
kotlinx-coroutines = "1.7.3"
kotlinx-serialization = "1.6.0"
kotlinx-datetime = "0.4.1"

# Android Versions
androidx-core = "1.12.0"
androidx-lifecycle = "2.7.0"
androidx-activity = "1.8.2"
androidx-navigation = "2.7.5"
androidx-room = "2.6.1"
androidx-test = "1.5.0"

# Compose
compose-bom = "2023.10.01"
compose-compiler = "1.5.5"

# Dependency Injection
hilt = "2.48"
hilt-navigation-compose = "1.1.0"

# Network
retrofit = "2.9.0"
okhttp = "4.12.0"

# Image Loading
coil = "2.5.0"

# Firebase
firebase-bom = "32.7.0"

# Testing
junit = "4.13.2"
mockito = "5.7.0"
truth = "1.1.5"
turbine = "1.0.0"
robolectric = "4.11.1"

# Tools
android-desugarJdkLibs = "2.0.4"
leakcanary = "2.12"

[libraries]
# Core Android
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "androidx-core" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "androidx-lifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "androidx-activity" }

# Compose
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose-bom" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }

# Navigation
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "androidx-navigation" }
androidx-hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hilt-navigation-compose" }

# Dependency Injection
dagger-hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
dagger-hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }

# Database
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "androidx-room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "androidx-room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "androidx-room" }

# Network
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-kotlinx-serialization = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version = "1.0.0" }
okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinx-serialization" }

# Coroutines
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "kotlinx-coroutines" }
kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "kotlinx-coroutines" }

# Image Loading
coil-compose = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }

# Date/Time
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinx-datetime" }
android-desugarJdkLibs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "android-desugarJdkLibs" }

# Firebase
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics-ktx = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-crashlytics-ktx = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-performance-ktx = { group = "com.google.firebase", name = "firebase-perf-ktx" }

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-arch-core-testing = { group = "androidx.arch.core", name = "core-testing", version = "2.2.0" }
mockito-kotlin = { group = "org.mockito.kotlin", name = "mockito-kotlin", version.ref = "mockito" }
google-truth = { group = "com.google.truth", name = "truth", version.ref = "truth" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }
robolectric = { group = "org.robolectric", name = "robolectric", version.ref = "robolectric" }

# Android Testing
androidx-test-ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test" }
androidx-test-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version = "3.5.1" }
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-test-orchestrator = { group = "androidx.test", name = "orchestrator", version = "1.4.2" }
dagger-hilt-android-testing = { group = "com.google.dagger", name = "hilt-android-testing", version.ref = "hilt" }
dagger-hilt-android-compiler = { group = "com.google.dagger", name = "hilt-android-compiler", version.ref = "hilt" }

# Debug Tools
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
leakcanary-android = { group = "com.squareup.leakcanary", name = "leakcanary-android", version.ref = "leakcanary" }

[plugins]
android-application = { id = "com.android.application", version = "8.2.0" }
android-library = { id = "com.android.library", version = "8.2.0" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
```

---

## **Code Coverage Configuration**

### **JaCoCo Integration**

```kotlin
// Code coverage configuration in app/build.gradle.kts
apply(plugin = "jacoco")

jacoco {
    toolVersion = "0.8.8"
}

android {
    buildTypes {
        debug {
            enableUnitTestCoverage = true
            enableAndroidTestCoverage = true
        }
    }

    testOptions {
        unitTests.all { test ->
            test.useJUnitPlatform()
            test.testLogging {
                events = setOf(
                    org.gradle.api.tasks.testing.logging.TestLogEvent.PASSED,
                    org.gradle.api.tasks.testing.logging.TestLogEvent.SKIPPED,
                    org.gradle.api.tasks.testing.TestLogEvent.FAILED
                )
            }
        }
    }
}

tasks.register<JacocoReport>("jacocoTestReport") {
    dependsOn("testDebugUnitTest", "createDebugCoverageReport")

    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }

    val fileFilter = listOf(
        "**/R.class",
        "**/R$*.class",
        "**/BuildConfig.*",
        "**/Manifest*.*",
        "**/*Test*.*",
        "android/**/*.*",
        "**/databinding/*",
        "**/di/*",
        "**/*Hilt*.*",
        "**/*_Factory.*",
        "**/*_MembersInjector.*"
    )

    val debugTree = fileTree("${buildDir}/tmp/kotlin-classes/debug") {
        exclude(fileFilter)
    }
    val mainSrc = "${project.projectDir}/src/main/java"

    sourceDirectories.setFrom(files(mainSrc))
    classDirectories.setFrom(files(debugTree))
    executionData.setFrom(fileTree(buildDir) {
        include("jacoco/testDebugUnitTest.exec", "outputs/code_coverage/debugAndroidTest/connected/coverage.ec")
    })
}

tasks.register("jacocoTestCoverageVerification", JacocoCoverageVerification::class) {
    dependsOn("jacocoTestReport")

    violationRules {
        rule {
            limit {
                minimum = "0.80".toBigDecimal()
            }
        }

        rule {
            element = "CLASS"
            limit {
                counter = "BRANCH"
                value = "COVEREDRATIO"
                minimum = "0.70".toBigDecimal()
            }
            excludes = listOf(
                "*.databinding.*",
                "*.di.*",
                "*Test*",
                "*Hilt*"
            )
        }
    }
}
```

---

## **ProGuard/R8 Configuration**

### **ProGuard Rules**

**File:** `app/proguard-rules.pro`

```proguard
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep AndroidX and Google libraries
-keep class androidx.** { *; }
-keep class com.google.** { *; }

# Keep Kotlin metadata
-keep class kotlin.Metadata { *; }

# Keep Retrofit interfaces
-keep interface retrofit2.** { *; }
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# Keep Kotlinx Serialization
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.SerializationKt
-keep,includedescriptorclasses class com.culturestack.**$$serializer { *; }
-keepclassmembers class com.culturestack.** {
    *** Companion;
}
-keepclasseswithmembers class com.culturestack.** {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep Room database classes
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-keep @androidx.room.Dao class *

# Keep Hilt generated classes
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.HiltAndroidApp
-keep @dagger.hilt.android.AndroidEntryPoint class * { *; }

# Keep custom data classes
-keep @kotlinx.serialization.Serializable class ** { *; }
-keep class com.culturestack.android.data.** { *; }
-keep class com.culturestack.android.domain.** { *; }

# Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# Coil image loading
-keep class coil.** { *; }

# OkHttp
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# Kotlin Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Crashlytics
-keepattributes SourceFile,LineNumberTable
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**
```

---

## **Build Performance Optimization**

### **Gradle Performance Settings**

**Additional `gradle.properties` optimizations:**

```properties
# Build Performance
kotlin.incremental=true
kotlin.caching.enabled=true
kotlin.incremental.multiplatform=true
kotlin.incremental.useClasspathSnapshot=true

# Compiler Settings
kotlin.compiler.execution.strategy=in-process
kapt.incremental.apt=true
kapt.use.worker.api=true

# Dex Settings
android.enableD8.desugaring=true
android.enableR8=true

# Build Cache
org.gradle.unsafe.configuration-cache=true
org.gradle.unsafe.configuration-cache-problems=warn

# Memory Settings for large projects
org.gradle.daemon=true
org.gradle.jvmargs=-Xmx6g -XX:+UseG1GC -XX:+UseStringDeduplication
```

### **Custom Gradle Tasks**

```kotlin
// Custom build tasks in app/build.gradle.kts
tasks.register("assembleAndTest") {
    description = "Builds and runs all tests"
    group = "verification"
    dependsOn("assembleDebug", "testDebugUnitTest", "connectedDebugAndroidTest")
}

tasks.register("fullClean") {
    description = "Deep clean including .gradle cache"
    group = "build"
    doLast {
        delete(".gradle")
        delete("build")
        delete("app/build")
    }
}

tasks.register("generateReleaseReports") {
    description = "Generate all release reports"
    group = "reporting"
    dependsOn(
        "lintRelease",
        "jacocoTestReport",
        "testReleaseUnitTest"
    )
}

// Automatically run tests after successful build
tasks.named("assembleDebug") {
    finalizedBy("testDebugUnitTest")
}

// Check code coverage after tests
tasks.named("testDebugUnitTest") {
    finalizedBy("jacocoTestReport", "jacocoTestCoverageVerification")
}
```

---

## **Build Variants and Flavors**

### **Product Flavors Configuration**

```kotlin
android {
    flavorDimensions += "environment"

    productFlavors {
        create("dev") {
            dimension = "environment"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
            buildConfigField("String", "API_BASE_URL", "\"https://api-dev.culturestack.com\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "true")
            resValue("string", "app_name", "CultureStack Dev")
        }

        create("staging") {
            dimension = "environment"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
            buildConfigField("String", "API_BASE_URL", "\"https://api-staging.culturestack.com\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "true")
            resValue("string", "app_name", "CultureStack Staging")
        }

        create("prod") {
            dimension = "environment"
            // No suffix for production
            buildConfigField("String", "API_BASE_URL", "\"https://api.culturestack.com\"")
            buildConfigField("boolean", "ENABLE_LOGGING", "false")
            resValue("string", "app_name", "CultureStack")
        }
    }

    // Variant-specific configuration
    applicationVariants.all { variant ->
        val flavorName = variant.flavorName
        val buildType = variant.buildType.name

        variant.outputs.all { output ->
            val outputImpl = output as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            val fileName = "CultureStack-${flavorName}-${buildType}-v${variant.versionName}.apk"
            outputImpl.outputFileName = fileName
        }

        // Custom build config fields per variant
        when (flavorName) {
            "dev" -> {
                buildConfigField("int", "DATABASE_VERSION", "1")
                buildConfigField("boolean", "ENABLE_DEBUG_TOOLS", "true")
            }
            "staging" -> {
                buildConfigField("int", "DATABASE_VERSION", "1")
                buildConfigField("boolean", "ENABLE_DEBUG_TOOLS", "false")
            }
            "prod" -> {
                buildConfigField("int", "DATABASE_VERSION", "1")
                buildConfigField("boolean", "ENABLE_DEBUG_TOOLS", "false")
            }
        }
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After build configuration testing and optimization