# CultureStack Development Environment Setup

## Prerequisites & System Requirements

### **Required Software**
| Tool | Version | Platform | Installation Source |
|------|---------|----------|-------------------|
| **Android Studio** | Giraffe 2022.3.1+ | Windows/macOS/Linux | [developer.android.com](https://developer.android.com/studio) |
| **JDK** | 17 (Eclipse Temurin) | All platforms | [adoptium.net](https://adoptium.net/) |
| **Git** | 2.40+ | All platforms | [git-scm.com](https://git-scm.com/) |
| **Node.js** | 18 LTS+ | All platforms | [nodejs.org](https://nodejs.org/) (for documentation) |

### **Android SDK Requirements**
- **Target SDK:** API 34 (Android 14)
- **Minimum SDK:** API 24 (Android 7.0)
- **Build Tools:** 34.0.0+
- **NDK:** Not required for this project

### **Hardware Requirements**
- **RAM:** 16GB+ recommended (8GB minimum)
- **Storage:** 50GB+ free space
- **CPU:** Intel i5/AMD Ryzen 5 or better
- **Android Device:** Physical device or emulator with API 24+

---

## **Step 1: Development Environment Setup**

### **1.1 Install Android Studio**

**Windows:**
```powershell
# Download from developer.android.com/studio
# Run android-studio-ide-setup.exe as administrator
# Choose "Standard" installation type
# Accept all SDK component licenses
```

**macOS:**
```bash
# Download from developer.android.com/studio
# Drag Android Studio.app to Applications folder
# Run Android Studio and follow setup wizard
```

**Linux (Ubuntu/Debian):**
```bash
# Download android-studio-ide.tar.gz
sudo tar -xzf android-studio-ide.tar.gz -C /opt/
sudo ln -sf /opt/android-studio/bin/studio.sh /usr/local/bin/android-studio
android-studio
```

### **1.2 Configure Android Studio**

1. **Install Required SDKs:**
   - Open SDK Manager (Tools → SDK Manager)
   - Install Android API 24, 30, 33, 34
   - Install Google Play services, Google Repository
   - Install Android SDK Build-Tools 34.0.0+

2. **Install Required Plugins:**
   - Kotlin Multiplatform Mobile (pre-installed)
   - Android Kotlin Extensions (pre-installed)
   - Database Inspector (pre-installed)

3. **Configure VM Options:**
```bash
# In Android Studio: Help → Edit Custom VM Options
-Xmx4096m
-Xms1024m
-XX:ReservedCodeCacheSize=512m
-XX:+UseConcMarkSweepGC
-XX:SoftRefLRUPolicyMSPerMB=50
```

### **1.3 Setup Android Virtual Device (AVD)**

```bash
# Create AVD via Android Studio:
# Tools → AVD Manager → Create Virtual Device
# Recommended configurations:
```

| Device | API Level | System Image | RAM | Storage |
|--------|-----------|--------------|-----|---------|
| **Pixel 7** | API 34 | Google APIs | 4096MB | 8192MB |
| **Pixel 4** | API 30 | Google Play | 2048MB | 4096MB |
| **Nexus 5X** | API 24 | Google APIs | 1536MB | 2048MB |

---

## **Step 2: Project Setup & Clone**

### **2.1 Repository Setup**
```bash
# Clone the repository
git clone https://github.com/your-org/culturestack-android.git
cd culturestack-android

# Verify Git configuration
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Setup Git hooks
cp .githooks/* .git/hooks/
chmod +x .git/hooks/*
```

### **2.2 Project Structure Verification**
```bash
culturestack-android/
├── app/                    # Main application module
│   ├── build.gradle.kts
│   └── src/
├── core/                   # Core domain logic
│   ├── database/          # Room database module
│   ├── network/           # Network layer
│   └── common/            # Shared utilities
├── feature/               # Feature modules
│   ├── cultures/          # Culture management
│   ├── recipes/           # Recipe management
│   └── auth/              # Authentication
├── gradle/
│   └── libs.versions.toml # Version catalog
├── build.gradle.kts       # Project build script
└── README.md
```

### **2.3 Gradle Configuration**

**Verify gradle/libs.versions.toml:**
```toml
[versions]
kotlin = "1.9.10"
androidGradlePlugin = "8.1.2"
targetSdk = "34"
minSdk = "24"
compileSdk = "34"

compose = "1.5.4"
composeBom = "2023.10.01"
room = "2.5.0"
hilt = "2.48"
retrofit = "2.9.0"
coil = "2.4.0"

[libraries]
# Core Android
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version = "1.12.0" }
androidx-lifecycle-runtime = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version = "2.7.0" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version = "1.8.0" }

# Compose BOM
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-compose-ui = { group = "androidx.compose", name = "ui" }
androidx-compose-ui-tooling = { group = "androidx.compose", name = "ui-tooling" }
androidx-compose-ui-test = { group = "androidx.compose", name = "ui-test-junit4" }
androidx-compose-material3 = { group = "androidx.compose", name = "material3" }

# Room Database
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

# Hilt DI
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version = "1.1.0" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
android-library = { id = "com.android.library", version.ref = "androidGradlePlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
```

---

## **Step 3: Dependency Installation**

### **3.1 Gradle Dependencies**
```bash
# Clean and build project
./gradlew clean
./gradlew build --info

# Verify all dependencies resolve
./gradlew dependencies --configuration debugRuntimeClasspath
```

### **3.2 Google Services Setup**

1. **Create Firebase Project:**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create new project: "CultureStack"
   - Enable Analytics, Authentication, Cloud Messaging

2. **Download Configuration:**
   - Add Android app to Firebase project
   - Package name: `com.culturestack.android`
   - Download `google-services.json`
   - Place in `app/` directory

3. **Google Drive API Setup:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Enable Google Drive API v3
   - Create OAuth 2.0 credentials
   - Add SHA-1 fingerprints for debug/release

### **3.3 Environment Configuration**

**Create local.properties:**
```properties
# Android SDK location
sdk.dir=/path/to/Android/Sdk

# Google Services
GOOGLE_DRIVE_CLIENT_ID=your_client_id.googleusercontent.com
GOOGLE_SERVICES_API_KEY=your_api_key

# Debug keystore (for development)
DEBUG_KEYSTORE_PATH=~/.android/debug.keystore
DEBUG_KEY_ALIAS=androiddebugkey
DEBUG_KEY_PASSWORD=android
DEBUG_STORE_PASSWORD=android

# Release keystore (for production builds)
RELEASE_KEYSTORE_PATH=/path/to/release.keystore
RELEASE_KEY_ALIAS=release
RELEASE_KEY_PASSWORD=secure_password
RELEASE_STORE_PASSWORD=secure_password
```

---

## **Step 4: Database & Storage Setup**

### **4.1 SQLite Database Initialization**
```kotlin
// Database will be auto-created on first run
// Location: /data/data/com.culturestack.android/databases/
// No manual setup required - handled by Room
```

### **4.2 Test Database Creation**
```bash
# Run database tests to verify setup
./gradlew :core:database:testDebugUnitTest --tests="*DatabaseTest*"

# Verify database creation in emulator
adb shell
cd /data/data/com.culturestack.android/databases/
ls -la
```

### **4.3 Sample Data Generation**
```bash
# Run app in debug mode to trigger sample data creation
./gradlew assembleDebug
./gradlew installDebug

# Or run database populate task
./gradlew :app:populateSampleData
```

---

## **Step 5: Development Tools Configuration**

### **5.1 Code Quality Tools**

**Install ktlint:**
```bash
# Add to gradle (already in project)
./gradlew ktlintCheck
./gradlew ktlintFormat
```

**Install detekt:**
```bash
# Static analysis tool (already configured)
./gradlew detekt
```

### **5.2 Debugging Tools**

**Database Inspector Setup:**
- Open Android Studio
- Run app on emulator/device
- View → Tool Windows → Database Inspector
- Select running app process

**Layout Inspector:**
- Tools → Layout Inspector
- Connect to running app
- Inspect UI hierarchy and properties

### **5.3 Testing Tools**

**Unit Test Setup:**
```bash
# Run all unit tests
./gradlew testDebugUnitTest

# Run specific test
./gradlew testDebugUnitTest --tests="*CultureRepositoryTest*"
```

**UI Test Setup:**
```bash
# Start emulator first
./gradlew connectedDebugAndroidTest

# Run specific UI test
./gradlew connectedDebugAndroidTest --tests="*CultureCreationTest*"
```

---

## **Step 6: Verification & Validation**

### **6.1 Build Verification**
```bash
# Clean build
./gradlew clean build

# Expected output:
# BUILD SUCCESSFUL in 2m 15s
# 156 actionable tasks: 156 executed

# Check for warnings
./gradlew build --warning-mode all
```

### **6.2 App Launch Verification**
```bash
# Install and launch debug app
./gradlew installDebug
adb shell am start -n com.culturestack.android/.MainActivity

# Verify logs
adb logcat -s CultureStack
```

### **6.3 Feature Testing Checklist**
- [ ] App launches successfully
- [ ] Database creates and populates
- [ ] Google Sign-In flow works
- [ ] Photo capture functions
- [ ] Navigation works across screens
- [ ] No crashes on basic operations

---

## **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **Issue: Gradle Sync Failed**
```bash
# Solution 1: Clean project
./gradlew clean
# Delete .gradle/ folder
rm -rf .gradle/
./gradlew build

# Solution 2: Invalidate caches
# Android Studio: File → Invalidate Caches and Restart
```

#### **Issue: Google Services Not Working**
```bash
# Verify google-services.json is in app/ directory
ls -la app/google-services.json

# Check package name matches Firebase configuration
grep "package_name" app/google-services.json
```

#### **Issue: Database Creation Failed**
```bash
# Check storage permissions
adb shell pm grant com.culturestack.android android.permission.WRITE_EXTERNAL_STORAGE

# Clear app data
adb shell pm clear com.culturestack.android
```

#### **Issue: Emulator Performance Issues**
```bash
# Increase AVD RAM allocation
# AVD Manager → Edit AVD → Advanced Settings → RAM: 4096MB

# Enable hardware acceleration
# Ensure Intel HAXM (Windows) or Hypervisor.framework (macOS) is installed
```

#### **Issue: Build Performance Slow**
```bash
# Add to gradle.properties
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.jvmargs=-Xmx4096m -XX:+UseParallelGC
```

---

## **Development Workflow**

### **Daily Development Process**
1. **Start of Day:**
   ```bash
   git pull origin develop
   ./gradlew clean build
   ```

2. **Feature Development:**
   ```bash
   git checkout -b feature/your-feature-name
   # Develop and test feature
   ./gradlew testDebugUnitTest
   git commit -m "feat: implement feature description"
   ```

3. **Before Push:**
   ```bash
   ./gradlew ktlintCheck detekt
   ./gradlew testDebugUnitTest
   git push origin feature/your-feature-name
   ```

### **Code Review Process**
- Create Pull Request with detailed description
- Ensure CI checks pass (automated)
- Request review from team member
- Address feedback and merge

### **Release Process**
```bash
# Create release branch
git checkout -b release/v1.0.0

# Update version numbers
# Increment versionCode and versionName in app/build.gradle.kts

# Build release candidate
./gradlew assembleRelease

# Upload to Play Console (Internal Testing)
```

---

## **Performance Monitoring Setup**

### **Development Metrics**
- **Build Time Target:** < 2 minutes clean build
- **App Launch Time:** < 3 seconds cold start
- **Memory Usage:** < 150MB baseline
- **Battery Impact:** Minimal background usage

### **Monitoring Tools**
- Android Studio Profiler
- Firebase Performance Monitoring
- Google Play Console vitals

---

## **Security Configuration**

### **Debug vs Release Configuration**

**Debug Build:**
- Uses debug keystore
- Allows HTTP traffic
- Enables debugging and logging
- No code obfuscation

**Release Build:**
- Uses release keystore from secure location
- HTTPS only
- Minimal logging
- Code obfuscation enabled
- Google Play signing

### **Sensitive Data Management**
- API keys in local.properties (not in VCS)
- Production secrets in environment variables
- No hardcoded credentials in source code
- Proper certificate pinning for production

---

This development setup guide provides a complete foundation for CultureStack Android development, covering all aspects from initial environment setup through production deployment preparation.