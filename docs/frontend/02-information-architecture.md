# Information Architecture & Navigation

## Purpose
This document defines the information architecture, site map, and navigation structure for CultureStack, ensuring logical content organization and intuitive user flows.

**Dependencies:** [01-overview.md](./01-overview.md)
**Referenced By:** [03-user-flows.md](./03-user-flows.md), [04-component-library.md](./04-component-library.md)

## Information Architecture (IA)

### Site Map / Screen Inventory

```mermaid
graph TD
    A[Launch Screen] --> B[Dashboard]
    A --> C[Onboarding Flow]

    B[Dashboard] --> B1[Active Cultures Overview]
    B --> B2[Recent Activity Feed]
    B --> B3[Next Actions Required]
    B --> B4[Quick Stats]

    B --> D[Cultures]
    D --> D1[Culture List/Grid]
    D --> D2[Create New Culture]
    D --> D3[Culture Detail]
    D3 --> D3a[Progress Timeline]
    D3 --> D3b[Photos & Notes]
    D3 --> D3c[Protocol Steps]
    D3 --> D3d[Environmental Data]

    B --> E[Protocols]
    E --> E1[Protocol Library]
    E --> E2[My Protocols]
    E --> E3[Community Protocols]
    E --> E4[Protocol Detail/Editor]

    B --> F[Equipment]
    F --> F1[Equipment Inventory]
    F --> F2[Maintenance Schedules]
    F --> F3[Equipment History]

    B --> G[Learning]
    G --> G1[Tutorial Library]
    G --> G2[Knowledge Base]
    G --> G3[Troubleshooting Guide]
    G --> G4[Community Forum/Q&A]

    B --> H[Profile]
    H --> H1[Account Settings]
    H --> H2[Data Export/Backup]
    H --> H3[Notifications]
    H --> H4[App Preferences]
```

## Navigation Structure

### Primary Navigation
**Implementation:** Bottom tab bar with 5 core sections (Dashboard, Cultures, Protocols, Learning, Profile)

**Rationale:** Bottom tabs provide thumb-friendly access to main sections, following Android Material Design guidelines for primary navigation.

### Secondary Navigation
**Implementation:** Context-sensitive top navigation within each section, utilizing back buttons and breadcrumbs for deep navigation paths. Enhanced with contextual shortcuts in Culture Detail screens for quick access to troubleshooting and related protocols.

**Hierarchy Examples:**
- Dashboard → Culture Card → Culture Detail → Progress Timeline
- Protocols → Protocol Detail → Protocol Editor → Step Editor
- Learning → Troubleshooting Guide → Specific Problem → Related Protocols

### Breadcrumb Strategy
**Hierarchical breadcrumbs** for multi-level sections (e.g., Cultures > Culture Detail > Progress Timeline), with tab persistence to maintain user's place when switching between sections.

**Implementation Notes:**
- Breadcrumbs appear in header for navigation depth > 2 levels
- Current section always highlighted in bottom tabs
- Back button behavior respects user's navigation history

### Cross-Section Integration
**Floating action buttons** and contextual bridges connecting related content across sections:
- Protocols accessible from culture work contexts
- Learning resources from troubleshooting scenarios
- Equipment logs from culture environmental data
- Community discussions from protocol usage

## Content Prioritization

### Information Hierarchy
1. **Critical Actions** - Time-sensitive culture maintenance tasks
2. **Active Monitoring** - Current culture status and health indicators
3. **Planning & Setup** - Protocol selection and culture creation
4. **Learning & Research** - Educational content and community resources
5. **Management & Settings** - Profile, equipment, and system preferences

### Screen Real Estate Allocation
- **Dashboard**: 40% active cultures, 30% next actions, 20% quick stats, 10% navigation
- **Culture Detail**: 50% current status, 25% timeline/progress, 15% actions, 10% navigation
- **Protocol Views**: 60% step content, 25% progress indicators, 15% navigation/context

## Search & Discovery

### Search Strategy
- **Global Search**: Cross-section search from any screen
- **Contextual Search**: Section-specific filtering and search
- **Smart Suggestions**: Based on culture stage, history, and common patterns

### Content Tagging
Systematic tagging strategy for protocols, learning content, and community contributions to enable intelligent content discovery and personalized recommendations.

---

**Related Documents:**
- [← UX Overview](./01-overview.md)
- [User Flows →](./03-user-flows.md)
- [Component Library →](./04-component-library.md)
- [Complete Frontend Index →](./index.md)