# Component Library & Design System

## Purpose
This document defines the complete design system, component library, and brand guidelines for CultureStack, ensuring consistent visual design and interaction patterns across the application.

**Dependencies:** [01-overview.md](./01-overview.md), [02-information-architecture.md](./02-information-architecture.md), [03-user-flows.md](./03-user-flows.md)
**Referenced By:** [05-accessibility.md](./05-accessibility.md), [06-responsive-design.md](./06-responsive-design.md)

## Design System Approach

**Philosophy:** Create a custom design system tailored for scientific/lab applications with emphasis on data clarity, status indication, and touch-friendly interactions for sterile environment use.

**Foundation:** Built on Material Design principles with scientific workflow enhancements and tissue culture domain-specific components.

## Core Components

### Culture Status Card
**Purpose:** Display culture health, progress, and required actions at-a-glance

**Variants:**
- **List View (Compact):** Single line with status indicator, name, and next action
- **Grid View (Detailed):** Card format with photo, status, progress bar, and actions
- **Dashboard Widget:** Summary format with key metrics and alerts

**States:**
- **Healthy:** Green status indicator, normal progress display
- **Alert:** Yellow/orange indicator with specific alert message
- **Critical:** Red indicator with urgent action required
- **Dormant:** Gray indicator for inactive/completed cultures
- **Completed:** Success indicator with completion date

**Usage Guidelines:**
- Always include visual status indicator with color and icon
- Display next action date prominently for active cultures
- Provide quick access to detail view via tap
- Support long-press for contextual actions (edit, archive, share)

**Component Specification:**
```
Culture Status Card {
  - Status indicator (8dp circle + icon)
  - Culture name (16sp, medium weight)
  - Progress indicator (linear progress bar)
  - Next action (14sp, secondary text)
  - Photo thumbnail (40dp square, rounded 4dp)
  - Quick actions (overflow menu, 24dp touch targets)
}
```

### Protocol Step Component
**Purpose:** Guide users through sequential culture procedures with clear progression

**Variants:**
- **Collapsed Step:** Title, step number, completion status
- **Expanded Step:** Full instructions, media, timer, notes section
- **Active Step:** Highlighted with progress indicators and action buttons
- **Completed Step:** Checkmark, timestamp, completion notes

**States:**
- **Pending:** Gray state with step number
- **In Progress:** Active blue state with timer if applicable
- **Completed:** Green checkmark with completion timestamp
- **Skipped:** Orange indicator with reason
- **Failed:** Red state with error details and recovery options

**Usage Guidelines:**
- Clear progression indicators showing current step in sequence
- Embedded media support (images, videos) for complex procedures
- Timer integration for time-sensitive steps
- Note-taking capability for each step completion
- Navigation between steps while maintaining context

**Component Specification:**
```
Protocol Step Component {
  - Step number badge (32dp circle)
  - Step title (18sp, medium weight)
  - Instruction text (16sp, regular)
  - Media area (expandable, 16:9 aspect ratio)
  - Timer display (when applicable)
  - Completion controls (checkboxes, notes)
  - Navigation arrows (previous/next)
}
```

### Data Input Forms
**Purpose:** Streamlined data entry for observations and measurements

**Variants:**
- **Quick Entry:** Minimal fields for rapid updates (status, basic notes)
- **Detailed Entry:** Comprehensive form with measurements, environmental data
- **Voice Note Integration:** Speech-to-text capability for hands-free entry

**States:**
- **Empty:** Placeholder text with input guidance
- **Validating:** Loading state during real-time validation
- **Valid:** Green indicators for properly formatted data
- **Error:** Red indicators with specific error messages
- **Saved:** Confirmation state with timestamp

**Usage Guidelines:**
- Smart defaults based on previous entries and culture context
- Offline capability with automatic sync when connected
- Auto-save functionality to prevent data loss
- Contextual help for complex measurements
- Photo integration for visual observations

**Component Specification:**
```
Data Input Forms {
  - Input labels (14sp, medium weight)
  - Input fields (16sp, 48dp min height)
  - Validation indicators (icons + text)
  - Helper text (12sp, secondary color)
  - Action buttons (48dp touch targets)
  - Photo attachment area
  - Auto-save indicator
}
```

## Branding & Style Guide

### Visual Identity
**Brand Guidelines:** Clean, scientific aesthetic with nature-inspired accents. Emphasizes growth, precision, and reliability through careful use of organic shapes balanced with structured layouts.

**Brand Personality:**
- **Scientific:** Precise, reliable, data-driven
- **Accessible:** Approachable, educational, supportive
- **Natural:** Growth-oriented, organic, sustainable

### Color Palette

| Color Type | Hex Code | RGB | Usage | Accessibility Notes |
|------------|----------|-----|-------|-------------------|
| Primary | #2D5016 | 45, 80, 22 | Primary actions, headers, active states | WCAG AA compliant with white text |
| Secondary | #7CB342 | 124, 179, 66 | Growth indicators, positive feedback | WCAG AA compliant with white text |
| Accent | #FF8A65 | 255, 138, 101 | Alerts, important notifications | WCAG AA compliant with black text |
| Success | #4CAF50 | 76, 175, 80 | Positive feedback, confirmations | WCAG AA compliant with white text |
| Warning | #FF9800 | 255, 152, 0 | Cautions, important notices | WCAG AA compliant with black text |
| Error | #F44336 | 244, 67, 54 | Errors, destructive actions | WCAG AA compliant with white text |
| Neutral Dark | #37474F | 55, 71, 79 | Primary text, dark elements | WCAG AAA compliant |
| Neutral Mid | #78909C | 120, 144, 156 | Secondary text, disabled states | WCAG AA compliant |
| Neutral Light | #CFD8DC | 207, 216, 220 | Backgrounds, borders, dividers | Background use only |

### Typography

#### Font Families
- **Primary:** Roboto (Android system font for familiarity and performance)
- **Secondary:** Roboto Condensed (data-heavy screens where space is premium)
- **Monospace:** Roboto Mono (technical data, timestamps, measurements)

#### Type Scale & Hierarchy

| Element | Size | Weight | Line Height | Usage | Accessibility |
|---------|------|--------|-------------|-------|--------------|
| H1 | 24sp | Medium (500) | 32sp | Screen titles, primary headings | Logical heading structure |
| H2 | 20sp | Medium (500) | 28sp | Section headers, card titles | Proper H1→H2 hierarchy |
| H3 | 18sp | Medium (500) | 24sp | Subsection headers, step titles | Consistent nesting |
| Body | 16sp | Regular (400) | 24sp | Primary content, descriptions | 1.5x line height minimum |
| Small | 14sp | Regular (400) | 20sp | Secondary text, metadata | Still readable at scale |
| Caption | 12sp | Regular (400) | 16sp | Labels, timestamps | Limited use, 4.5:1 contrast |

#### Typography Guidelines
- **Sentence case** for all UI text (buttons, labels, headings)
- **Title case** only for proper nouns and protocol names
- **Maximum line length:** 75 characters for optimal readability
- **Paragraph spacing:** 16sp between paragraphs
- **Link styling:** Underlined with primary color, 4.5:1 contrast minimum

### Iconography

**Icon Library:** Material Design Icons with custom culture-specific additions

**Custom Icon Set:**
- Culture container types (petri dish, test tube, flask)
- Growth stages (seedling, established, mature)
- Equipment symbols (autoclave, laminar flow hood, microscope)
- Status indicators (healthy, alert, contaminated)
- Protocol actions (transfer, dilute, observe, harvest)

**Usage Guidelines:**
- Consistent 24dp sizing throughout application
- Meaningful labels for all icons (accessibility requirement)
- Culture-specific symbology for protocols and equipment
- Consistent visual weight and style matching Material Design

### Spacing & Layout

**Grid System:** 8dp base grid system for consistent spacing and alignment

**Spacing Scale:**
- **4dp:** Minimal spacing (within components)
- **8dp:** Small spacing (component internal padding)
- **16dp:** Medium spacing (between related elements)
- **24dp:** Large spacing (between sections)
- **32dp:** Extra large spacing (screen margins)
- **48dp:** Maximum spacing (major section separation)

**Layout Principles:**
- **Consistent margins:** 16dp minimum on mobile, 24dp on tablet+
- **Touch target spacing:** 8dp minimum between interactive elements
- **Card padding:** 16dp internal padding for content cards
- **List item height:** 56dp minimum for single-line items, 72dp for two-line

## Material Design Integration

### Component Adaptations
Leveraging Material Design components with culture-specific customizations:

- **Cards:** Enhanced with status indicators and progress elements
- **Lists:** Custom avatar areas for culture thumbnails
- **Buttons:** Extended color palette for status-specific actions
- **Navigation:** Bottom navigation with custom icons for app sections
- **Dialogs:** Specialized for protocol step guidance and troubleshooting

### Animation & Transitions
- **Duration:** Follow Material Motion guidelines (200-300ms for most transitions)
- **Easing:** Standard Material easing curves for natural motion
- **Shared Elements:** Culture photos and status cards between list and detail views
- **Micro-interactions:** Button press feedback, loading states, success confirmations

---

**Related Documents:**
- [← User Flows](./03-user-flows.md)
- [Accessibility Requirements →](./05-accessibility.md)
- [Responsive Design →](./06-responsive-design.md)
- [Complete Frontend Index →](./index.md)