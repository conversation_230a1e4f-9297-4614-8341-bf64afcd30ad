# CultureStack PRD - Requirements

> **Part of:** [CultureStack Product Requirements Document](../prd.md)
> **Version:** v4
> **Related Sections:** [Overview](01-overview.md) | [Technical Assumptions](03-technical.md) | [Epic Details](05-epic-list.md)

## Requirements

### Functional
1.  **FR1:** Users can initiate a new Culture record, capturing Species/Variety, Explant type, Source plant ID, Date of initiation, Medium composition (select from saved recipe or enter manually), and Initial conditions, the system will assign a unique root Culture ID.
2.  **FR2:** Users can create a Subculture record from a parent Culture, capturing Parent Culture ID, Date of subculture, Medium composition (select from saved recipe or enter manually), and Number of explants transferred.
3.  **FR3:** The system must maintain a clear lineage, linking each Subculture back to its parent Culture, forming a traceable lineage tree (e.g., Culture C1 → Subculture S1 → Subculture S1.1).
4.  **FR4:** Users can log monitoring observations for any culture or subculture, capturing Date of observation, Contamination status (Yes/No), Survival status, Growth stage, and Photos (optional).
5.  **FR5:** The application shall provide a visual timeline or dashboard that displays all cultures and subcultures with their current status, for example Healthy, Contaminated, Ready for Transfer, In Rooting, Acclimatizing.
6.  **FR6:** Users can update the status of a culture or subculture through its lifecycle stages, for example from shoot to root to acclimatization.
7.  **FR7:** The system will flag contaminated cultures for disposal and allow users to remove them from the active timeline, disposal records shall remain accessible for traceability.
8.  **FR8:** Users can schedule transfer or subculture reminders with customizable intervals, for example 7, 14, or 21 days.
9.  **FR9:** The application must send push notifications for upcoming scheduled events such as transfers and monitoring check-ins.
10. **FR10:** A calendar view shall display all scheduled activities for all cultures and subcultures.
11. **FR11:** Users can create and save custom medium recipes, capturing Recipe name, Ingredients and concentrations, and Notes (optional, e.g., special preparation steps).
12. **FR12:** Users can edit, duplicate, or delete existing recipes in their personal recipe library.
13. **FR13:** When initiating or subculturing, users can apply a saved recipe to auto-fill the medium composition field.
14. **FR14:** Free users are limited to tracking a total of 10 active cultures and subcultures combined, with clear in-app messaging about the limit.
15. **FR15:** Paid (Practitioner tier) users have unlimited culture and subculture tracking, premium tier unlock persists across devices when using the same Google Play account.
16. **FR16:** Paid users must sign in with Google account for purchase verification and automatic data sync via their personal Google Drive storage, including photos, lineage, and recipes, for multi-device access.
17. **FR17:** Paid users can export their data, including lineage history and recipes, to CSV or PDF formats, supporting both local device export and direct Google Drive storage.

### Non-Functional
1.  **NFR1:** The application shall be developed natively for Android to ensure optimal performance and user experience on mobile devices.
2.  **NFR2:** CultureStack must comply with Google Play Store policies to ensure publication and monetization of the app.
3.  **NFR3:** User data, especially for paid users, must be securely stored and transmitted, adhering to best practices in data protection and privacy.
4.  **NFR4:** The app should be designed to minimize battery consumption, considering the mobile nature of the user base.
5.  **NFR5:** CultureStack should be usable in offline mode, with data syncing when the device is online, to accommodate users in various environments.
6.  **NFR6:** The user interface must be intuitive and accessible, reducing the learning curve for new users and accommodating users with varying levels of tech-savviness.
7.  **NFR7:** The application should be scalable to accommodate a growing number of users and cultures without performance degradation.
8.  **NFR8:** Regular updates and maintenance must be planned to address bugs, update dependencies, and introduce new features based on user feedback.
9.  **NFR9:** The app shall support structured storage of recipes, not just free text, to enable future analytics such as comparing contamination or survival rates by recipe type.
10. **NFR10:** Photo storage must be optimized, for example through compression and resizing, to prevent device or Google Drive space issues.
11. **NFR11:** The app should support multi-language localization, starting with English and Malay.
12. **NFR12:** The app should include error recovery mechanisms, for example when sync fails, when data conflicts across devices, or during unexpected shutdowns.
13. **NFR13:** Each recipe record shall maintain version history, allowing users to view and revert changes. Culture and subculture records shall maintain a lightweight audit log of key edits, specifically: status changes, contamination flags, disposal events, medium composition changes, and lineage modifications, without full rollback capability.

---
**Navigation:** [← Back: Overview](01-overview.md) | [Next: Technical Assumptions →](03-technical.md)