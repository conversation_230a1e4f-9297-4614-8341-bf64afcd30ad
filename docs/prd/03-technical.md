# CultureStack PRD - Technical Assumptions

> **Part of:** [CultureStack Product Requirements Document](../prd.md)
> **Version:** v4
> **Related Sections:** [Requirements](02-requirements.md) | [UI Design Goals](04-ui-design.md) | [Epic Details](05-epic-list.md)

## Technical Assumptions

### Repository Structure: Monorepo
Single repository containing the complete Android application with organized module structure for maintainability.

### Service Architecture
**Native Android Application** - Single APK deployment targeting Android devices, with local SQLite storage and cloud sync capabilities.

### Testing Requirements
**Unit + Integration Testing** - Comprehensive testing including unit tests for business logic, integration tests for database operations, and UI testing for critical user flows.

### Additional Technical Assumptions and Requests
- **Authentication**: Google Sign-In API for user authentication (no separate account system)
- **Purchase Management**: Google Play Billing Library for in-app purchases with cross-device restoration
- **Data Storage**: Local SQLite database for offline functionality with Google Drive API v3 for cloud sync
- **Cloud Sync**: User's personal Google Drive app folder for data synchronization (user-owned data)
- **Offline Support**: Offline-first architecture with automatic sync when connectivity available
- **Platform**: Native Android development using Kotlin/Java for optimal performance
- **Target SDK**: Android API level supporting modern Google Services integration
- **Dependencies**: Google Services (Sign-In, Drive, Play Billing), Room Database, WorkManager for sync

---
**Navigation:** [← Back: Requirements](02-requirements.md) | [Next: UI Design Goals →](04-ui-design.md)