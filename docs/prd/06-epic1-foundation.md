# CultureStack PRD - Epic 1: Foundation & Core Culture Management

> **Part of:** [CultureStack Product Requirements Document](../prd.md)
> **Version:** v4
> **Related Sections:** [Epic List](05-epic-list.md) | [Epic 2](07-epic2-advanced.md)

## Epic 1: Foundation & Core Culture Management

Establish the foundational Android application infrastructure and core culture tracking capabilities that enable tissue culture practitioners to create, manage, and track their cultures and subcultures. This epic delivers immediate value by replacing makeshift tracking solutions with a purpose-built mobile application that maintains lineage relationships and provides a centralized timeline view of all cultures.

### Story 1.1: Android App Setup and Basic Navigation

As a tissue culture practitioner,
I want a native Android application with basic navigation structure,
so that I have a reliable mobile platform to manage my cultures.

#### Acceptance Criteria
1. Native Android application builds and installs successfully on target devices
2. App includes bottom navigation with Timeline, Calendar, Add, Library, and Settings tabs
3. Main timeline screen displays with placeholder content and app branding
4. App follows Material Design guidelines for consistent Android UX
5. All navigation tabs are accessible but show appropriate "coming soon" messages for unimplemented features
6. App includes proper Android manifest configuration and required permissions structure
7. Local SQLite database is initialized with basic schema for cultures and subcultures

### Story 1.2: Create New Culture Record

As a tissue culture practitioner,
I want to create a new culture record with essential details,
so that I can begin tracking a new culture from initiation.

#### Acceptance Criteria
1. "Add Culture" button on main timeline navigates to culture creation form
2. Form captures: Species/Variety, Explant type, Source plant ID, Date of initiation (defaults to today), Medium composition (text field), and Initial conditions
3. System assigns unique Culture ID automatically (e.g., C001, C002)
4. Form includes validation for required fields (Species/Variety, Explant type, Date)
5. Successfully created culture appears immediately in timeline view
6. Culture record is saved to local SQLite database
7. Created culture shows status as "Active" by default
8. Form can be cancelled without saving, returning to timeline

### Story 1.3: View Culture Timeline Dashboard

As a tissue culture practitioner,
I want to see all my cultures in a visual timeline dashboard,
so that I can quickly assess the status and progress of all my cultures.

#### Acceptance Criteria
1. Timeline displays all cultures in chronological order (newest first)
2. Each culture entry shows: Culture ID, Species/Variety, Days since initiation, Current status
3. Timeline uses color coding for status: Green (Healthy), Yellow (Needs attention), Red (Contaminated)
4. Empty timeline shows helpful message encouraging user to create first culture
5. Timeline refreshes automatically when new cultures are added
6. Tapping on culture entry navigates to detailed culture view
7. Timeline supports scrolling for users with many cultures
8. Pull-to-refresh gesture updates timeline data

### Story 1.4: Create Subculture from Parent Culture

As a tissue culture practitioner,
I want to create a subculture from an existing culture,
so that I can expand my cultures while maintaining lineage tracking.

#### Acceptance Criteria
1. Culture detail view includes "Create Subculture" button
2. Subculture form pre-fills Parent Culture ID from source culture
3. Form captures: Date of subculture (defaults to today), Medium composition (text field), Number of explants transferred
4. System assigns unique Subculture ID following parent (e.g., C001 → S001, S002)
5. Subculture appears in timeline as separate entry with parent linkage indicator
6. Parent culture's detail view shows list of all subcultures created from it
7. Subculture record links back to parent culture ID in database
8. Timeline distinguishes between original cultures and subcultures visually

### Story 1.5: Culture and Subculture Detail Views

As a tissue culture practitioner,
I want to view comprehensive details of any culture or subculture,
so that I can review its complete information and history.

#### Acceptance Criteria
1. Culture detail view displays all recorded information: ID, Species/Variety, Explant type, Source plant, Initiation date, Current status, Medium composition, Initial conditions
2. Detail view shows "Created X days ago" and current age calculation
3. Subculture detail view includes parent culture information and lineage path
4. Detail view includes "Edit" functionality for updating information
5. Back navigation returns to timeline maintaining scroll position
6. Detail view shows placeholder sections for future features (observations, photos)
7. Lineage section displays parent-child relationships clearly
8. Detail view adapts layout appropriately for both cultures and subcultures

### Story 1.6: Basic Lineage Tree Visualization

As a tissue culture practitioner,
I want to see the lineage relationships between cultures and subcultures,
so that I can understand the heritage and expansion of my cultures.

#### Acceptance Criteria
1. Culture detail view includes expandable "Lineage" section
2. Lineage displays parent culture (for subcultures) and all child subcultures (for parent cultures)
3. Lineage uses visual indicators (lines, indentation) to show parent-child relationships
4. Each lineage entry shows ID, creation date, and current status
5. Tapping lineage entry navigates to that culture's detail view
6. Lineage correctly handles multiple generations (C1 → S1 → S1.1)
7. Root cultures clearly marked as "Original Culture" in lineage display
8. Empty lineage sections show appropriate messaging

---
**Navigation:** [← Back: Epic List](05-epic-list.md) | [Next: Epic 2 →](07-epic2-advanced.md)