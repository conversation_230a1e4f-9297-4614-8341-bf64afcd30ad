# CultureStack PRD - Epic 2: Advanced Culture Operations & Recipe System

> **Part of:** [CultureStack Product Requirements Document](../prd.md)
> **Version:** v4
> **Related Sections:** [Epic 1](06-epic1-foundation.md) | [Epic 3](08-epic3-scheduling.md)

## Epic 2: Advanced Culture Operations & Recipe System

Enable sophisticated culture management capabilities including photo documentation, detailed monitoring observations, recipe library management, and culture lifecycle status tracking. This epic transforms CultureStack from basic tracking to a comprehensive culture management system that significantly reduces failure rates through systematic monitoring and standardized recipes.

### Story 2.1: Log Culture Monitoring Observations

As a tissue culture practitioner,
I want to log detailed monitoring observations for my cultures,
so that I can track their health and identify problems early.

#### Acceptance Criteria
1. Culture detail view includes "Add Observation" button
2. Observation form captures: Date of observation (defaults to today), Contamination status (Yes/No toggle), Survival status (dropdown: Excellent/Good/Fair/Poor), Growth stage (dropdown: Initiation/Establishment/Growth/Ready for transfer)
3. Observations are saved with timestamp and linked to specific culture/subculture
4. Culture detail view displays chronological list of all observations
5. Latest observation updates culture's current status automatically
6. Contamination flag immediately changes culture status and visual indicators
7. Observation form includes optional notes field for additional details
8. Timeline view reflects updated status from latest observations

### Story 2.2: Photo Documentation Integration

As a tissue culture practitioner,
I want to capture and attach photos to my culture observations,
so that I can visually document progress and identify issues.

#### Acceptance Criteria
1. Observation form includes camera integration with "Add Photo" button
2. Camera opens with optimal settings for close-up culture photography
3. Captured photos are compressed and resized automatically for storage efficiency
4. Multiple photos can be attached to single observation (up to 5 photos)
5. Photos display as thumbnails in observation list with tap-to-expand
6. Culture detail view shows photo gallery of all historical photos
7. Photos are stored locally with efficient file naming linked to observations
8. Photo deletion functionality available with confirmation prompt

### Story 2.3: Culture Status Lifecycle Management

As a tissue culture practitioner,
I want to update culture status through defined lifecycle stages,
so that I can systematically track progress from initiation to completion.

#### Acceptance Criteria
1. Culture status dropdown includes: Healthy, Contaminated, Ready for Transfer, In Rooting, Acclimatizing, Completed, Disposed
2. Status changes are logged with timestamp for audit trail
3. Status updates automatically trigger visual changes in timeline (colors, icons)
4. Contaminated cultures show prominent warning indicators throughout app
5. Ready for Transfer status triggers suggestion to create subculture
6. Completed/Disposed cultures can be filtered out of active timeline view
7. Status change notifications appear briefly to confirm updates
8. Status history is maintained and viewable in culture detail

### Story 2.4: Create and Save Custom Medium Recipes

As a tissue culture practitioner,
I want to create and save custom medium recipes,
so that I can standardize my culture media and ensure consistency.

#### Acceptance Criteria
1. Recipe Library screen accessible from main navigation
2. "New Recipe" button opens recipe creation form
3. Recipe form captures: Recipe name (required), Ingredients list with concentrations, Preparation notes (optional), Recipe category (optional)
4. Ingredients can be added/removed dynamically with concentration fields
5. Recipe validation ensures name uniqueness and required fields
6. Saved recipes appear in alphabetical list in Recipe Library
7. Recipe detail view shows complete formulation and notes
8. Recipes are stored locally in SQLite database with proper indexing

### Story 2.5: Apply Saved Recipes to Culture Creation

As a tissue culture practitioner,
I want to select and apply saved recipes when creating cultures,
so that I can quickly use standardized media formulations.

#### Acceptance Criteria
1. Culture and subculture creation forms include "Select Recipe" option
2. Recipe selection opens searchable list of saved recipes
3. Selected recipe auto-fills medium composition field with complete formulation
4. Auto-filled text can be edited for custom modifications
5. Recipe name is stored with culture record for tracking
6. Culture detail view shows recipe name used (if any) with link to recipe details
7. Recipe selection is optional - manual entry remains available
8. Recently used recipes appear at top of selection list

### Story 2.6: Edit and Manage Recipe Library

As a tissue culture practitioner,
I want to edit, duplicate, and organize my recipe library,
so that I can maintain and improve my media formulations over time.

#### Acceptance Criteria
1. Recipe Library includes edit, duplicate, and delete actions for each recipe
2. Recipe editing preserves original creation date but adds modification timestamp
3. Recipe duplication creates copy with "_Copy" suffix for easy identification
4. Recipe deletion includes confirmation prompt with usage warning
5. Search functionality filters recipes by name and ingredients
6. Recipe categories enable organization (if implemented in creation)
7. Bulk actions available for managing multiple recipes
8. Recipe usage count displayed showing how many cultures used each recipe

---
**Navigation:** [← Back: Epic 1](06-epic1-foundation.md) | [Next: Epic 3 →](08-epic3-scheduling.md)