# Unit Testing Strategy (70% of tests)

## Overview
This document defines the comprehensive unit testing approach for CultureStack, targeting 70% of the total test suite with fast, isolated tests that provide immediate feedback during development.

---

## **Unit Testing Goals and Coverage Targets**

### **Coverage Targets**
- **Overall Target:** 80%+ code coverage
- **Domain Logic:** 95%+ coverage (business rules, use cases)
- **Data Layer:** 90%+ coverage (DAOs, Repositories)
- **UI Logic (ViewModels):** 85%+ coverage
- **Utility Classes:** 90%+ coverage

### **Test Characteristics**
- **Execution Speed:** < 100ms per test
- **Isolation:** No Android dependencies, pure JVM execution
- **Scope:** Single class/method behavior validation
- **Reliability:** 99%+ success rate, < 1% flakiness

## **Testing Framework Stack**

```kotlin
// build.gradle.kts (app module)
dependencies {
    // Unit Testing Core
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")

    // Mockito for mocking
    testImplementation("org.mockito:mockito-core:5.5.0")
    testImplementation("org.mockito.kotlin:mockito-kotlin:5.1.0")
    testImplementation("org.mockito:mockito-inline:5.2.0")

    // Truth assertions
    testImplementation("com.google.truth:truth:1.1.5")

    // Robolectric for Android components
    testImplementation("org.robolectric:robolectric:4.10.3")

    // Room testing
    testImplementation("androidx.room:room-testing:${Versions.room}")

    // Architecture components testing
    testImplementation("androidx.arch.core:core-testing:2.2.0")

    // Turbine for Flow testing
    testImplementation("app.cash.turbine:turbine:1.0.0")
}
```

## **Repository Layer Unit Testing**

### **Repository Implementation Testing**

```kotlin
@RunWith(MockitoJUnitRunner::class)
class CultureRepositoryImplTest {

    @Mock private lateinit var cultureDao: CultureDao
    @Mock private lateinit var subcultureDao: SubcultureDao
    @Mock private lateinit var validationService: ValidationService
    @Mock private lateinit var eventBus: EventBus

    private lateinit var repository: CultureRepositoryImpl

    @Before
    fun setup() {
        repository = CultureRepositoryImpl(
            cultureDao = cultureDao,
            subcultureDao = subcultureDao,
            validationService = validationService,
            eventBus = eventBus
        )
    }

    @Test
    fun `createCulture with valid request returns success`() = runTest {
        // Arrange
        val request = createValidCultureRequest()
        val expectedCulture = request.toCulture()

        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(isValid = true, errors = emptyList()))
        whenever(cultureDao.insertCulture(any())).thenReturn(1L)

        // Act
        val result = repository.createCulture(request)

        // Assert
        assertThat(result.isSuccess).isTrue()
        verify(cultureDao).insertCulture(any())
        verify(eventBus).publish(any<CultureCreatedEvent>())
    }

    @Test
    fun `createCulture with invalid request returns validation error`() = runTest {
        // Arrange
        val request = createInvalidCultureRequest()
        val validationErrors = listOf("Species is required")

        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(isValid = false, errors = validationErrors))

        // Act
        val result = repository.createCulture(request)

        // Assert
        assertThat(result.isFailure).isTrue()
        assertThat(result.exceptionOrNull()).isInstanceOf(ValidationException::class.java)
        verify(cultureDao, never()).insertCulture(any())
    }

    @Test
    fun `getActiveCultures returns flow of cultures`() = runTest {
        // Arrange
        val cultures = listOf(
            createTestCulture("1", "Orchid"),
            createTestCulture("2", "Rose")
        )
        whenever(cultureDao.getAllActiveCultures()).thenReturn(flowOf(cultures))

        // Act & Assert
        repository.getActiveCultures().test {
            assertThat(awaitItem()).isEqualTo(cultures)
            awaitComplete()
        }
    }

    @Test
    fun `getCultureById with existing id returns culture`() = runTest {
        // Arrange
        val cultureId = "existing-id"
        val expectedCulture = createTestCulture(cultureId, "Test Plant")
        whenever(cultureDao.getCultureById(cultureId)).thenReturn(expectedCulture)

        // Act
        val result = repository.getCultureById(cultureId)

        // Assert
        assertThat(result.isSuccess).isTrue()
        assertThat(result.getOrNull()).isEqualTo(expectedCulture)
    }

    @Test
    fun `getCultureById with non-existing id returns null`() = runTest {
        // Arrange
        val cultureId = "non-existing-id"
        whenever(cultureDao.getCultureById(cultureId)).thenReturn(null)

        // Act
        val result = repository.getCultureById(cultureId)

        // Assert
        assertThat(result.isSuccess).isTrue()
        assertThat(result.getOrNull()).isNull()
    }

    @Test
    fun `updateCultureStatus with valid transition succeeds`() = runTest {
        // Arrange
        val cultureId = "test-id"
        val currentStatus = CultureStatus.HEALTHY
        val newStatus = CultureStatus.READY_FOR_TRANSFER
        val culture = createTestCulture(cultureId, "Test", status = currentStatus)

        whenever(cultureDao.getCultureById(cultureId)).thenReturn(culture)
        whenever(validationService.validateStatusTransition(currentStatus, newStatus))
            .thenReturn(true)

        // Act
        val result = repository.updateCultureStatus(cultureId, newStatus, "Growth observed")

        // Assert
        assertThat(result.isSuccess).isTrue()
        verify(cultureDao).updateCultureStatus(cultureId, newStatus)
        verify(eventBus).publish(any<CultureStatusChangedEvent>())
    }

    private fun createValidCultureRequest() = CreateCultureRequest(
        species = "Orchid",
        explantType = "Leaf",
        initiationDate = LocalDate.now(),
        mediumComposition = "MS medium",
        initialConditions = "25°C, 16h light"
    )

    private fun createInvalidCultureRequest() = CreateCultureRequest(
        species = "",
        explantType = "",
        initiationDate = LocalDate.now().plusDays(1),
        mediumComposition = "",
        initialConditions = ""
    )

    private fun createTestCulture(
        id: String,
        species: String,
        status: CultureStatus = CultureStatus.HEALTHY
    ) = Culture(
        id = id,
        cultureId = "C${id.padStart(3, '0')}",
        species = species,
        explantType = "Leaf",
        initiationDate = LocalDate.now(),
        mediumComposition = "MS medium",
        initialConditions = "25°C, 16h light",
        status = status,
        deviceId = "test-device"
    )
}
```

## **ViewModel Unit Testing**

### **ViewModel State Management Testing**

```kotlin
@RunWith(MockitoJUnitRunner::class)
class CultureDetailViewModelTest {

    @Mock private lateinit var cultureRepository: CultureRepository
    @Mock private lateinit var observationRepository: ObservationRepository
    @Mock private lateinit var savedStateHandle: SavedStateHandle

    private lateinit var viewModel: CultureDetailViewModel
    private val testDispatcher = UnconfinedTestDispatcher()

    @get:Rule
    val mainDispatcherRule = MainDispatcherRule()

    @Before
    fun setup() {
        whenever(savedStateHandle.get<String>("cultureId")).thenReturn("test-culture-id")

        viewModel = CultureDetailViewModel(
            cultureRepository = cultureRepository,
            observationRepository = observationRepository,
            savedStateHandle = savedStateHandle
        )
    }

    @Test
    fun `initial state loads culture data successfully`() = runTest {
        // Arrange
        val testCulture = createTestCulture("test-culture-id", "Orchid")
        whenever(cultureRepository.getCultureById("test-culture-id"))
            .thenReturn(Result.success(testCulture))

        // Act - ViewModel initialization triggers loading

        // Assert
        viewModel.uiState.test {
            val initialState = awaitItem()
            assertThat(initialState.culture).isEqualTo(testCulture)
            assertThat(initialState.isLoading).isFalse()
            assertThat(initialState.error).isNull()
        }
    }

    @Test
    fun `initial state handles culture not found`() = runTest {
        // Arrange
        whenever(cultureRepository.getCultureById("test-culture-id"))
            .thenReturn(Result.success(null))

        // Act - ViewModel initialization

        // Assert
        viewModel.uiState.test {
            val state = awaitItem()
            assertThat(state.culture).isNull()
            assertThat(state.error).isEqualTo("Culture not found")
            assertThat(state.isLoading).isFalse()
        }
    }

    @Test
    fun `updateCultureStatus updates UI state on success`() = runTest {
        // Arrange
        val initialCulture = createTestCulture("test-id", "Orchid", CultureStatus.HEALTHY)
        val newStatus = CultureStatus.READY_FOR_TRANSFER

        whenever(cultureRepository.getCultureById(any()))
            .thenReturn(Result.success(initialCulture))
        whenever(cultureRepository.updateCultureStatus(any(), eq(newStatus), any()))
            .thenReturn(Result.success(Unit))

        // Act
        viewModel.updateCultureStatus(newStatus, "Ready for transfer")

        // Assert
        viewModel.uiState.test {
            awaitItem() // Skip initial state
            val updatedState = awaitItem()
            assertThat(updatedState.culture?.status).isEqualTo(newStatus)
            assertThat(updatedState.isLoading).isFalse()
            assertThat(updatedState.error).isNull()
        }

        verify(cultureRepository).updateCultureStatus(
            "test-culture-id",
            newStatus,
            "Ready for transfer"
        )
    }

    @Test
    fun `updateCultureStatus shows error on failure`() = runTest {
        // Arrange
        val error = Exception("Update failed")
        whenever(cultureRepository.updateCultureStatus(any(), any(), any()))
            .thenReturn(Result.failure(error))

        // Act
        viewModel.updateCultureStatus(CultureStatus.CONTAMINATED, "Contamination detected")

        // Assert
        viewModel.uiState.test {
            awaitItem() // Skip initial state
            val errorState = awaitItem()
            assertThat(errorState.error).isEqualTo("Update failed")
            assertThat(errorState.isLoading).isFalse()
        }
    }

    @Test
    fun `refreshData reloads culture information`() = runTest {
        // Arrange
        val updatedCulture = createTestCulture("test-culture-id", "Updated Orchid")
        whenever(cultureRepository.getCultureById("test-culture-id"))
            .thenReturn(Result.success(updatedCulture))

        // Act
        viewModel.refreshData()

        // Assert
        viewModel.uiState.test {
            val state = awaitItem()
            assertThat(state.culture).isEqualTo(updatedCulture)
            assertThat(state.isLoading).isFalse()
        }

        verify(cultureRepository, times(2)).getCultureById("test-culture-id")
    }

    private fun createTestCulture(
        id: String,
        species: String,
        status: CultureStatus = CultureStatus.HEALTHY
    ) = Culture(
        id = id,
        cultureId = "C001",
        species = species,
        explantType = "Leaf",
        initiationDate = LocalDate.now(),
        mediumComposition = "MS medium",
        initialConditions = "Standard conditions",
        status = status,
        deviceId = "test-device"
    )
}

// Custom test rule for main dispatcher
class MainDispatcherRule(
    private val testDispatcher: TestDispatcher = UnconfinedTestDispatcher()
) : TestWatcher() {

    override fun starting(description: Description) {
        Dispatchers.setMain(testDispatcher)
    }

    override fun finished(description: Description) {
        Dispatchers.resetMain()
    }
}
```

## **DAO Unit Testing with Room**

### **Database Testing with In-Memory Database**

```kotlin
@RunWith(AndroidJUnit4::class)
class CultureDaoTest {

    private lateinit var database: CultureDatabase
    private lateinit var cultureDao: CultureDao

    @Before
    fun createDb() {
        val context = ApplicationProvider.getApplicationContext<Context>()
        database = Room.inMemoryDatabaseBuilder(context, CultureDatabase::class.java)
            .allowMainThreadQueries()
            .build()
        cultureDao = database.cultureDao()
    }

    @After
    fun closeDb() {
        database.close()
    }

    @Test
    fun insertCulture_retrievesCulture() = runTest {
        // Arrange
        val culture = createTestCulture("test-id", "Test Plant")

        // Act
        val insertedId = cultureDao.insertCulture(culture)
        val retrievedCulture = cultureDao.getCultureById(culture.id)

        // Assert
        assertThat(insertedId).isGreaterThan(0)
        assertThat(retrievedCulture).isEqualTo(culture)
    }

    @Test
    fun getAllActiveCultures_excludesDeletedCultures() = runTest {
        // Arrange
        val activeCulture = createTestCulture("1", "Active Plant", isDeleted = false)
        val deletedCulture = createTestCulture("2", "Deleted Plant", isDeleted = true)

        cultureDao.insertCulture(activeCulture)
        cultureDao.insertCulture(deletedCulture)

        // Act
        val activeCultures = cultureDao.getAllActiveCultures().first()

        // Assert
        assertThat(activeCultures).hasSize(1)
        assertThat(activeCultures[0].id).isEqualTo("1")
        assertThat(activeCultures[0].isDeleted).isFalse()
    }

    @Test
    fun searchCulturesBySpecies_returnsMatchingResults() = runTest {
        // Arrange
        val orchid1 = createTestCulture("1", "Dendrobium orchid")
        val orchid2 = createTestCulture("2", "Phalaenopsis orchid")
        val rose = createTestCulture("3", "Rosa damascena")

        listOf(orchid1, orchid2, rose).forEach { cultureDao.insertCulture(it) }

        // Act
        val searchResults = cultureDao.searchCulturesBySpecies("orchid")

        // Assert
        assertThat(searchResults).hasSize(2)
        assertThat(searchResults.map { it.id }).containsExactly("1", "2")
    }

    @Test
    fun getCulturesByStatus_filtersCorrectly() = runTest {
        // Arrange
        val healthyCulture1 = createTestCulture("1", "Plant 1", status = CultureStatus.HEALTHY)
        val healthyCulture2 = createTestCulture("2", "Plant 2", status = CultureStatus.HEALTHY)
        val contaminatedCulture = createTestCulture("3", "Plant 3", status = CultureStatus.CONTAMINATED)

        listOf(healthyCulture1, healthyCulture2, contaminatedCulture)
            .forEach { cultureDao.insertCulture(it) }

        // Act
        val healthyCultures = cultureDao.getCulturesByStatus(CultureStatus.HEALTHY)

        // Assert
        assertThat(healthyCultures).hasSize(2)
        assertThat(healthyCultures.map { it.id }).containsExactly("1", "2")
    }

    @Test
    fun updateCultureStatus_updatesCorrectly() = runTest {
        // Arrange
        val culture = createTestCulture("test-id", "Test Plant", status = CultureStatus.HEALTHY)
        cultureDao.insertCulture(culture)

        // Act
        val newStatus = CultureStatus.READY_FOR_TRANSFER
        cultureDao.updateCultureStatus("test-id", newStatus)

        // Assert
        val updatedCulture = cultureDao.getCultureById("test-id")
        assertThat(updatedCulture?.status).isEqualTo(newStatus)
        assertThat(updatedCulture?.syncVersion).isEqualTo(2) // Should increment sync version
    }

    @Test
    fun getCulturesPage_implementsPaginationCorrectly() = runTest {
        // Arrange - Insert 15 cultures
        repeat(15) { index ->
            val culture = createTestCulture("culture-$index", "Plant $index")
            cultureDao.insertCulture(culture)
        }

        // Act - Get first page
        val firstPage = cultureDao.getCulturesPage(limit = 5, offset = 0)

        // Assert
        assertThat(firstPage).hasSize(5)

        // Act - Get second page
        val secondPage = cultureDao.getCulturesPage(limit = 5, offset = 5)

        // Assert
        assertThat(secondPage).hasSize(5)
        // Ensure no overlap between pages
        val firstPageIds = firstPage.map { it.id }.toSet()
        val secondPageIds = secondPage.map { it.id }.toSet()
        assertThat(firstPageIds.intersect(secondPageIds)).isEmpty()
    }

    private fun createTestCulture(
        id: String = "test-id",
        species: String = "Test Species",
        status: CultureStatus = CultureStatus.HEALTHY,
        isDeleted: Boolean = false
    ) = Culture(
        id = id,
        cultureId = "C${id.padStart(3, '0')}",
        species = species,
        explantType = "Leaf",
        initiationDate = LocalDate.now(),
        mediumComposition = "MS medium",
        initialConditions = "25°C, 16h light",
        status = status,
        isDeleted = isDeleted,
        deviceId = "test-device",
        createdAt = Instant.now(),
        updatedAt = Instant.now(),
        syncVersion = 1
    )
}
```

## **Service Layer Unit Testing**

### **Business Logic Service Testing**

```kotlin
@RunWith(MockitoJUnitRunner::class)
class CultureServiceImplTest {

    @Mock private lateinit var cultureRepository: CultureRepository
    @Mock private lateinit var observationRepository: ObservationRepository
    @Mock private lateinit var validationService: ValidationService
    @Mock private lateinit var notificationService: NotificationService

    private lateinit var cultureService: CultureServiceImpl

    @Before
    fun setup() {
        cultureService = CultureServiceImpl(
            cultureRepository = cultureRepository,
            observationRepository = observationRepository,
            validationService = validationService,
            notificationService = notificationService
        )
    }

    @Test
    fun `createCultureWorkflow completes all steps successfully`() = runTest {
        // Arrange
        val request = createValidCultureRequest()
        val expectedCulture = createTestCulture("test-id", request.species)

        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(isValid = true, errors = emptyList()))
        whenever(cultureRepository.createCulture(request))
            .thenReturn(Result.success(expectedCulture))
        whenever(observationRepository.createObservation(any()))
            .thenReturn(Result.success(createTestObservation()))

        // Act
        val result = cultureService.createCultureWorkflow(request)

        // Assert
        assertThat(result.isSuccess).isTrue()
        val workflowResult = result.getOrThrow()

        assertThat(workflowResult.isComplete).isTrue()
        assertThat(workflowResult.workflowSteps).hasSize(5)
        assertThat(workflowResult.workflowSteps.all { it.status == "completed" }).isTrue()
        assertThat(workflowResult.nextRecommendedActions).isNotEmpty()

        // Verify all services were called appropriately
        verify(validationService).validateCultureCreation(request)
        verify(cultureRepository).createCulture(request)
        verify(notificationService, times(2)).scheduleReminder(any(), any(), any(), any())
    }

    @Test
    fun `createCultureWorkflow fails on validation error`() = runTest {
        // Arrange
        val request = createInvalidCultureRequest()
        val validationErrors = listOf("Species is required", "Invalid date")

        whenever(validationService.validateCultureCreation(request))
            .thenReturn(ValidationResult(isValid = false, errors = validationErrors))

        // Act
        val result = cultureService.createCultureWorkflow(request)

        // Assert
        assertThat(result.isFailure).isTrue()
        assertThat(result.exceptionOrNull()).isInstanceOf(ValidationException::class.java)

        // Verify repository was never called
        verify(cultureRepository, never()).createCulture(any())
        verify(notificationService, never()).scheduleReminder(any(), any(), any(), any())
    }

    @Test
    fun `transitionCultureStatus validates business rules`() = runTest {
        // Arrange
        val cultureId = "test-id"
        val culture = createTestCulture(cultureId, "Test Plant", CultureStatus.HEALTHY)
        val transition = StatusTransitionRequest(
            newStatus = CultureStatus.READY_FOR_TRANSFER,
            notes = "Growth observed"
        )

        whenever(cultureRepository.getCultureById(cultureId))
            .thenReturn(Result.success(culture))
        whenever(validationService.validateStatusTransition(
            CultureStatus.HEALTHY,
            CultureStatus.READY_FOR_TRANSFER
        )).thenReturn(true)
        whenever(cultureRepository.updateCultureStatus(cultureId, transition.newStatus, transition.notes))
            .thenReturn(Result.success(Unit))

        // Act
        val result = cultureService.transitionCultureStatus(cultureId, transition)

        // Assert
        assertThat(result.isSuccess).isTrue()
        val transitionResult = result.getOrThrow()

        assertThat(transitionResult.cultureId).isEqualTo(cultureId)
        assertThat(transitionResult.oldStatus).isEqualTo(CultureStatus.HEALTHY)
        assertThat(transitionResult.newStatus).isEqualTo(CultureStatus.READY_FOR_TRANSFER)
        assertThat(transitionResult.notes).isEqualTo("Growth observed")

        verify(validationService).validateStatusTransition(CultureStatus.HEALTHY, CultureStatus.READY_FOR_TRANSFER)
        verify(cultureRepository).updateCultureStatus(cultureId, transition.newStatus, transition.notes)
    }

    @Test
    fun `analyzeCulturePerformance calculates metrics correctly`() = runTest {
        // Arrange
        val cultureId = "test-id"
        val culture = createTestCulture(cultureId, "Test Plant")
        val observations = listOf(
            createTestObservation(cultureId, LocalDate.now().minusDays(7), SurvivalStatus.GOOD),
            createTestObservation(cultureId, LocalDate.now().minusDays(3), SurvivalStatus.EXCELLENT),
            createTestObservation(cultureId, LocalDate.now(), SurvivalStatus.EXCELLENT)
        )

        whenever(cultureRepository.getCultureById(cultureId))
            .thenReturn(Result.success(culture))
        whenever(observationRepository.getObservationsForCulture(cultureId))
            .thenReturn(flowOf(observations))

        // Act
        val result = cultureService.analyzeCulturePerformance(cultureId)

        // Assert
        assertThat(result.isSuccess).isTrue()
        val analysis = result.getOrThrow()

        assertThat(analysis.cultureId).isEqualTo(cultureId)
        assertThat(analysis.observationCount).isEqualTo(3)
        assertThat(analysis.averageSurvivalRating).isGreaterThan(3.0) // Should be improving
        assertThat(analysis.daysActive).isEqualTo(culture.daysActive)
        assertThat(analysis.recommendations).isNotEmpty()
    }

    private fun createValidCultureRequest() = CreateCultureRequest(
        species = "Test Orchid",
        explantType = "Leaf segment",
        initiationDate = LocalDate.now(),
        mediumComposition = "MS + 2mg/L BAP",
        initialConditions = "25°C, 16h photoperiod",
        initialObservation = "Initial culture setup"
    )

    private fun createInvalidCultureRequest() = CreateCultureRequest(
        species = "",
        explantType = "Invalid",
        initiationDate = LocalDate.now().plusDays(1),
        mediumComposition = "",
        initialConditions = ""
    )

    private fun createTestCulture(
        id: String,
        species: String,
        status: CultureStatus = CultureStatus.HEALTHY
    ) = Culture(
        id = id,
        cultureId = "C001",
        species = species,
        explantType = "Leaf segment",
        initiationDate = LocalDate.now().minusDays(14),
        mediumComposition = "MS + 2mg/L BAP",
        initialConditions = "25°C, 16h photoperiod",
        status = status,
        deviceId = "test-device"
    )

    private fun createTestObservation(
        cultureId: String = "test-id",
        date: LocalDate = LocalDate.now(),
        survivalStatus: SurvivalStatus = SurvivalStatus.GOOD
    ) = Observation(
        id = "obs-${UUID.randomUUID()}",
        cultureId = cultureId,
        observationDate = date,
        contaminationStatus = false,
        survivalStatus = survivalStatus,
        growthStage = GrowthStage.ESTABLISHMENT,
        notes = "Test observation",
        deviceId = "test-device"
    )
}
```

## **Test Utilities and Helpers**

### **Test Data Factory**

```kotlin
object TestDataFactory {

    fun createCulture(
        id: String = "test-culture-${UUID.randomUUID()}",
        species: String = "Test Species",
        status: CultureStatus = CultureStatus.HEALTHY,
        initiationDate: LocalDate = LocalDate.now().minusDays(7)
    ) = Culture(
        id = id,
        cultureId = generateCultureId(),
        species = species,
        explantType = "Leaf segment",
        sourcePlantId = null,
        initiationDate = initiationDate,
        mediumComposition = "MS + 2mg/L BAP + 0.5mg/L NAA",
        initialConditions = "25°C, 16h photoperiod, 50μmol/m²/s",
        status = status,
        isDeleted = false,
        createdAt = Instant.now().minus(7, ChronoUnit.DAYS),
        updatedAt = Instant.now().minus(1, ChronoUnit.DAYS),
        syncVersion = 1,
        lastSyncedAt = null,
        deviceId = "test-device"
    )

    fun createObservation(
        cultureId: String,
        observationDate: LocalDate = LocalDate.now(),
        contaminationStatus: Boolean = false,
        survivalStatus: SurvivalStatus = SurvivalStatus.GOOD
    ) = Observation(
        id = "test-observation-${UUID.randomUUID()}",
        cultureId = cultureId,
        observationDate = observationDate,
        contaminationStatus = contaminationStatus,
        survivalStatus = survivalStatus,
        growthStage = GrowthStage.ESTABLISHMENT,
        notes = "Test observation notes",
        photoFilenames = emptyList(),
        isDeleted = false,
        createdAt = Instant.now(),
        syncVersion = 1,
        lastSyncedAt = null,
        deviceId = "test-device"
    )

    fun createCultureRequest(
        species: String = "Test Species",
        explantType: String = "Leaf"
    ) = CreateCultureRequest(
        species = species,
        explantType = explantType,
        sourcePlantId = null,
        initiationDate = LocalDate.now(),
        mediumComposition = "MS medium",
        recipeId = null,
        initialConditions = "Standard conditions",
        initialObservation = "Initial setup completed",
        tags = emptyList()
    )

    private fun generateCultureId(): String {
        return "C${Random.nextInt(1, 9999).toString().padStart(3, '0')}"
    }
}
```

### **Custom Test Rules**

```kotlin
class TestCoroutineRule(
    private val testDispatcher: TestDispatcher = UnconfinedTestDispatcher()
) : TestWatcher() {

    override fun starting(description: Description) {
        Dispatchers.setMain(testDispatcher)
    }

    override fun finished(description: Description) {
        Dispatchers.resetMain()
    }
}

class PerformanceTestRule : TestWatcher() {
    private val executionTimes = mutableMapOf<String, Long>()

    fun measureTime(testName: String, block: () -> Unit) {
        val startTime = System.currentTimeMillis()
        block()
        val endTime = System.currentTimeMillis()
        executionTimes[testName] = endTime - startTime
    }

    fun getExecutionTime(testName: String): Long = executionTimes[testName] ?: 0L

    override fun finished(description: Description) {
        executionTimes.clear()
    }
}
```

## **Unit Test Best Practices**

### **Test Structure Guidelines**

1. **Use AAA Pattern:** Arrange, Act, Assert
2. **Descriptive Test Names:** `should_returnSuccess_when_validRequestProvided`
3. **One Assertion Per Test:** Focus on single behavior
4. **Test Edge Cases:** Null values, empty collections, boundary conditions

### **Mock Usage Standards**

1. **Mock External Dependencies:** Network, database, external services
2. **Don't Mock Value Objects:** Simple data classes and DTOs
3. **Verify Important Interactions:** Check method calls when behavior matters
4. **Use ArgumentCaptor:** When need to verify specific argument values

### **Performance Guidelines**

1. **Keep Tests Fast:** < 100ms execution time
2. **Minimize Test Setup:** Use lazy initialization where possible
3. **Parallel Execution:** Design tests to run concurrently
4. **Resource Cleanup:** Always close resources in @After methods

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After unit testing implementation and coverage validation