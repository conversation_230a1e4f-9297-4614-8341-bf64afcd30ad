# Integration Testing Strategy (20% of tests)

## Overview
This document defines the integration testing approach for CultureStack, focusing on component interactions, service workflows, and end-to-end data flow validation comprising 20% of the total test suite.

---

## **Integration Testing Goals**

### **Test Characteristics**
- **Coverage Target:** 100% of critical user workflows
- **Execution Speed:** < 5 seconds per test
- **Environment:** Android Test framework with real database
- **Scope:** Multi-component interactions and service integrations
- **Reliability:** 99%+ success rate with consistent environment

### **Integration Test Categories**

**1. Repository Integration Tests**
- Database operations with Room
- Service layer interactions
- Sync mechanism validation
- Transaction handling

**2. Service Integration Tests**
- End-to-end workflow testing
- Cross-component communication
- Event handling and messaging
- Business rule enforcement

**3. External Service Integration**
- Google Drive API integration
- Authentication flow testing
- Network resilience validation
- Offline-first behavior

## **Integration Test Framework Setup**

```kotlin
// build.gradle.kts (app module) - Integration Test Dependencies
dependencies {
    // Hilt Testing for DI
    androidTestImplementation("com.google.dagger:hilt-android-testing:${Versions.hilt}")
    kaptAndroidTest("com.google.dagger:hilt-android-compiler:${Versions.hilt}")

    // Android Test Framework
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test:rules:1.5.0")
    androidTestImplementation("androidx.test:runner:1.5.2")

    // Room Testing
    androidTestImplementation("androidx.room:room-testing:${Versions.room}")

    // Coroutines Testing
    androidTestImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")

    // Truth Assertions
    androidTestImplementation("com.google.truth:truth:1.1.5")

    // Test Orchestrator
    androidTestUtil("androidx.test:orchestrator:1.4.2")
}
```

## **Repository Integration Testing**

### **Culture Repository Integration Tests**

```kotlin
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class CultureRepositoryIntegrationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var cultureRepository: CultureRepository

    @Inject
    lateinit var observationRepository: ObservationRepository

    @Inject
    lateinit var database: CultureDatabase

    @Before
    fun init() {
        hiltRule.inject()
    }

    @After
    fun cleanup() {
        database.clearAllTables()
    }

    @Test
    fun fullCultureWorkflow_createsAndManagesCulture() = runTest {
        // Create culture
        val createRequest = CreateCultureRequest(
            species = "Integration Test Plant",
            explantType = "Leaf segment",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS + 2mg/L BAP",
            initialConditions = "25°C, 100μmol/m²/s, 16h photoperiod"
        )

        val createResult = cultureRepository.createCulture(createRequest)
        assertThat(createResult.isSuccess).isTrue()
        val culture = createResult.getOrThrow()

        // Verify culture was created with correct data
        assertThat(culture.species).isEqualTo(createRequest.species)
        assertThat(culture.explantType).isEqualTo(createRequest.explantType)
        assertThat(culture.status).isEqualTo(CultureStatus.HEALTHY)

        // Update status through repository
        val statusUpdate = cultureRepository.updateCultureStatus(
            culture.id,
            CultureStatus.READY_FOR_TRANSFER,
            "Visible growth after 3 weeks"
        )
        assertThat(statusUpdate.isSuccess).isTrue()

        // Verify persistence and sync version increment
        val retrievedCulture = cultureRepository.getCultureById(culture.id).getOrThrow()
        assertThat(retrievedCulture?.status).isEqualTo(CultureStatus.READY_FOR_TRANSFER)
        assertThat(retrievedCulture?.syncVersion).isEqualTo(2) // Should increment

        // Create subculture from parent
        val subcultureRequest = CreateSubcultureRequest(
            parentCultureId = culture.id,
            subcultureDate = LocalDate.now(),
            mediumComposition = "MS + 1mg/L IBA",
            explantCount = 5
        )

        val subcultureResult = cultureRepository.createSubculture(subcultureRequest)
        assertThat(subcultureResult.isSuccess).isTrue()

        // Verify lineage relationship
        val lineage = cultureRepository.getCultureLineage(culture.id).getOrThrow()
        assertThat(lineage.subcultures).hasSize(1)
        assertThat(lineage.subcultures[0].parentCultureId).isEqualTo(culture.id)
    }

    @Test
    fun cultureSearch_worksAcrossMultipleFields() = runTest {
        // Create test cultures with different attributes
        val orchid1 = CreateCultureRequest(
            species = "Dendrobium nobile",
            explantType = "Node",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard",
            tags = listOf("orchid", "easy")
        )

        val orchid2 = CreateCultureRequest(
            species = "Phalaenopsis amabilis",
            explantType = "Leaf",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard",
            tags = listOf("orchid", "challenging")
        )

        val rose = CreateCultureRequest(
            species = "Rosa damascena",
            explantType = "Shoot tip",
            initiationDate = LocalDate.now(),
            mediumComposition = "WPM medium",
            initialConditions = "Standard",
            tags = listOf("rose", "fragrant")
        )

        // Create all cultures
        val createdCultures = listOf(orchid1, orchid2, rose).map { request ->
            cultureRepository.createCulture(request).getOrThrow()
        }

        // Search by species
        val orchidSearch = CultureSearchQuery(
            species = "orchid",
            sortBy = SortBy.SPECIES,
            sortOrder = SortOrder.ASC
        )
        val orchidResults = cultureRepository.searchCultures(orchidSearch).getOrThrow()
        assertThat(orchidResults).hasSize(2)
        assertThat(orchidResults.map { it.species }).containsExactly(
            "Dendrobium nobile", "Phalaenopsis amabilis"
        )

        // Search by status with date range
        val healthySearch = CultureSearchQuery(
            status = CultureStatus.HEALTHY,
            dateRange = DateRange(
                startDate = LocalDate.now().minusDays(1),
                endDate = LocalDate.now().plusDays(1)
            )
        )
        val healthyResults = cultureRepository.searchCultures(healthySearch).getOrThrow()
        assertThat(healthyResults).hasSize(3) // All should be healthy

        // Search by tags
        val challengingSearch = CultureSearchQuery(
            tags = listOf("challenging")
        )
        val challengingResults = cultureRepository.searchCultures(challengingSearch).getOrThrow()
        assertThat(challengingResults).hasSize(1)
        assertThat(challengingResults[0].species).isEqualTo("Phalaenopsis amabilis")
    }

    @Test
    fun cultureWithObservations_maintainsDataConsistency() = runTest {
        // Create culture
        val cultureRequest = CreateCultureRequest(
            species = "Test Plant",
            explantType = "Leaf",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )
        val culture = cultureRepository.createCulture(cultureRequest).getOrThrow()

        // Add multiple observations
        val observations = listOf(
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now().minusDays(7),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.GOOD,
                growthStage = GrowthStage.INITIATION,
                notes = "Initial setup"
            ),
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now().minusDays(3),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.EXCELLENT,
                growthStage = GrowthStage.ESTABLISHMENT,
                notes = "Good growth"
            ),
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now(),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.EXCELLENT,
                growthStage = GrowthStage.MULTIPLICATION,
                notes = "Ready for subculturing"
            )
        )

        val createdObservations = observations.map { request ->
            observationRepository.createObservation(request).getOrThrow()
        }

        // Verify observation-culture relationships
        val cultureObservations = observationRepository.getObservationsForCulture(culture.id)
            .first() // Get first emission from Flow

        assertThat(cultureObservations).hasSize(3)
        assertThat(cultureObservations.map { it.observationDate }).isInOrder()

        // Test observation statistics calculation
        val stats = observationRepository.getObservationStats(culture.id).getOrThrow()
        assertThat(stats.totalObservations).isEqualTo(3)
        assertThat(stats.contaminationCount).isEqualTo(0)
        assertThat(stats.averageSurvivalRating).isGreaterThan(3.0)

        // Delete culture should soft delete observations
        val deleteResult = cultureRepository.deleteCulture(culture.id, cascade = true)
        assertThat(deleteResult.isSuccess).isTrue()

        // Verify observations are also marked as deleted
        val deletedCultureObservations = observationRepository.getObservationsForCulture(culture.id)
            .first()
        assertThat(deletedCultureObservations).isEmpty() // Should exclude deleted observations
    }

    @Test
    fun paginatedCultureRetrieval_worksCorrectly() = runTest {
        // Create 15 test cultures
        repeat(15) { index ->
            val request = CreateCultureRequest(
                species = "Test Plant $index",
                explantType = "Leaf",
                initiationDate = LocalDate.now().minusDays(index.toLong()),
                mediumComposition = "MS medium",
                initialConditions = "Standard"
            )
            cultureRepository.createCulture(request)
        }

        // Test first page
        val firstPage = cultureRepository.getCulturesPaginated(page = 0, pageSize = 5).getOrThrow()
        assertThat(firstPage.items).hasSize(5)
        assertThat(firstPage.page).isEqualTo(0)
        assertThat(firstPage.totalCount).isEqualTo(15)
        assertThat(firstPage.hasMore).isTrue()
        assertThat(firstPage.totalPages).isEqualTo(3)

        // Test second page
        val secondPage = cultureRepository.getCulturesPaginated(page = 1, pageSize = 5).getOrThrow()
        assertThat(secondPage.items).hasSize(5)
        assertThat(secondPage.hasMore).isTrue()

        // Ensure no overlap between pages
        val firstPageIds = firstPage.items.map { it.id }.toSet()
        val secondPageIds = secondPage.items.map { it.id }.toSet()
        assertThat(firstPageIds.intersect(secondPageIds)).isEmpty()

        // Test last page
        val lastPage = cultureRepository.getCulturesPaginated(page = 2, pageSize = 5).getOrThrow()
        assertThat(lastPage.items).hasSize(5)
        assertThat(lastPage.hasMore).isFalse()
    }

    @Test
    fun cultureAlerts_generatedCorrectly() = runTest {
        // Create culture that should generate alerts
        val oldCulture = CreateCultureRequest(
            species = "Neglected Plant",
            explantType = "Leaf",
            initiationDate = LocalDate.now().minusDays(30), // Old culture
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )
        val culture = cultureRepository.createCulture(oldCulture).getOrThrow()

        // Add old observation (should trigger overdue alert)
        val oldObservation = CreateObservationRequest(
            cultureId = culture.id,
            observationDate = LocalDate.now().minusDays(14), // 2 weeks ago
            contaminationStatus = false,
            survivalStatus = SurvivalStatus.FAIR,
            growthStage = GrowthStage.ESTABLISHMENT,
            notes = "Last observation"
        )
        observationRepository.createObservation(oldObservation)

        // Get cultures requiring attention
        val alertCultures = cultureRepository.getCulturesRequiringAttention().getOrThrow()

        assertThat(alertCultures).isNotEmpty()
        val alert = alertCultures.find { it.cultureId == culture.id }
        assertThat(alert).isNotNull()
        assertThat(alert?.alertType).isEqualTo(AlertType.OVERDUE_OBSERVATION)
        assertThat(alert?.severity).isIn(listOf(AlertSeverity.MEDIUM, AlertSeverity.HIGH))
    }
}
```

## **Service Layer Integration Testing**

### **Culture Service Integration Tests**

```kotlin
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class CultureServiceIntegrationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var cultureService: CultureService

    @Inject
    lateinit var observationService: ObservationService

    @Inject
    lateinit var notificationService: NotificationService

    @Inject
    lateinit var database: CultureDatabase

    @Before
    fun init() {
        hiltRule.inject()
    }

    @After
    fun cleanup() {
        database.clearAllTables()
    }

    @Test
    fun cultureWorkflowIntegration_completesSuccessfully() = runTest {
        // Create culture through service (full workflow)
        val request = CreateCultureRequest(
            species = "Service Test Plant",
            explantType = "Node",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard conditions",
            initialObservation = "Initial culture setup"
        )

        val workflowResult = cultureService.createCultureWorkflow(request)
        assertThat(workflowResult.isSuccess).isTrue()

        val result = workflowResult.getOrThrow()
        assertThat(result.isComplete).isTrue()
        assertThat(result.workflowSteps).hasSize(5) // All workflow steps completed

        // Verify all steps completed successfully
        val completedSteps = result.workflowSteps.filter { it.status == "completed" }
        assertThat(completedSteps).hasSize(5)

        // Verify culture was created
        assertThat(result.culture.species).isEqualTo(request.species)
        assertThat(result.culture.status).isEqualTo(CultureStatus.HEALTHY)

        // Verify initial observation was created
        val observations = observationService.getObservationsForCulture(result.culture.id)
            .first()
        assertThat(observations).hasSize(1)
        assertThat(observations[0].notes).contains("Initial culture setup")

        // Verify next recommended actions are provided
        assertThat(result.nextRecommendedActions).isNotEmpty()
        val firstAction = result.nextRecommendedActions.first()
        assertThat(firstAction.type).isEqualTo(ActionType.SCHEDULE_OBSERVATION)
    }

    @Test
    fun statusTransitionWorkflow_enforcesBusinessRules() = runTest {
        // Create initial culture
        val cultureRequest = CreateCultureRequest(
            species = "Status Test Plant",
            explantType = "Leaf",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )
        val workflowResult = cultureService.createCultureWorkflow(cultureRequest)
        val culture = workflowResult.getOrThrow().culture

        // Test valid status transition
        val validTransition = StatusTransitionRequest(
            newStatus = CultureStatus.READY_FOR_TRANSFER,
            notes = "Good growth observed after 3 weeks"
        )

        val transitionResult = cultureService.transitionCultureStatus(culture.id, validTransition)
        assertThat(transitionResult.isSuccess).isTrue()

        val result = transitionResult.getOrThrow()
        assertThat(result.oldStatus).isEqualTo(CultureStatus.HEALTHY)
        assertThat(result.newStatus).isEqualTo(CultureStatus.READY_FOR_TRANSFER)
        assertThat(result.triggeredActions).isNotEmpty()

        // Test invalid status transition (should fail business rule validation)
        val invalidTransition = StatusTransitionRequest(
            newStatus = CultureStatus.DISPOSED,
            notes = "Direct disposal without proper steps"
        )

        val invalidResult = cultureService.transitionCultureStatus(culture.id, invalidTransition)
        // This might succeed depending on business rules - adjust based on actual rules
        // assertThat(invalidResult.isFailure).isTrue()
    }

    @Test
    fun batchSubcultureCreation_createsAndTracksProgress() = runTest {
        // Create parent culture
        val parentRequest = CreateCultureRequest(
            species = "Parent Plant",
            explantType = "Shoot tip",
            initiationDate = LocalDate.now().minusWeeks(4),
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )
        val parentWorkflow = cultureService.createCultureWorkflow(parentRequest)
        val parentCulture = parentWorkflow.getOrThrow().culture

        // Update parent to ready for transfer
        val readyTransition = StatusTransitionRequest(
            newStatus = CultureStatus.READY_FOR_TRANSFER,
            notes = "Ready for subculturing"
        )
        cultureService.transitionCultureStatus(parentCulture.id, readyTransition)

        // Create batch subcultures
        val batchRequest = BatchSubcultureRequest(
            parentCultureId = parentCulture.id,
            count = 5,
            subcultureDate = LocalDate.now(),
            mediumComposition = "MS + 1mg/L IBA",
            explantCountPerSubculture = 3,
            notes = "First subculture batch"
        )

        val batchResult = cultureService.createBatchSubcultures(batchRequest)
        assertThat(batchResult.isSuccess).isTrue()

        val batchHandle = batchResult.getOrThrow()
        assertThat(batchHandle.operationType).isEqualTo(BatchOperationType.SUBCULTURE_CREATION)
        assertThat(batchHandle.totalItems).isEqualTo(5)

        // Wait for batch completion (in real implementation, this would be async)
        // For integration test, we might need to poll or use test synchronization

        // Verify all subcultures were created
        // This would require additional service method to get subcultures by parent
        val parentLineage = cultureService.getCultureLineage(parentCulture.id).getOrThrow()
        assertThat(parentLineage.subcultures).hasSize(5)

        parentLineage.subcultures.forEach { subculture ->
            assertThat(subculture.parentCultureId).isEqualTo(parentCulture.id)
            assertThat(subculture.mediumComposition).contains("IBA")
        }
    }

    @Test
    fun culturePerformanceAnalysis_calculatesCorrectMetrics() = runTest {
        // Create culture with observation history
        val cultureRequest = CreateCultureRequest(
            species = "Analysis Test Plant",
            explantType = "Leaf",
            initiationDate = LocalDate.now().minusDays(21),
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )
        val workflowResult = cultureService.createCultureWorkflow(cultureRequest)
        val culture = workflowResult.getOrThrow().culture

        // Add progression of observations showing improvement
        val observations = listOf(
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now().minusDays(14),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.FAIR,
                growthStage = GrowthStage.INITIATION,
                notes = "Slow start"
            ),
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now().minusDays(7),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.GOOD,
                growthStage = GrowthStage.ESTABLISHMENT,
                notes = "Showing improvement"
            ),
            CreateObservationRequest(
                cultureId = culture.id,
                observationDate = LocalDate.now(),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.EXCELLENT,
                growthStage = GrowthStage.MULTIPLICATION,
                notes = "Excellent growth"
            )
        )

        observations.forEach { request ->
            observationService.addObservation(request)
        }

        // Analyze performance
        val analysisResult = cultureService.analyzeCulturePerformance(culture.id)
        assertThat(analysisResult.isSuccess).isTrue()

        val analysis = analysisResult.getOrThrow()
        assertThat(analysis.cultureId).isEqualTo(culture.id)
        assertThat(analysis.observationCount).isEqualTo(4) // 3 manual + 1 initial
        assertThat(analysis.daysActive).isEqualTo(21)
        assertThat(analysis.averageSurvivalRating).isGreaterThan(2.5) // Should show improvement
        assertThat(analysis.contaminationRate).isEqualTo(0.0)
        assertThat(analysis.recommendations).isNotEmpty()

        // Verify performance trend
        assertThat(analysis.performanceTrend).isEqualTo(PerformanceTrend.IMPROVING)
    }

    @Test
    fun cultureRecommendations_providesPersonalizedSuggestions() = runTest {
        val userId = "test-user"

        // Create diverse culture portfolio for user
        val cultureRequests = listOf(
            CreateCultureRequest(
                species = "Successful Orchid",
                explantType = "Node",
                initiationDate = LocalDate.now().minusMonths(2),
                mediumComposition = "MS + Orchid formula",
                initialConditions = "Standard"
            ),
            CreateCultureRequest(
                species = "Challenging Rose",
                explantType = "Shoot tip",
                initiationDate = LocalDate.now().minusWeeks(6),
                mediumComposition = "WPM medium",
                initialConditions = "Cool temperature"
            )
        )

        // Create cultures and simulate success/challenges
        val cultures = cultureRequests.map { request ->
            cultureService.createCultureWorkflow(request).getOrThrow().culture
        }

        // Simulate successful orchid progression
        cultureService.transitionCultureStatus(
            cultures[0].id,
            StatusTransitionRequest(CultureStatus.READY_FOR_TRANSFER, "Great success")
        )

        // Add challenging rose observations
        observationService.addObservation(
            CreateObservationRequest(
                cultureId = cultures[1].id,
                observationDate = LocalDate.now(),
                contaminationStatus = false,
                survivalStatus = SurvivalStatus.POOR,
                growthStage = GrowthStage.ESTABLISHMENT,
                notes = "Struggling with medium"
            )
        )

        // Get personalized recommendations
        val recommendationsResult = cultureService.getCultureRecommendations(userId, limit = 5)
        assertThat(recommendationsResult.isSuccess).isTrue()

        val recommendations = recommendationsResult.getOrThrow()
        assertThat(recommendations).isNotEmpty()

        // Should recommend similar successful approaches
        val orchidRecommendation = recommendations.find {
            it.recommendationType == RecommendationType.SIMILAR_SUCCESS
        }
        assertThat(orchidRecommendation).isNotNull()
        assertThat(orchidRecommendation?.description).contains("Orchid")

        // Should suggest improvements for struggling cultures
        val improvementRecommendation = recommendations.find {
            it.recommendationType == RecommendationType.IMPROVEMENT_SUGGESTION
        }
        assertThat(improvementRecommendation).isNotNull()
    }
}
```

## **Database Transaction Integration Testing**

### **Transaction Consistency Tests**

```kotlin
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class DatabaseTransactionIntegrationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @Inject
    lateinit var database: CultureDatabase

    @Inject
    lateinit var cultureRepository: CultureRepository

    @Test
    fun complexTransaction_maintainsConsistency() = runTest {
        val request = CreateCultureRequest(
            species = "Transaction Test Plant",
            explantType = "Leaf",
            initiationDate = LocalDate.now(),
            mediumComposition = "MS medium",
            initialConditions = "Standard"
        )

        // This should be wrapped in a transaction internally
        val result = cultureRepository.createCulture(request)
        assertThat(result.isSuccess).isTrue()

        val culture = result.getOrThrow()

        // Verify all related data was created atomically
        assertThat(culture.id).isNotEmpty()
        assertThat(culture.cultureId).isNotEmpty()
        assertThat(culture.syncVersion).isEqualTo(1)

        // Test that partial failures rollback properly
        // This would require injecting failure scenarios in service layer
    }

    @Test
    fun batchOperations_handlePartialFailures() = runTest {
        val requests = listOf(
            CreateCultureRequest(
                species = "Valid Plant 1",
                explantType = "Leaf",
                initiationDate = LocalDate.now(),
                mediumComposition = "MS medium",
                initialConditions = "Standard"
            ),
            CreateCultureRequest(
                species = "", // Invalid - should cause validation failure
                explantType = "Leaf",
                initiationDate = LocalDate.now().plusDays(1), // Invalid date
                mediumComposition = "",
                initialConditions = ""
            ),
            CreateCultureRequest(
                species = "Valid Plant 2",
                explantType = "Node",
                initiationDate = LocalDate.now(),
                mediumComposition = "MS medium",
                initialConditions = "Standard"
            )
        )

        // Test batch creation with mixed valid/invalid requests
        val results = requests.map { request ->
            cultureRepository.createCulture(request)
        }

        // Verify results match expectations
        assertThat(results[0].isSuccess).isTrue()
        assertThat(results[1].isFailure).isTrue()
        assertThat(results[2].isSuccess).isTrue()

        // Verify database state is consistent
        val activeCultures = cultureRepository.getActiveCultures().first()
        assertThat(activeCultures).hasSize(2) // Only valid cultures should be created
    }
}
```

## **Mock External Services for Integration Tests**

### **Mock Google Drive Service**

```kotlin
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [DriveServiceModule::class]
)
@Module
object MockDriveServiceModule {

    @Provides
    @Singleton
    fun provideMockDriveService(): DriveApiService = MockDriveApiService()
}

class MockDriveApiService : DriveApiService {

    private val uploadedFiles = mutableMapOf<String, ByteArray>()
    private var simulateNetworkError = false
    private var uploadDelay = 0L

    fun setNetworkError(shouldError: Boolean) {
        simulateNetworkError = shouldError
    }

    fun setUploadDelay(delayMs: Long) {
        uploadDelay = delayMs
    }

    override suspend fun uploadCultureData(culture: Culture): DriveUploadResult {
        delay(uploadDelay)

        if (simulateNetworkError) {
            throw IOException("Mock network error")
        }

        val fileId = "mock-file-${culture.id}"
        val data = JsonSerializer.serialize(culture).toByteArray()
        uploadedFiles[fileId] = data

        return DriveUploadResult.success(fileId, "${culture.cultureId}.json", data.size.toLong())
    }

    override suspend fun downloadCultureData(cultureId: String): Culture {
        delay(uploadDelay)

        if (simulateNetworkError) {
            throw IOException("Mock network error")
        }

        // Return mock culture data
        return TestDataFactory.createCulture(id = cultureId)
    }

    fun getUploadedFileCount(): Int = uploadedFiles.size

    fun clearUploads() {
        uploadedFiles.clear()
    }
}
```

## **Integration Test Best Practices**

### **Test Isolation**
1. **Clean Database State:** Clear all tables between tests
2. **Independent Test Data:** Each test creates its own data
3. **Mock External Services:** Use test doubles for network calls
4. **Consistent Environment:** Use same device/emulator configuration

### **Performance Considerations**
1. **Minimize Test Data:** Create only necessary test data
2. **Parallel Safe:** Design tests to run concurrently when possible
3. **Resource Cleanup:** Always clean up created resources
4. **Timeout Handling:** Set appropriate timeouts for async operations

### **Reliability Standards**
1. **Deterministic Results:** Tests should always produce same results
2. **No Network Dependencies:** Mock all external service calls
3. **Environment Independence:** Tests work on any device/emulator
4. **Error Scenario Testing:** Test both success and failure paths

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After integration testing implementation and workflow validation