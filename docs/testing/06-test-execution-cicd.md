# Test Execution & CI/CD Integration

## Overview
This document defines the test execution infrastructure, CI/CD pipeline integration, and automated testing workflows for CultureStack, ensuring consistent quality validation across all development stages.

---

## **Local Testing Setup**

### **Gradle Test Configuration**

```gradle
// build.gradle.kts (app module)

android {
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }

        animationsDisabled = true

        // Use Orchestrator for instrumented tests
        execution = "ANDROIDX_TEST_ORCHESTRATOR"
    }

    // Test build type configuration
    buildTypes {
        debug {
            testCoverageEnabled = true
            isDebuggable = true
        }
    }
}

// Custom test tasks
tasks.register("runAllTests") {
    group = "verification"
    description = "Runs all test types in sequence"

    dependsOn("testDebugUnitTest")
    dependsOn("connectedDebugAndroidTest")
    dependsOn("jacocoTestReport")

    doLast {
        println("✅ All tests completed successfully")
        println("📊 Coverage report generated in build/reports/jacoco/")
    }
}

tasks.register("runUnitTests") {
    group = "verification"
    description = "Runs unit tests only"
    dependsOn("testDebugUnitTest")
}

tasks.register("runIntegrationTests") {
    group = "verification"
    description = "Runs instrumented tests only"
    dependsOn("connectedDebugAndroidTest")
}

tasks.register("runPerformanceTests") {
    group = "verification"
    description = "Runs performance-focused tests"
    dependsOn("testDebugUnitTest")

    doFirst {
        systemProperty("test.performance.enabled", true)
    }
}

// Test execution optimization
tasks.withType<Test> {
    // Parallel execution
    maxParallelForks = Runtime.getRuntime().availableProcessors()

    // JVM options for test execution
    jvmArgs = listOf(
        "-XX:MaxMetaspaceSize=512m",
        "-XX:+HeapDumpOnOutOfMemoryError",
        "-Xms512m",
        "-Xmx2g"
    )

    // Test logging configuration
    testLogging {
        events = setOf(
            TestLogEvent.PASSED,
            TestLogEvent.SKIPPED,
            TestLogEvent.FAILED,
            TestLogEvent.STANDARD_OUT,
            TestLogEvent.STANDARD_ERROR
        )
        exceptionFormat = TestExceptionFormat.FULL
        showStandardStreams = false
    }

    // Test result handling
    afterSuite(KotlinClosure2<TestDescriptor, TestResult, Unit>({ desc, result ->
        if (desc.parent == null) {
            println("Test Results: ${result.resultType} " +
                   "(${result.testCount} tests, " +
                   "${result.successfulTestCount} passed, " +
                   "${result.failedTestCount} failed, " +
                   "${result.skippedTestCount} skipped)")
        }
    }))
}

// Android Test configuration
tasks.withType<com.android.build.gradle.internal.tasks.AndroidTestTask> {
    // Device selection and configuration
    doFirst {
        adbOptions {
            timeOutInMs = 10 * 60 * 1000 // 10 minutes
            installOptions("-r", "-d")
        }
    }
}
```

### **Test Coverage Configuration**

```gradle
// JaCoCo coverage configuration
apply(plugin = "jacoco")

jacoco {
    toolVersion = "0.8.8"
}

tasks.withType<Test> {
    finalizedBy(tasks.jacocoTestReport)
}

tasks.jacocoTestReport {
    dependsOn(tasks.test)

    reports {
        xml.required.set(true)
        html.required.set(true)
        csv.required.set(false)
    }

    // Exclusions for generated code and framework classes
    val fileFilter = listOf(
        "**/R.class",
        "**/R$*.class",
        "**/BuildConfig.*",
        "**/Manifest*.*",
        "**/*Test*.*",
        "android/**/*.*",
        "**/databinding/*",
        "**/di/*",
        "**/*_Hilt*",
        "**/*_Factory*",
        "**/*_MembersInjector*",
        "**/Dagger*Component*",
        "**/*Module_*Factory*"
    )

    val debugTree = fileTree("${buildDir}/tmp/kotlin-classes/debug") {
        exclude(fileFilter)
    }

    val mainSrc = "${project.projectDir}/src/main/kotlin"

    sourceDirectories.setFrom(files(mainSrc))
    classDirectories.setFrom(files(debugTree))
    executionData.setFrom(fileTree(buildDir) {
        include("jacoco/testDebugUnitTest.exec")
        include("outputs/unit_test_code_coverage/debugUnitTest/testDebugUnitTest.exec")
    })
}

// Coverage verification
tasks.jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = "0.80".toBigDecimal() // 80% minimum coverage
            }
        }

        rule {
            element = "CLASS"
            excludes = listOf(
                "*.di.*",
                "*.*Activity",
                "*.*Fragment",
                "*.databinding.*"
            )
            limit {
                counter = "LINE"
                value = "COVEREDRATIO"
                minimum = "0.75".toBigDecimal()
            }
        }
    }
}

tasks.check {
    dependsOn(tasks.jacocoTestCoverageVerification)
}
```

## **CI/CD Pipeline Configuration**

### **GitHub Actions Workflow**

```yaml
# .github/workflows/comprehensive-testing.yml

name: Comprehensive Testing Pipeline

on:
  pull_request:
    branches: [main, develop]
  push:
    branches: [main, develop]
  schedule:
    - cron: '0 6 * * *' # Daily at 6 AM UTC

env:
  GRADLE_OPTS: -Dorg.gradle.jvmargs="-Xmx4g -XX:MaxMetaspaceSize=1g"

jobs:
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Run lint checks
        run: ./gradlew lintDebug

      - name: Run Detekt static analysis
        run: ./gradlew detekt

      - name: Upload lint results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: lint-results
          path: |
            app/build/reports/lint-results-debug.html
            app/build/reports/lint-results-debug.xml

      - name: Upload Detekt results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: detekt-results
          path: build/reports/detekt/

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Run unit tests
        run: ./gradlew testDebugUnitTest --continue

      - name: Generate test report
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Unit Test Results
          path: '**/build/test-results/testDebugUnitTest/TEST-*.xml'
          reporter: java-junit
          fail-on-error: true

      - name: Generate coverage report
        run: ./gradlew jacocoTestReport

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: '**/build/reports/jacoco/testDebugUnitTestCoverage/testDebugUnitTestCoverage.xml'
          flags: unit-tests
          name: unit-test-coverage

      - name: Coverage verification
        run: ./gradlew jacocoTestCoverageVerification

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: unit-test-results
          path: |
            **/build/test-results/testDebugUnitTest/
            **/build/reports/tests/testDebugUnitTest/
            **/build/reports/jacoco/

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 45
    strategy:
      matrix:
        api-level: [24, 30, 34]
        arch: [x86_64]
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm

      - name: Cache AVD
        uses: actions/cache@v3
        id: avd-cache
        with:
          path: |
            ~/.android/avd/*
            ~/.android/adb*
          key: avd-${{ matrix.api-level }}-${{ matrix.arch }}

      - name: Create AVD and generate snapshot for caching
        if: steps.avd-cache.outputs.cache-hit != 'true'
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: ${{ matrix.api-level }}
          arch: ${{ matrix.arch }}
          force-avd-creation: false
          emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: false
          script: echo "Generated AVD snapshot for caching."

      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Run integration tests
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: ${{ matrix.api-level }}
          arch: ${{ matrix.arch }}
          force-avd-creation: false
          emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
          disable-animations: true
          script: |
            adb devices
            ./gradlew connectedDebugAndroidTest --continue

      - name: Generate integration test report
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Integration Test Results (API ${{ matrix.api-level }})
          path: '**/build/outputs/androidTest-results/connected/TEST-*.xml'
          reporter: java-junit
          fail-on-error: false

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: integration-test-results-api-${{ matrix.api-level }}
          path: |
            **/build/reports/androidTests/
            **/build/outputs/androidTest-results/
            **/build/outputs/connected_android_test_additional_output/

  ui-tests:
    name: UI Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [unit-tests, integration-tests]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Enable KVM group perms
        run: |
          echo 'KERNEL=="kvm", GROUP="kvm", MODE="0666", OPTIONS+="static_node=kvm"' | sudo tee /etc/udev/rules.d/99-kvm4all.rules
          sudo udevadm control --reload-rules
          sudo udevadm trigger --name-match=kvm

      - name: Run UI tests
        uses: reactivecircus/android-emulator-runner@v2
        with:
          api-level: 30
          arch: x86_64
          force-avd-creation: false
          emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim
          disable-animations: true
          script: |
            ./gradlew connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.package=com.culturestack.ui

      - name: Upload UI test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: ui-test-results
          path: |
            **/build/reports/androidTests/
            **/build/outputs/androidTest-results/

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Run performance tests
        run: ./gradlew runPerformanceTests

      - name: Performance regression check
        run: |
          echo "Checking performance regression..."
          # Implementation would compare current results with baseline
          ./scripts/check-performance-regression.sh

      - name: Upload performance results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: performance-test-results
          path: |
            **/build/reports/performance/
            performance-baseline.json

  build-validation:
    name: Build Validation
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests]
    timeout-minutes: 20

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Build debug APK
        run: ./gradlew assembleDebug

      - name: Build release APK
        run: ./gradlew assembleRelease

      - name: Run bundle validation
        run: ./gradlew bundleReleaseClassesToCompileJar

      - name: Upload APK artifacts
        uses: actions/upload-artifact@v3
        with:
          name: apk-artifacts
          path: |
            app/build/outputs/apk/debug/*.apk
            app/build/outputs/apk/release/*.apk

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

      - name: Run security scan with Semgrep
        uses: returntocorp/semgrep-action@v1
        with:
          config: auto

      - name: Run OWASP Dependency Check
        run: ./gradlew dependencyCheckAnalyze

      - name: Upload security scan results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: security-scan-results
          path: |
            build/reports/dependency-check-report.html
            semgrep-results.json

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, ui-tests, performance-tests, build-validation, security-scan]
    if: always()
    timeout-minutes: 5

    steps:
      - name: Download all test artifacts
        uses: actions/download-artifact@v3

      - name: Generate comprehensive test report
        run: |
          echo "# 🧪 Comprehensive Test Summary" >> test-summary.md
          echo "" >> test-summary.md
          echo "## Test Execution Results" >> test-summary.md

          if [ "${{ needs.unit-tests.result }}" == "success" ]; then
            echo "✅ Unit Tests: PASSED" >> test-summary.md
          else
            echo "❌ Unit Tests: FAILED" >> test-summary.md
          fi

          if [ "${{ needs.integration-tests.result }}" == "success" ]; then
            echo "✅ Integration Tests: PASSED" >> test-summary.md
          else
            echo "❌ Integration Tests: FAILED" >> test-summary.md
          fi

          if [ "${{ needs.ui-tests.result }}" == "success" ]; then
            echo "✅ UI Tests: PASSED" >> test-summary.md
          else
            echo "❌ UI Tests: FAILED" >> test-summary.md
          fi

          if [ "${{ needs.performance-tests.result }}" == "success" ]; then
            echo "✅ Performance Tests: PASSED" >> test-summary.md
          else
            echo "❌ Performance Tests: FAILED" >> test-summary.md
          fi

          echo "" >> test-summary.md
          echo "## Build Validation" >> test-summary.md

          if [ "${{ needs.build-validation.result }}" == "success" ]; then
            echo "✅ Build Validation: PASSED" >> test-summary.md
          else
            echo "❌ Build Validation: FAILED" >> test-summary.md
          fi

          if [ "${{ needs.security-scan.result }}" == "success" ]; then
            echo "✅ Security Scan: PASSED" >> test-summary.md
          else
            echo "❌ Security Scan: FAILED" >> test-summary.md
          fi

      - name: Comment test summary on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const testSummary = fs.readFileSync('test-summary.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: testSummary
            });

      - name: Upload test summary
        uses: actions/upload-artifact@v3
        with:
          name: test-summary
          path: test-summary.md
```

## **Test Execution Optimization**

### **Parallel Test Execution**

```gradle
// gradle.properties
org.gradle.parallel=true
org.gradle.workers.max=4
org.gradle.configureondemand=true
org.gradle.caching=true
org.gradle.daemon=true

// Test-specific optimizations
systemProp.junit.jupiter.execution.parallel.enabled=true
systemProp.junit.jupiter.execution.parallel.mode.default=concurrent
systemProp.junit.jupiter.execution.parallel.mode.classes.default=concurrent
```

### **Test Sharding Configuration**

```kotlin
// For large test suites, implement test sharding
android {
    testOptions {
        execution = "ANDROIDX_TEST_ORCHESTRATOR"

        animationsDisabled = true

        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true

            all {
                // Enable parallel execution
                maxParallelForks = Runtime.getRuntime().availableProcessors()

                // Test sharding based on package
                if (project.hasProperty("testShard")) {
                    val shard = project.property("testShard") as String
                    include("**/*${shard}*Test.class")
                }

                // Memory optimization
                forkEvery = 100
                minHeapSize = "512m"
                maxHeapSize = "2048m"
                jvmArgs("-XX:MaxPermSize=512m")
            }
        }
    }
}
```

## **Local Development Scripts**

### **Test Execution Scripts**

```bash
#!/bin/bash
# scripts/run-tests.sh

set -e

echo "🧪 Starting CultureStack Test Suite"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${2}${1}${NC}"
}

# Parse command line arguments
RUN_UNIT=true
RUN_INTEGRATION=true
RUN_UI=false
GENERATE_COVERAGE=true

while [[ $# -gt 0 ]]; do
    case $1 in
        --unit-only)
            RUN_INTEGRATION=false
            RUN_UI=false
            shift
            ;;
        --integration-only)
            RUN_UNIT=false
            RUN_UI=false
            shift
            ;;
        --ui-only)
            RUN_UNIT=false
            RUN_INTEGRATION=false
            shift
            ;;
        --no-coverage)
            GENERATE_COVERAGE=false
            shift
            ;;
        --with-ui)
            RUN_UI=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Ensure gradlew is executable
chmod +x gradlew

# Clean previous test results
print_status "🧹 Cleaning previous test results..." $YELLOW
./gradlew clean

# Run unit tests
if [ "$RUN_UNIT" = true ]; then
    print_status "🔬 Running unit tests..." $YELLOW
    if ./gradlew testDebugUnitTest; then
        print_status "✅ Unit tests passed" $GREEN
    else
        print_status "❌ Unit tests failed" $RED
        exit 1
    fi
fi

# Run integration tests
if [ "$RUN_INTEGRATION" = true ]; then
    print_status "🔗 Running integration tests..." $YELLOW

    # Check if emulator is running
    if ! adb devices | grep -q "emulator"; then
        print_status "⚠️ No emulator detected. Starting emulator..." $YELLOW
        # Start emulator in background
        $ANDROID_HOME/emulator/emulator -avd test_avd -no-window -no-audio &

        # Wait for emulator to boot
        adb wait-for-device
        adb shell 'while [[ -z $(getprop sys.boot_completed) ]]; do sleep 1; done;'
        print_status "📱 Emulator ready" $GREEN
    fi

    if ./gradlew connectedDebugAndroidTest; then
        print_status "✅ Integration tests passed" $GREEN
    else
        print_status "❌ Integration tests failed" $RED
        exit 1
    fi
fi

# Run UI tests
if [ "$RUN_UI" = true ]; then
    print_status "📱 Running UI tests..." $YELLOW
    if ./gradlew connectedDebugAndroidTest -Pandroid.testInstrumentationRunnerArguments.package=com.culturestack.ui; then
        print_status "✅ UI tests passed" $GREEN
    else
        print_status "❌ UI tests failed" $RED
        exit 1
    fi
fi

# Generate coverage report
if [ "$GENERATE_COVERAGE" = true ] && [ "$RUN_UNIT" = true ]; then
    print_status "📊 Generating coverage report..." $YELLOW
    ./gradlew jacocoTestReport

    # Open coverage report
    if command -v xdg-open &> /dev/null; then
        xdg-open app/build/reports/jacoco/testDebugUnitTestCoverage/html/index.html
    elif command -v open &> /dev/null; then
        open app/build/reports/jacoco/testDebugUnitTestCoverage/html/index.html
    fi

    print_status "✅ Coverage report generated" $GREEN
fi

print_status "🎉 All tests completed successfully!" $GREEN
```

### **Performance Regression Check**

```bash
#!/bin/bash
# scripts/check-performance-regression.sh

set -e

BASELINE_FILE="performance-baseline.json"
CURRENT_RESULTS="build/reports/performance/current-results.json"
THRESHOLD=20 # 20% regression threshold

if [ ! -f "$BASELINE_FILE" ]; then
    echo "⚠️ No baseline file found. Creating initial baseline..."
    cp "$CURRENT_RESULTS" "$BASELINE_FILE"
    echo "✅ Baseline created"
    exit 0
fi

echo "🔍 Checking for performance regressions..."

# Compare current results with baseline using jq
python3 << EOF
import json
import sys

def load_json(file_path):
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        sys.exit(1)

baseline = load_json("$BASELINE_FILE")
current = load_json("$CURRENT_RESULTS")

regressions = []
improvements = []

for test_name in current.get('performance_tests', {}):
    baseline_time = baseline.get('performance_tests', {}).get(test_name, {}).get('execution_time_ms', 0)
    current_time = current.get('performance_tests', {}).get(test_name, {}).get('execution_time_ms', 0)

    if baseline_time > 0:
        change_percent = ((current_time - baseline_time) / baseline_time) * 100

        if change_percent > $THRESHOLD:
            regressions.append({
                'test': test_name,
                'baseline': baseline_time,
                'current': current_time,
                'change': change_percent
            })
        elif change_percent < -10:  # 10% improvement
            improvements.append({
                'test': test_name,
                'baseline': baseline_time,
                'current': current_time,
                'change': change_percent
            })

if regressions:
    print("❌ Performance regressions detected:")
    for reg in regressions:
        print(f"  - {reg['test']}: {reg['baseline']}ms → {reg['current']}ms ({reg['change']:.1f}%)")
    sys.exit(1)

if improvements:
    print("✅ Performance improvements detected:")
    for imp in improvements:
        print(f"  + {imp['test']}: {imp['baseline']}ms → {imp['current']}ms ({imp['change']:.1f}%)")

print("✅ No significant performance regressions detected")
EOF

echo "📊 Performance check completed"
```

## **Test Result Reporting**

### **Slack Notification Integration**

```yaml
# .github/workflows/test-notifications.yml

name: Test Result Notifications

on:
  workflow_run:
    workflows: ["Comprehensive Testing Pipeline"]
    types: [completed]

jobs:
  notify:
    runs-on: ubuntu-latest
    if: always()

    steps:
      - name: Send Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          custom_payload: |
            {
              "text": "CultureStack Test Results",
              "attachments": [
                {
                  "color": "${{ job.status == 'success' && 'good' || 'danger' }}",
                  "fields": [
                    {
                      "title": "Repository",
                      "value": "${{ github.repository }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "${{ github.event.workflow_run.conclusion }}",
                      "short": true
                    },
                    {
                      "title": "Workflow",
                      "value": "${{ github.event.workflow_run.name }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### **Test Metrics Collection**

```kotlin
// Test metrics collection for analytics
data class TestExecutionMetrics(
    val totalTests: Int,
    val passedTests: Int,
    val failedTests: Int,
    val skippedTests: Int,
    val executionTimeMs: Long,
    val coveragePercentage: Double,
    val testCategories: Map<String, TestCategoryMetrics>
)

data class TestCategoryMetrics(
    val categoryName: String,
    val testCount: Int,
    val averageExecutionTime: Double,
    val successRate: Double
)

// Custom test rule to collect metrics
class TestMetricsRule : TestWatcher() {
    private val startTime = System.currentTimeMillis()
    private val metrics = mutableListOf<TestResult>()

    override fun succeeded(description: Description) {
        val duration = System.currentTimeMillis() - startTime
        metrics.add(TestResult(description.methodName, "PASSED", duration))
    }

    override fun failed(e: Throwable?, description: Description) {
        val duration = System.currentTimeMillis() - startTime
        metrics.add(TestResult(description.methodName, "FAILED", duration, e?.message))
    }

    fun getMetrics(): List<TestResult> = metrics.toList()
}
```

## **Quality Gates and Thresholds**

### **Automated Quality Checks**

```kotlin
// Quality gate configuration
object QualityGates {
    const val MIN_CODE_COVERAGE = 80.0
    const val MIN_DOMAIN_COVERAGE = 95.0
    const val MAX_TEST_EXECUTION_TIME_MS = 300_000L // 5 minutes
    const val MAX_ACCEPTABLE_FAILURES = 0
    const val MIN_TEST_SUCCESS_RATE = 99.0

    fun validateTestResults(results: TestExecutionMetrics): ValidationResult {
        val violations = mutableListOf<String>()

        if (results.coveragePercentage < MIN_CODE_COVERAGE) {
            violations.add("Code coverage ${results.coveragePercentage}% below minimum ${MIN_CODE_COVERAGE}%")
        }

        if (results.failedTests > MAX_ACCEPTABLE_FAILURES) {
            violations.add("${results.failedTests} test failures exceed maximum ${MAX_ACCEPTABLE_FAILURES}")
        }

        val successRate = (results.passedTests.toDouble() / results.totalTests) * 100
        if (successRate < MIN_TEST_SUCCESS_RATE) {
            violations.add("Test success rate ${successRate}% below minimum ${MIN_TEST_SUCCESS_RATE}%")
        }

        if (results.executionTimeMs > MAX_TEST_EXECUTION_TIME_MS) {
            violations.add("Test execution time ${results.executionTimeMs}ms exceeds maximum ${MAX_TEST_EXECUTION_TIME_MS}ms")
        }

        return ValidationResult(violations.isEmpty(), violations)
    }
}
```

---

**Document Version:** v1.0
**Last Updated:** 2025-09-25
**Next Review:** After CI/CD pipeline implementation and optimization