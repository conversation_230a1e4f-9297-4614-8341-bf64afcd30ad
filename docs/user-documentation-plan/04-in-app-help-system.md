# **In-App Help System**

## **Contextual Help Framework**

```kotlin
/**
 * In-app help system architecture
 */
@Singleton
class HelpSystemManager @Inject constructor(
    private val helpContentRepository: HelpContentRepository,
    private val userProgressTracker: UserProgressTracker,
    private val analyticsService: AnalyticsService
) {

    /**
     * Shows contextual help for current screen
     * @param screenId Current screen identifier
     * @param userContext User's experience level and progress
     * @return Relevant help content
     */
    suspend fun getContextualHelp(
        screenId: String,
        userContext: UserContext
    ): List<HelpItem> {

        val baseHelp = helpContentRepository.getHelpForScreen(screenId)
        val personalizedHelp = personalizeHelpContent(baseHelp, userContext)

        // Track help content views
        analyticsService.trackHelpContentViewed(screenId, personalizedHelp.map { it.id })

        return personalizedHelp
    }

    /**
     * Triggers smart help suggestions based on user behavior
     * @param userAction Current user action
     * @param context Additional context
     * @return Suggested help content
     */
    suspend fun triggerSmartHelp(
        userAction: UserAction,
        context: Map<String, Any>
    ): HelpSuggestion? {

        return when {
            // First-time culture creation
            userAction == UserAction.CREATE_CULTURE &&
            userProgressTracker.getCultureCount() == 0 -> {
                HelpSuggestion(
                    type = HelpType.INTERACTIVE_TUTORIAL,
                    title = "Creating Your First Culture",
                    description = "Let's walk through creating your first tissue culture step-by-step",
                    priority = Priority.HIGH
                )
            }

            // Contamination detected
            userAction == UserAction.ADD_OBSERVATION &&
            context["contamination"] == true -> {
                HelpSuggestion(
                    type = HelpType.TROUBLESHOOTING,
                    title = "Dealing with Contamination",
                    description = "Learn how to identify and handle contaminated cultures",
                    priority = Priority.URGENT
                )
            }

            // Multiple failed cultures
            userProgressTracker.getFailureRate() > 0.5 -> {
                HelpSuggestion(
                    type = HelpType.BEST_PRACTICES,
                    title = "Improving Success Rates",
                    description = "Tips and techniques to reduce culture failures",
                    priority = Priority.MEDIUM
                )
            }

            else -> null
        }
    }

    private suspend fun personalizeHelpContent(
        baseHelp: List<HelpItem>,
        userContext: UserContext
    ): List<HelpItem> {

        return baseHelp.filter { helpItem ->
            when (userContext.experienceLevel) {
                ExperienceLevel.BEGINNER -> helpItem.difficulty <= DifficultyLevel.BEGINNER
                ExperienceLevel.INTERMEDIATE -> helpItem.difficulty <= DifficultyLevel.INTERMEDIATE
                ExperienceLevel.ADVANCED -> true
            }
        }.sortedBy {
            calculateRelevanceScore(it, userContext)
        }.reversed()
    }
}
```

## **Interactive Tutorial System**

```kotlin
/**
 * Step-by-step tutorial framework
 */
data class InteractiveTutorial(
    val id: String,
    val title: String,
    val description: String,
    val estimatedDuration: Duration,
    val prerequisites: List<String>,
    val steps: List<TutorialStep>,
    val category: TutorialCategory
)

data class TutorialStep(
    val id: String,
    val title: String,
    val instruction: String,
    val targetElementId: String?,
    val highlightType: HighlightType,
    val validation: StepValidation?,
    val helpContent: String?,
    val mediaUrl: String?
)

enum class TutorialCategory {
    GETTING_STARTED,
    CULTURE_CREATION,
    OBSERVATION_TRACKING,
    TROUBLESHOOTING,
    ADVANCED_TECHNIQUES
}
```

## **Help Content Structure**

### **Tooltip System**
```kotlin
@Composable
fun HelpTooltip(
    content: String,
    targetElement: @Composable () -> Unit,
    placement: TooltipPlacement = TooltipPlacement.BOTTOM,
    trigger: TooltipTrigger = TooltipTrigger.HOVER
) {
    var showTooltip by remember { mutableStateOf(false) }

    Box {
        targetElement()

        if (showTooltip) {
            TooltipPopup(
                content = content,
                placement = placement,
                onDismiss = { showTooltip = false }
            )
        }
    }
}

// Usage example
HelpTooltip(
    content = "Species name helps identify your culture and find relevant protocols",
    targetElement = {
        OutlinedTextField(
            value = speciesName,
            onValueChange = { speciesName = it },
            label = { Text("Species Name") }
        )
    }
)
```

### **Quick Help Cards**
```kotlin
@Composable
fun QuickHelpCard(
    title: String,
    description: String,
    icon: ImageVector,
    actionText: String? = null,
    onActionClick: (() -> Unit)? = null
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium
            )

            if (actionText != null && onActionClick != null) {
                Spacer(modifier = Modifier.height(12.dp))
                TextButton(onClick = onActionClick) {
                    Text(actionText)
                }
            }
        }
    }
}
```

---
