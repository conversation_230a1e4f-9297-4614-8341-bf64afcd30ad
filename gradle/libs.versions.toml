[versions]
# Core versions
kotlin = "1.9.10"
android-gradle = "8.1.2"
android-compile-sdk = "34"
android-target-sdk = "34"
android-min-sdk = "24"

# Jetpack Compose
compose-bom = "2023.10.01"
compose-compiler = "1.5.4"
activity-compose = "1.8.0"
navigation-compose = "2.7.4"

# Architecture Components
lifecycle = "2.7.0"
viewmodel-compose = "2.7.0"

# Dependency Injection
hilt = "2.48"
hilt-navigation-compose = "1.1.0"

# Database
room = "2.5.0"
sqlcipher = "4.5.4"

# Networking
retrofit = "2.9.0"
okhttp = "4.11.0"
kotlinx-serialization = "1.5.1"

# Image Loading
coil = "2.4.0"

# Background Tasks
work-manager = "2.8.1"

# Google Services
google-services = "4.4.0"
play-services-auth = "20.7.0"
play-services-drive = "17.0.0"
play-billing = "6.0.1"

# Firebase
firebase-bom = "32.5.0"

# Testing
junit = "4.13.2"
junit-ext = "1.1.5"
espresso = "3.5.1"
mockito = "5.6.0"
turbine = "1.0.0"

# Code Quality
detekt = "1.23.1"
ktlint = "0.50.0"

[libraries]
# Kotlin
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "kotlin" }
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinx-serialization" }

# Android Core
android-core-ktx = { group = "androidx.core", name = "core-ktx", version = "1.12.0" }
android-appcompat = { group = "androidx.appcompat", name = "appcompat", version = "1.6.1" }
android-material = { group = "com.google.android.material", name = "material", version = "1.10.0" }

# Jetpack Compose
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose-bom" }
compose-ui = { group = "androidx.compose.ui", name = "ui" }
compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
compose-material3 = { group = "androidx.compose.material3", name = "material3" }
compose-activity = { group = "androidx.activity", name = "activity-compose", version.ref = "activity-compose" }
compose-navigation = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation-compose" }

# Architecture Components
lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "viewmodel-compose" }

# Dependency Injection
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hilt-navigation-compose" }

# Database
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-testing = { group = "androidx.room", name = "room-testing", version.ref = "room" }
sqlcipher = { group = "net.zetetic", name = "android-database-sqlcipher", version.ref = "sqlcipher" }

# Networking
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-kotlinx-serialization = { group = "com.jakewharton.retrofit", name = "retrofit2-kotlinx-serialization-converter", version = "1.0.0" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }

# Image Loading
coil = { group = "io.coil-kt", name = "coil-compose", version.ref = "coil" }

# Background Tasks
work-manager = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work-manager" }

# Google Services
play-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "play-services-auth" }
play-services-drive = { group = "com.google.android.gms", name = "play-services-drive", version.ref = "play-services-drive" }
play-billing = { group = "com.android.billingclient", name = "billing-ktx", version.ref = "play-billing" }

# Firebase
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebase-bom" }
firebase-analytics = { group = "com.google.firebase", name = "firebase-analytics-ktx" }
firebase-crashlytics = { group = "com.google.firebase", name = "firebase-crashlytics-ktx" }
firebase-messaging = { group = "com.google.firebase", name = "firebase-messaging-ktx" }

# Testing
junit = { group = "junit", name = "junit", version.ref = "junit" }
junit-ext = { group = "androidx.test.ext", name = "junit", version.ref = "junit-ext" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso" }
mockito-core = { group = "org.mockito", name = "mockito-core", version.ref = "mockito" }
mockito-kotlin = { group = "org.mockito.kotlin", name = "mockito-kotlin", version = "5.1.0" }
turbine = { group = "app.cash.turbine", name = "turbine", version.ref = "turbine" }

[plugins]
android-application = { id = "com.android.application", version.ref = "android-gradle" }
android-library = { id = "com.android.library", version.ref = "android-gradle" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
google-services = { id = "com.google.gms.google-services", version.ref = "google-services" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }

[bundles]
compose = ["compose-ui", "compose-ui-tooling-preview", "compose-material3", "compose-activity", "compose-navigation"]
room = ["room-runtime", "room-ktx"]
networking = ["retrofit", "retrofit-kotlinx-serialization", "okhttp", "okhttp-logging"]
testing = ["junit", "junit-ext", "espresso-core", "mockito-core", "mockito-kotlin"]
