pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.name = "CultureStack"

// Main application module
include(":app")

// Core modules
include(":core:common")
include(":core:database")
include(":core:network")
include(":core:ui")

// Feature modules
include(":feature:cultures")
include(":feature:recipes")
include(":feature:observations")
include(":feature:auth")

// Shared modules
include(":shared:testing")
include(":shared:analytics")
