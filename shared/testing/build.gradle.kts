plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.hilt)
    alias(libs.plugins.kotlin.serialization)
}

android {
    namespace = "com.culturestack.shared.testing"
    
    buildFeatures {
        compose = true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.4"
    }
}

dependencies {
    // Core modules
    implementation(project(":core:common"))
    implementation(project(":core:database"))

    // Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlinx.serialization.json)

    // Android Core
    implementation(libs.android.core.ktx)

    // Database
    implementation(libs.bundles.room)
    implementation(libs.room.testing)

    // Jetpack Compose
    implementation(platform(libs.compose.bom))
    implementation(libs.compose.ui.test.junit4)

    // Dependency Injection
    implementation(libs.hilt.android)
    kapt(libs.hilt.compiler)

    // Testing
    implementation(libs.bundles.testing)
    implementation(libs.turbine)
}

kapt {
    correctErrorTypes = true
}
